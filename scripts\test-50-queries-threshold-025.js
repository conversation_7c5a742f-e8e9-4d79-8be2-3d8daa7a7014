/**
 * BATERÍA DE 50 CONSULTAS - THRESHOLD 0.25 vs 0.35
 * 
 * Prueba exhaustiva para validar el impacto de cambiar el threshold
 * de 0.35 (actual) a 0.25 (propuesto) en una variedad amplia de consultas.
 */

const fetch = require('node-fetch');

// Configuración
const API_BASE_URL = 'http://localhost:3000';
const API_KEY = 'demo-rag-key-2025';

console.log('🧪 BATERÍA DE 50 CONSULTAS - THRESHOLD 0.25 vs 0.35');
console.log('📅 Fecha:', new Date().toLocaleString());

// 50 consultas diversas organizadas por categoría
const TEST_QUERIES = [
  // CATEGORÍA 1: Términos genéricos (10 consultas)
  'apartamento',
  'casa',
  'depto',
  'apto',
  'propiedad',
  'inmueble',
  'vivienda',
  'guatemala',
  'venta',
  'alquiler',
  
  // CATEGORÍA 2: Ubicaciones específicas (10 consultas)
  'zona 14',
  'zona 15',
  'zona 10',
  'z14',
  'z15',
  'z10',
  'zona viva',
  'carretera salvador',
  'ces',
  'oakland',
  
  // CATEGORÍA 3: Sinónimos complejos (10 consultas)
  'arrendar apartamento',
  'conseguir casa',
  'invertir propiedad',
  'rentar depto',
  'adquirir apartamento',
  'buscar casa',
  'encontrar apartamento',
  'comprar inmueble',
  'alquilar vivienda',
  'rentar casa',
  
  // CATEGORÍA 4: Búsquedas específicas (10 consultas)
  'apartamento zona 14',
  'casa en venta zona 15',
  'alquiler zona 10',
  'comprar apartamento zona 14',
  'apartamento 3 habitaciones zona 14',
  'casa venta zona 15 piscina',
  'alquiler 2 habitaciones zona viva',
  'apartamento lujo zona 14 parqueo',
  'casa 3 habitaciones zona 15',
  'apartamento amueblado zona 10',
  
  // CATEGORÍA 5: Casos edge y combinaciones (10 consultas)
  'guatemala casa apartamento',
  'z14 apto 3 hab',
  'depto guatemala z14',
  'casa apartamento zona 14',
  'venta alquiler zona 15',
  'apartamento casa zona viva',
  'propiedad guatemala zona',
  'inmueble zona 14 15',
  'vivienda guatemala city',
  'depto apto apartamento'
];

async function testQuery(query, threshold) {
  try {
    const startTime = Date.now();
    
    const response = await fetch(`${API_BASE_URL}/api/v1/search`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY,
      },
      body: JSON.stringify({
        query,
        options: {
          limit: 10,
          scoreThreshold: threshold,
          includeResponse: false
        },
      }),
    });

    const responseTime = Date.now() - startTime;

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    
    return {
      query,
      threshold,
      success: data.resultsCount > 0,
      resultCount: data.resultsCount,
      topScore: data.properties?.[0]?.score || 0,
      avgScore: data.properties?.length > 0 ? 
        data.properties.reduce((sum, p) => sum + p.score, 0) / data.properties.length : 0,
      responseTime,
      properties: data.properties || []
    };
    
  } catch (error) {
    return {
      query,
      threshold,
      success: false,
      resultCount: 0,
      topScore: 0,
      avgScore: 0,
      responseTime: 0,
      properties: [],
      error: error.message
    };
  }
}

async function runComprehensiveTest() {
  console.log('\n📊 EJECUTANDO BATERÍA DE 50 CONSULTAS');
  console.log('=' .repeat(80));
  
  const results = {
    threshold_035: [],
    threshold_025: []
  };
  
  let queryIndex = 0;
  
  for (const query of TEST_QUERIES) {
    queryIndex++;
    console.log(`\n${queryIndex}/50 🔍 Probando: "${query}"`);
    console.log('-'.repeat(60));
    
    // Probar con threshold actual (0.35)
    console.log('   📊 Threshold 0.35...');
    const result035 = await testQuery(query, 0.35);
    results.threshold_035.push(result035);
    
    const status035 = result035.success ? '✅' : '❌';
    const score035 = result035.topScore > 0 ? ` (score: ${Math.round(result035.topScore * 100)}%)` : '';
    console.log(`      ${status035} ${result035.resultCount} resultados${score035} - ${result035.responseTime}ms`);
    
    // Pausa pequeña
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // Probar con threshold propuesto (0.25)
    console.log('   📊 Threshold 0.25...');
    const result025 = await testQuery(query, 0.25);
    results.threshold_025.push(result025);
    
    const status025 = result025.success ? '✅' : '❌';
    const score025 = result025.topScore > 0 ? ` (score: ${Math.round(result025.topScore * 100)}%)` : '';
    console.log(`      ${status025} ${result025.resultCount} resultados${score025} - ${result025.responseTime}ms`);
    
    // Mostrar comparación inmediata
    if (result035.success !== result025.success) {
      if (result025.success && !result035.success) {
        console.log('      🎯 MEJORA: Threshold 0.25 encuentra resultados que 0.35 no encuentra');
      } else if (result035.success && !result025.success) {
        console.log('      ⚠️ DEGRADACIÓN: Threshold 0.25 pierde resultados que 0.35 encuentra');
      }
    }
    
    // Pausa entre consultas
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  return results;
}

function analyzeResults(results) {
  console.log('\n📈 ANÁLISIS DETALLADO DE RESULTADOS');
  console.log('=' .repeat(80));
  
  const analysis = {};
  
  for (const [thresholdKey, queryResults] of Object.entries(results)) {
    const threshold = thresholdKey.replace('threshold_', '0.');
    
    const stats = {
      totalQueries: queryResults.length,
      successfulQueries: queryResults.filter(r => r.success).length,
      failedQueries: queryResults.filter(r => !r.success).length,
      avgTopScore: queryResults.reduce((sum, r) => sum + r.topScore, 0) / queryResults.length,
      avgResultCount: queryResults.reduce((sum, r) => sum + r.resultCount, 0) / queryResults.length,
      avgResponseTime: queryResults.reduce((sum, r) => sum + r.responseTime, 0) / queryResults.length,
      successRate: (queryResults.filter(r => r.success).length / queryResults.length) * 100
    };
    
    analysis[threshold] = stats;
    
    console.log(`\n🎯 THRESHOLD ${threshold}:`);
    console.log(`   Consultas exitosas: ${stats.successfulQueries}/${stats.totalQueries}`);
    console.log(`   Tasa de éxito: ${stats.successRate.toFixed(1)}%`);
    console.log(`   Score promedio: ${stats.avgTopScore.toFixed(3)}`);
    console.log(`   Resultados promedio: ${stats.avgResultCount.toFixed(1)}`);
    console.log(`   Tiempo promedio: ${Math.round(stats.avgResponseTime)}ms`);
  }
  
  return analysis;
}

function generateDetailedComparison(results, analysis) {
  console.log('\n🔍 COMPARACIÓN DETALLADA');
  console.log('=' .repeat(80));
  
  const threshold035 = results.threshold_035;
  const threshold025 = results.threshold_025;
  
  // Consultas que mejoran con 0.25
  const improvements = [];
  // Consultas que empeoran con 0.25
  const degradations = [];
  // Consultas que se mantienen igual
  const unchanged = [];
  
  for (let i = 0; i < threshold035.length; i++) {
    const result035 = threshold035[i];
    const result025 = threshold025[i];
    
    if (!result035.success && result025.success) {
      improvements.push({
        query: result035.query,
        improvement: `0 → ${result025.resultCount} resultados (score: ${Math.round(result025.topScore * 100)}%)`
      });
    } else if (result035.success && !result025.success) {
      degradations.push({
        query: result035.query,
        degradation: `${result035.resultCount} → 0 resultados`
      });
    } else if (result035.success && result025.success) {
      const scoreDiff = result025.topScore - result035.topScore;
      const countDiff = result025.resultCount - result035.resultCount;
      
      unchanged.push({
        query: result035.query,
        scoreDiff,
        countDiff,
        status: 'maintained'
      });
    }
  }
  
  console.log(`\n✅ MEJORAS CON THRESHOLD 0.25 (${improvements.length} consultas):`);
  improvements.forEach(item => {
    console.log(`   - "${item.query}": ${item.improvement}`);
  });
  
  if (degradations.length > 0) {
    console.log(`\n❌ DEGRADACIONES CON THRESHOLD 0.25 (${degradations.length} consultas):`);
    degradations.forEach(item => {
      console.log(`   - "${item.query}": ${item.degradation}`);
    });
  }
  
  console.log(`\n📊 CONSULTAS MANTENIDAS (${unchanged.length} consultas):`);
  const avgScoreDiff = unchanged.reduce((sum, item) => sum + item.scoreDiff, 0) / unchanged.length;
  const avgCountDiff = unchanged.reduce((sum, item) => sum + item.countDiff, 0) / unchanged.length;
  
  console.log(`   Score promedio: ${avgScoreDiff > 0 ? '+' : ''}${avgScoreDiff.toFixed(3)}`);
  console.log(`   Resultados promedio: ${avgCountDiff > 0 ? '+' : ''}${avgCountDiff.toFixed(1)}`);
  
  // Resumen final
  const stats035 = analysis['0.35'];
  const stats025 = analysis['0.25'];
  
  console.log('\n🎯 RESUMEN EJECUTIVO:');
  console.log(`   Tasa de éxito: ${stats035.successRate.toFixed(1)}% → ${stats025.successRate.toFixed(1)}% (${stats025.successRate > stats035.successRate ? '+' : ''}${(stats025.successRate - stats035.successRate).toFixed(1)}%)`);
  console.log(`   Score promedio: ${stats035.avgTopScore.toFixed(3)} → ${stats025.avgTopScore.toFixed(3)} (${stats025.avgTopScore > stats035.avgTopScore ? '+' : ''}${(stats025.avgTopScore - stats035.avgTopScore).toFixed(3)})`);
  console.log(`   Mejoras: ${improvements.length} consultas`);
  console.log(`   Degradaciones: ${degradations.length} consultas`);
  console.log(`   Mantenidas: ${unchanged.length} consultas`);
  
  // Recomendación
  const netImprovement = improvements.length - degradations.length;
  const successRateImprovement = stats025.successRate - stats035.successRate;
  
  if (netImprovement > 0 && successRateImprovement > 5) {
    console.log('\n✅ RECOMENDACIÓN: IMPLEMENTAR THRESHOLD 0.25');
    console.log('   Beneficios superan claramente a las degradaciones');
  } else if (netImprovement > 0 && successRateImprovement > 0) {
    console.log('\n⚖️ RECOMENDACIÓN: CONSIDERAR THRESHOLD 0.25');
    console.log('   Mejora moderada, evaluar trade-offs');
  } else {
    console.log('\n❌ RECOMENDACIÓN: MANTENER THRESHOLD 0.35');
    console.log('   Degradaciones superan a las mejoras');
  }
}

// Ejecutar prueba completa
async function main() {
  try {
    const results = await runComprehensiveTest();
    const analysis = analyzeResults(results);
    generateDetailedComparison(results, analysis);
    
  } catch (error) {
    console.error('❌ Error en prueba:', error);
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  main()
    .then(() => {
      console.log('\n🏁 BATERÍA DE 50 CONSULTAS COMPLETADA');
    })
    .catch(error => {
      console.error('❌ Error:', error);
      process.exit(1);
    });
}

module.exports = {
  testQuery,
  runComprehensiveTest,
  analyzeResults,
  generateDetailedComparison
};
