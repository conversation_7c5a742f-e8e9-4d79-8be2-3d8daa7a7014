export interface ValidationError {
  field: string;
  message: string;
}

export interface PropertyFormData {
  title: string;
  description: string;
  price: number;
  currency: string;
  type: string;
  status: string;
  address: string;
  bedrooms: number;
  bathrooms: number;
  area: number;
  builtYear: number;
  parking: number;
  amenities: string[];
  images: string[];
  featured: boolean;
}

export function validatePropertyForm(data: PropertyFormData, isDraft = false): ValidationError[] {
  const errors: ValidationError[] = [];

  // Validaciones obligatorias (siempre)
  if (!data.title.trim()) {
    errors.push({ field: 'title', message: 'El título es obligatorio' });
  } else if (data.title.length < 10) {
    errors.push({ field: 'title', message: 'El título debe tener al menos 10 caracteres' });
  } else if (data.title.length > 100) {
    errors.push({ field: 'title', message: 'El título no puede exceder 100 caracteres' });
  }

  if (!data.description.trim()) {
    errors.push({ field: 'description', message: 'La descripción es obligatoria' });
  } else if (data.description.length < 50) {
    errors.push({ field: 'description', message: 'La descripción debe tener al menos 50 caracteres' });
  } else if (data.description.length > 2000) {
    errors.push({ field: 'description', message: 'La descripción no puede exceder 2000 caracteres' });
  }

  if (!data.type) {
    errors.push({ field: 'type', message: 'El tipo de propiedad es obligatorio' });
  }

  // Validaciones para publicación (no para borradores)
  if (!isDraft) {
    // Precio
    if (!data.price || data.price <= 0) {
      errors.push({ field: 'price', message: 'El precio debe ser mayor a 0' });
    } else if (data.price > 999999999) {
      errors.push({ field: 'price', message: 'El precio es demasiado alto' });
    }

    // Área
    if (!data.area || data.area <= 0) {
      errors.push({ field: 'area', message: 'El área debe ser mayor a 0' });
    } else if (data.area > 100000) {
      errors.push({ field: 'area', message: 'El área parece demasiado grande' });
    }

    // Al menos una imagen
    if (!data.images || data.images.length === 0) {
      errors.push({ field: 'images', message: 'Debes agregar al menos una imagen' });
    }
  }

  // Validaciones de formato
  if (data.bedrooms < 0 || data.bedrooms > 50) {
    errors.push({ field: 'bedrooms', message: 'Número de habitaciones inválido' });
  }

  if (data.bathrooms < 0 || data.bathrooms > 50) {
    errors.push({ field: 'bathrooms', message: 'Número de baños inválido' });
  }

  if (data.parking < 0 || data.parking > 100) {
    errors.push({ field: 'parking', message: 'Número de estacionamientos inválido' });
  }

  if (data.builtYear && (data.builtYear < 1800 || data.builtYear > new Date().getFullYear() + 5)) {
    errors.push({ field: 'builtYear', message: 'Año de construcción inválido' });
  }

  // Validar URLs de imágenes
  data.images.forEach((url, index) => {
    if (url && !isValidUrl(url)) {
      errors.push({ field: 'images', message: `La imagen ${index + 1} tiene una URL inválida` });
    }
  });

  return errors;
}

function isValidUrl(string: string): boolean {
  try {
    new URL(string);
    return true;
  } catch {
    return false;
  }
}

export function getFieldError(errors: ValidationError[], field: string): string | undefined {
  return errors.find(error => error.field === field)?.message;
}

// Función utilitaria para validar y mostrar errores con toast
export function validateAndShowErrors(
  data: PropertyFormData,
  isDraft = false,
  showToast: (message: string) => void
): boolean {
  const errors = validatePropertyForm(data, isDraft);
  if (errors.length > 0) {
    errors.forEach(error => {
      showToast(error.message);
    });
    return false;
  }
  return true;
}