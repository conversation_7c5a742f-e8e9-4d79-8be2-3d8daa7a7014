import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { useState, useEffect, useCallback, useMemo } from "react";
import { Id } from "@/convex/_generated/dataModel";

interface SelectedLevels {
  level1?: Id<"locationData">;
  level2?: Id<"locationData">;
  level3?: Id<"locationData">;
  level4?: Id<"locationData">;
}

interface LocationCascadeReturn {
  selectedLevels: SelectedLevels;
  level1Options: any[] | undefined;
  level2Options: any[] | undefined;
  level3Options: any[] | undefined;
  level4Options: any[] | undefined;
  updateLevel: (level: number, value: Id<"locationData"> | null) => void;
  isLoading: boolean;
  clearAll: () => void;
  getSelectedNames: () => Record<string, string>;
}

export const useLocationCascade = (countryCode: string, initialValues?: SelectedLevels): LocationCascadeReturn => {
  const [selectedLevels, setSelectedLevels] = useState<SelectedLevels>(initialValues || {});

  // Efecto para actualizar selectedLevels cuando cambien los valores iniciales
  useEffect(() => {
    if (initialValues && Object.keys(initialValues).length > 0) {
      setSelectedLevels(initialValues);
    }
  }, [initialValues]);

  // Cargar departamentos (nivel 1) - siempre se cargan
  const level1Options = useQuery(api.locationData.getRootLevels, {
    countryCode,
    level: 1
  });

  // Cargar municipios (nivel 2) cuando se selecciona un departamento
  const level2Options = useQuery(
    api.locationData.getChildrenByParent,
    selectedLevels.level1 ? {
      parentId: selectedLevels.level1,
      countryCode
    } : "skip"
  );

  // Cargar zonas (nivel 3) cuando se selecciona un municipio
  const level3Options = useQuery(
    api.locationData.getChildrenByParent,
    selectedLevels.level2 ? {
      parentId: selectedLevels.level2,
      countryCode
    } : "skip"
  );

  // Cargar colonias (nivel 4) cuando se selecciona una zona
  const level4Options = useQuery(
    api.locationData.getChildrenByParent,
    selectedLevels.level3 ? {
      parentId: selectedLevels.level3,
      countryCode
    } : "skip"
  );

  // Función para actualizar selección y limpiar niveles inferiores
  const updateLevel = useCallback((level: number, value: Id<"locationData"> | null) => {
    setSelectedLevels(prev => {
      const newState = { ...prev };

      // Actualizar nivel seleccionado
      if (value) {
        newState[`level${level}` as keyof SelectedLevels] = value;
      } else {
        delete newState[`level${level}` as keyof SelectedLevels];
      }

      // Limpiar niveles inferiores
      for (let i = level + 1; i <= 4; i++) {
        delete newState[`level${i}` as keyof SelectedLevels];
      }

      return newState;
    });
  }, []);

  // Función para limpiar todas las selecciones
  const clearAll = useCallback(() => {
    setSelectedLevels({});
  }, []);

  // Función para obtener nombres de las selecciones actuales
  const getSelectedNames = useCallback(() => {
    const names: Record<string, string> = {};
    
    if (selectedLevels.level1 && level1Options) {
      const level1Item = level1Options.find(item => item._id === selectedLevels.level1);
      if (level1Item) names.level1 = level1Item.name;
    }
    
    if (selectedLevels.level2 && level2Options) {
      const level2Item = level2Options.find(item => item._id === selectedLevels.level2);
      if (level2Item) names.level2 = level2Item.name;
    }
    
    if (selectedLevels.level3 && level3Options) {
      const level3Item = level3Options.find(item => item._id === selectedLevels.level3);
      if (level3Item) names.level3 = level3Item.name;
    }
    
    if (selectedLevels.level4 && level4Options) {
      const level4Item = level4Options.find(item => item._id === selectedLevels.level4);
      if (level4Item) names.level4 = level4Item.name;
    }
    
    return names;
  }, [selectedLevels, level1Options, level2Options, level3Options, level4Options]);

  return {
    selectedLevels,
    level1Options,
    level2Options,
    level3Options,
    level4Options,
    updateLevel,
    isLoading: level1Options === undefined,
    clearAll,
    getSelectedNames
  };
};

// Hook adicional para inicializar con valores existentes
export const useLocationCascadeWithInitial = (
  countryCode: string, 
  initialLocation?: any
): LocationCascadeReturn => {
  const cascade = useLocationCascade(countryCode);
  const [initialized, setInitialized] = useState(false);

  // Inicializar con valores existentes si se proporcionan
  useEffect(() => {
    if (initialLocation && !initialized && cascade.level1Options) {
      // Buscar coincidencias en los datos cargados
      const level1Match = cascade.level1Options.find(
        item => item.name === initialLocation.level1?.name
      );
      
      if (level1Match) {
        cascade.updateLevel(1, level1Match._id);
        setInitialized(true);
      }
    }
  }, [initialLocation, initialized, cascade.level1Options, cascade.updateLevel]);

  return cascade;
};

// Función utilitaria para convertir selecciones a formato de ubicación
export const convertSelectionsToLocation = (
  selectedLevels: SelectedLevels,
  countryCode: string,
  countryName: string,
  levelOptions: {
    level1Options?: any[];
    level2Options?: any[];
    level3Options?: any[];
    level4Options?: any[];
  }
) => {
  const location: any = {
    country: {
      code: countryCode,
      name: countryName
    }
  };

  // Agregar nivel 1 (departamento)
  if (selectedLevels.level1 && levelOptions.level1Options) {
    const level1Item = levelOptions.level1Options.find(
      item => item._id === selectedLevels.level1
    );
    if (level1Item) {
      location.level1 = {
        code: level1Item.code,
        name: level1Item.name,
        type: "departamento"
      };
    }
  }

  // Agregar nivel 2 (municipio)
  if (selectedLevels.level2 && levelOptions.level2Options) {
    const level2Item = levelOptions.level2Options.find(
      item => item._id === selectedLevels.level2
    );
    if (level2Item) {
      location.level2 = {
        name: level2Item.name,
        type: "municipio"
      };
    }
  }

  // Agregar nivel 3 (zona)
  if (selectedLevels.level3 && levelOptions.level3Options) {
    const level3Item = levelOptions.level3Options.find(
      item => item._id === selectedLevels.level3
    );
    if (level3Item) {
      location.level3 = {
        name: level3Item.name,
        type: "zona"
      };
    }
  }

  // Agregar nivel 4 (colonia)
  if (selectedLevels.level4 && levelOptions.level4Options) {
    const level4Item = levelOptions.level4Options.find(
      item => item._id === selectedLevels.level4
    );
    if (level4Item) {
      location.level4 = {
        name: level4Item.name,
        type: "colonia"
      };
    }
  }

  return location;
};
