"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Check, Crown, X, Zap } from "lucide-react";
import { STRIPE_CONFIG, getPlanInfo } from "@/lib/stripe-config";
import { toast } from "sonner";

interface UpgradeModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentPlan?: "free" | "pro" | "premium";
  reason?: string;
}

export function UpgradeModal({ isOpen, onClose, currentPlan = "free", reason }: UpgradeModalProps) {
  const [loading, setLoading] = useState(false);

  const handleUpgrade = async (plan: "pro" | "premium") => {
    try {
      setLoading(true);
      
      const response = await fetch("/api/stripe/checkout", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ plan }),
      });

      const data = await response.json();

      if (data.checkoutUrl) {
        // Redirigir a Stripe Checkout
        window.location.href = data.checkoutUrl;
      } else {
        toast.error("Error al crear el checkout");
      }
    } catch (error) {
      console.error("Error:", error);
      toast.error("Error al procesar el upgrade");
    } finally {
      setLoading(false);
    }
  };

  // Obtener información de los planes
  const freePlan = getPlanInfo("free");
  const premiumPlan = getPlanInfo("premium");

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="text-2xl font-bold">Actualizar Plan</DialogTitle>
            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
          {reason && (
            <p className="text-sm text-gray-600 mt-2 p-3 bg-blue-50 rounded-lg border-l-4 border-blue-400">
              {reason}
            </p>
          )}
        </DialogHeader>

        <div className="grid gap-6 md:grid-cols-2">
          {/* Plan Gratuito */}
          <Card className={currentPlan === "free" ? "ring-2 ring-blue-500" : "opacity-75"}>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                {freePlan.name}
                {currentPlan === "free" && (
                  <Badge variant="default">Actual</Badge>
                )}
              </CardTitle>
              <div className="text-2xl font-bold">${freePlan.price}<span className="text-sm font-normal">/{freePlan.interval}</span></div>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm">
                {freePlan.features.map((feature: any, index: any) => (
                  <li key={index} className="flex items-center gap-2">
                    <Check className="h-4 w-4 text-green-500" />
                    {feature}
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>



          {/* Plan Premium */}
          <Card className={currentPlan === "premium" ? "ring-2 ring-blue-500" : ""}>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                {premiumPlan.name}
                {currentPlan === "premium" ? (
                  <Badge variant="default">Actual</Badge>
                ) : (
                  <Badge variant="secondary" className="bg-purple-100 text-purple-800">Premium</Badge>
                )}
              </CardTitle>
              <div className="text-2xl font-bold">
                ${premiumPlan.price}
                <span className="text-sm font-normal">/{premiumPlan.interval}</span>
              </div>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm mb-4">
                {premiumPlan.features.map((feature: any, index: any) => (
                  <li key={index} className="flex items-center gap-2">
                    <Check className="h-4 w-4 text-green-500" />
                    {feature}
                  </li>
                ))}
              </ul>
              {currentPlan !== "premium" && (
                <Button 
                  className="w-full bg-purple-600 hover:bg-purple-700" 
                  onClick={() => handleUpgrade("premium")}
                  disabled={loading}
                >
                  {loading ? "Procesando..." : "Actualizar a Premium"}
                  <Crown className="ml-2 h-4 w-4" />
                </Button>
              )}
            </CardContent>
          </Card>
        </div>

        <div className="mt-6 text-center text-sm text-muted-foreground">
          <p>✅ Cancelación en cualquier momento</p>
          <p>✅ Soporte 24/7 para planes pagados</p>
          <p>✅ Facturación segura con Stripe</p>
        </div>
      </DialogContent>
    </Dialog>
  );
} 