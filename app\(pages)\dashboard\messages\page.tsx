"use client";

import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Mail,
  MailOpen,
  Archive,
  Search,
  Filter,
  Eye,
  MessageSquare,
  Calendar,
  TrendingUp,
  CreditCard,
  Building2,
  Lock,
  Reply,
  Send,
  ChevronDown,
  ChevronUp
} from "lucide-react";
import Image from "next/image";
import { formatDistanceToNow } from "date-fns";
import { es } from "date-fns/locale";
import { toast } from "sonner";
import { CreditsDisplay } from "@/components/ui/credits-display";
import { useUser } from "@clerk/clerk-react";
import Link from "next/link";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CollapsibleItem } from "@/components/ui/collapsible-card";
import { ManagedList } from "@/components/ui/list-controls";

export default function MessagesPage() {
  const [activeTab, setActiveTab] = useState<"inbox" | "sent">("inbox");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedMessage, setSelectedMessage] = useState<string | null>(null);

  // Queries
  const inboxMessages = useQuery(api.messages.getInboxMessages, {});
  const sentMessages = useQuery(api.messages.getSentMessages, {});
  const messageStats = useQuery(api.messages.getMessageStats, {});
  const subscription = useQuery(api.subscriptions.getUserSubscription);



  // Mutations
  const markAsRead = useMutation(api.messages.markAsRead);
  const archiveMessage = useMutation(api.messages.archiveMessage);
  const replyToMessage = useMutation(api.messages.replyToMessage);
  const consumeCreditsForAction = useMutation(api.subscriptions.consumeCreditsForAction);

  const handleMarkAsRead = async (messageId: string, silent = false) => {
    try {
      await markAsRead({ messageId: messageId as any });
      if (!silent) {
        toast.success("Mensaje marcado como leído");
      }
    } catch (error) {
      if (!silent) {
        toast.error("Error al marcar como leído");
      }
    }
  };

  const handleArchive = async (messageId: string) => {
    try {
      await archiveMessage({ messageId: messageId as any });
      toast.success("Mensaje archivado");
    } catch (error) {
      toast.error("Error al archivar mensaje");
    }
  };

  const handleReply = async (messageId: string, replyText: string) => {
    try {
      const result = await replyToMessage({
        originalMessageId: messageId as any,
        replyMessage: replyText
      });
      toast.success("Respuesta enviada" + (result.emailSent ? " y notificación por email enviada" : ""));
    } catch (error) {
      toast.error("Error al enviar respuesta");
    }
  };

  const handleReplyToMessage = async (messageId: string, replyText: string) => {
    return await handleReply(messageId, replyText);
  };

  const getLeadTypeBadge = (leadType: string) => {
    const types = {
      inquiry: { label: "Consulta", variant: "default" as const, color: "bg-blue-100 text-blue-800" },
      viewing: { label: "Visita", variant: "secondary" as const, color: "bg-green-100 text-green-800" },
      offer: { label: "Oferta", variant: "destructive" as const, color: "bg-yellow-100 text-yellow-800" },
      negotiation: { label: "Negociación", variant: "outline" as const, color: "bg-purple-100 text-purple-800" },
    };
    return types[leadType as keyof typeof types] || types.inquiry;
  };

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('es-ES', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
    }).format(price);
  };

  const filteredInboxMessages = inboxMessages?.filter((message: any) =>
    message.senderName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    message.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
    message.property?.title.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const filteredSentMessages = sentMessages?.filter((message: any) =>
    message.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
    message.property?.title.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  // Componente para mensaje con nuevo sistema "pago por respuesta"
  function ProtectedMessageCard({ message }: { message: any }) {
    // Verificar si ya se consumieron créditos para este mensaje
    const creditsConsumed = useQuery(api.subscriptions.checkCreditsConsumed, {
      action: "respond_to_message",
      resourceId: message._id,
    });

    // Si el mensaje ya fue respondido o ya se consumieron créditos, mostrar información completa
    if (message.hasBeenResponded || creditsConsumed) {
      return <FullMessageCard message={message} />;
    }

    // Mostrar vista limitada hasta que se responda o se consuman créditos
    return <LimitedMessageCard message={message} />;
  }

  // Componente para vista limitada del mensaje (antes de responder)
  function LimitedMessageCard({ message }: { message: any }) {
    // Todos los hooks deben estar al inicio del componente
    const checkCreditsConsumed = useQuery(api.subscriptions.checkCreditsConsumed, {
      action: "respond_to_message",
      resourceId: message._id,
    });

    const canRespond = useQuery(api.subscriptions.canPerformAction, {
      action: "respond_to_message",
      resourceId: message._id,
    });

    const subscription = useQuery(api.subscriptions.getUserSubscription);

    const [isReplying, setIsReplying] = useState(false);
    const [replyText, setReplyText] = useState('');
    const [creditsConsumed, setCreditsConsumed] = useState(false);

    // Si ya se consumieron créditos, mostrar información completa
    if (checkCreditsConsumed || creditsConsumed) {
      return <FullMessageCard message={message} />;
    }

    // Función para consumir créditos y mostrar información completa
    const handleShowFullMessage = async () => {
      try {
        // Consumir créditos para ver la información completa
        await consumeCreditsForAction({
          action: "respond_to_message",
          resourceId: message._id,
        });

        setCreditsConsumed(true);
        setIsReplying(true);
        toast.success("Información desbloqueada. Ahora puedes responder al mensaje.");
      } catch (error) {
        console.error('Error consuming credits:', error);
        toast.error("Error al desbloquear información");
      }
    };

    const handleReply = async () => {
      if (!replyText.trim()) {
        toast.error("Por favor escribe una respuesta");
        return;
      }

      try {
        await handleReplyToMessage(message._id, replyText);
        setIsReplying(false);
        setReplyText('');
        toast.success("Respuesta enviada exitosamente.");
      } catch (error) {
        console.error('Error replying:', error);
        toast.error("Error al enviar respuesta");
      }
    };

    // Determinar categoría del mensaje
    const getMessageCategory = (leadType: string) => {
      switch (leadType) {
        case 'inquiry': return 'Consulta General';
        case 'viewing': return 'Solicitud de Visita';
        case 'offer': return 'Oferta de Compra';
        case 'negotiation': return 'Consulta de Negociación';
        default: return 'Consulta sobre Propiedad';
      }
    };

    return (
      <Card className={`hover:shadow-md transition-shadow border-l-4 border-l-yellow-400`}>
        <CardContent className="p-6">
          <div className="space-y-4">
            {/* Vista previa limitada - Solo información básica */}
            <div className="flex items-start gap-4">
              {/* Información de la propiedad */}
              <div className="w-20 h-20 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0">
                {message.property?.images?.[0] ? (
                  <Image
                    src={message.property.images[0]}
                    alt={message.property.title}
                    width={80}
                    height={80}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <Building2 className="h-8 w-8 text-gray-400" />
                  </div>
                )}
              </div>

              {/* Contenido limitado */}
              <div className="flex-1">
                <div className="flex items-start justify-between mb-2">
                  <div>
                    <h3 className="font-semibold text-gray-900">{getMessageCategory(message.leadType)}</h3>
                    <p className="text-sm text-gray-600">
                      De: <span className="font-medium text-gray-400">••••••••</span> (Oculto hasta responder)
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={getLeadTypeBadge(message.leadType).color}>
                      {getLeadTypeBadge(message.leadType).label}
                    </Badge>
                    {message.status === 'unread' && (
                      <Badge variant="default" className="bg-blue-600">
                        Nuevo
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Información de la propiedad */}
                {message.property && (
                  <div className="bg-gray-50 rounded-lg p-3 mb-3">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium text-gray-900">{message.property.title}</h4>
                      <span className="text-sm font-semibold text-blue-600">
                        {formatPrice(message.property.price, message.property.currency)}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600">{message.property.city} • {message.property.type}</p>
                  </div>
                )}

                {/* Información oculta */}
                <div className="bg-gray-100 rounded-lg p-4 mb-3">
                  <div className="flex items-center gap-2 text-gray-500 mb-2">
                    <Lock className="w-4 h-4" />
                    <span className="text-sm font-medium">Información oculta - Desbloquear para ver detalles</span>
                  </div>
                  <div className="space-y-1 text-sm text-gray-400">
                    <p>• Nombre completo del remitente</p>
                    <p>• Email de contacto</p>
                    <p>• Teléfono (si proporcionado)</p>
                    <p>• Mensaje completo</p>
                  </div>
                </div>

                {/* Formulario de respuesta */}
                {!isReplying ? (
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-600">
                      {canRespond?.isPremiumAccess ? (
                        <span>Ver información <strong>gratis</strong> con tu plan premium</span>
                      ) : (
                        <span>Ver información completa cuesta <strong>{canRespond?.requiredCredits || 2} créditos</strong></span>
                      )}
                    </div>
                    <Button
                      onClick={handleShowFullMessage}
                      className="bg-blue-600 hover:bg-blue-700"
                      disabled={canRespond && !canRespond.canPerform}
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      Ver Información
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-3">
                    <Textarea
                      placeholder="Escribe tu respuesta aquí..."
                      value={replyText}
                      onChange={(e) => setReplyText(e.target.value)}
                      className="min-h-[100px]"
                    />
                    <div className="flex gap-2">
                      <Button
                        onClick={handleReply}
                        className="bg-green-600 hover:bg-green-700"
                        disabled={!replyText.trim()}
                      >
                        <Send className="w-4 h-4 mr-2" />
                        Enviar Respuesta
                      </Button>
                      <Button
                        onClick={() => setIsReplying(false)}
                        variant="outline"
                      >
                        Cancelar
                      </Button>
                    </div>
                  </div>
                )}

                {/* Mensaje de créditos insuficientes */}
                {canRespond && !canRespond.canPerform && (
                  <Alert className="mt-3">
                    <CreditCard className="h-4 w-4" />
                    <AlertDescription>
                      <div className="flex items-center justify-between">
                        <span>
                          Necesitas <strong>{canRespond.requiredCredits || 2} créditos</strong> para responder.
                          Tienes <strong>{subscription ? (subscription.credits - subscription.creditsUsed) : (canRespond.availableCredits || 0)}</strong> disponibles.
                        </span>
                        <Link href="/dashboard/finance">
                          <Button size="sm" className="ml-4">
                            Comprar Créditos
                          </Button>
                        </Link>
                      </div>
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Componente para mostrar el mensaje completo (información desbloqueada)
  function FullMessageCard({ message }: { message: any }) {
    const [showReplyForm, setShowReplyForm] = useState(false);
    const [replyText, setReplyText] = useState("");
    const [isReplying, setIsReplying] = useState(false);

    const handleSubmitReply = async () => {
      if (!replyText.trim()) return;

      setIsReplying(true);
      try {
        await handleReply(message._id, replyText);
        setReplyText("");
        setShowReplyForm(false);
      } catch (error) {
        console.error('Error sending reply:', error);
      } finally {
        setIsReplying(false);
      }
    };

    return (
      <Card className={`hover:shadow-md transition-shadow border-l-4 border-l-green-400`}>
        <CardContent className="p-6">
          <div className="flex items-start gap-4">
            {/* Información de la propiedad */}
            <div className="w-20 h-20 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0">
              {message.property?.images?.[0] ? (
                <Image
                  src={message.property.images[0]}
                  alt={message.property.title}
                  width={80}
                  height={80}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <Building2 className="h-8 w-8 text-gray-400" />
                </div>
              )}
            </div>

            {/* Contenido del mensaje */}
            <div className="flex-1">
              <div className="flex items-start justify-between mb-2">
                <div>
                  <h3 className="font-semibold text-gray-900">{message.subject}</h3>
                  <p className="text-sm text-gray-600">
                    De: <span className="font-medium">{message.senderName}</span> ({message.senderEmail})
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Badge className={getLeadTypeBadge(message.leadType).color}>
                    {getLeadTypeBadge(message.leadType).label}
                  </Badge>
                  <Badge className="bg-green-100 text-green-800">
                    Leído
                  </Badge>
                </div>
              </div>

              {/* Información de la propiedad */}
              {message.property && (
                <div className="bg-gray-50 rounded-lg p-3 mb-3">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-gray-900">{message.property.title}</h4>
                    <span className="text-sm font-semibold text-blue-600">
                      {formatPrice(message.property.price, message.property.currency)}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600">{message.property.city} • {message.property.type}</p>
                </div>
              )}

              {/* Mensaje completo */}
              <p className="text-gray-700 mb-3">{message.message}</p>

              {/* Acciones y timestamp */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2 text-sm text-gray-500">
                  <Calendar className="h-4 w-4" />
                  {formatDistanceToNow(new Date(message.createdAt), { 
                    addSuffix: true, 
                    locale: es 
                  })}
                  {message.creditsCharged && (
                    <>
                      <span className="mx-2">•</span>
                      <CreditCard className="h-4 w-4" />
                      {message.creditsCharged} créditos
                    </>
                  )}
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleArchive(message._id)}
                  >
                    <Archive className="h-4 w-4 mr-1" />
                    Archivar
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowReplyForm(!showReplyForm)}
                    className="border-green-200 text-green-700 hover:bg-green-50"
                  >
                    <Reply className="h-4 w-4 mr-1" />
                    Responder (sin costo)
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Formulario de respuesta */}
          {showReplyForm && (
            <div className="mt-4 border-t pt-4">
              <h4 className="font-medium text-gray-900 mb-3">Responder a {message.senderName}</h4>
              <div className="space-y-3">
                <textarea
                  value={replyText}
                  onChange={(e) => setReplyText(e.target.value)}
                  placeholder="Escribe tu respuesta..."
                  className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows={4}
                />
                <div className="flex items-center gap-2">
                  <Button
                    onClick={handleSubmitReply}
                    disabled={!replyText.trim() || isReplying}
                    size="sm"
                  >
                    {isReplying ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Enviando...
                      </>
                    ) : (
                      <>
                        <Send className="h-4 w-4 mr-1" />
                        Enviar Respuesta
                      </>
                    )}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setShowReplyForm(false);
                      setReplyText("");
                    }}
                  >
                    Cancelar
                  </Button>
                </div>
                <p className="text-xs text-gray-500">
                  💡 Se enviará una notificación por email a {message.senderEmail}
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  // Componente para lista de mensajes con elementos individuales colapsables
  function CollapsibleMessagesList({
    messages,
    title,
    icon: Icon
  }: {
    messages: any[],
    title: string,
    icon: any
  }) {
    return (
      <ManagedList
        items={messages}
        getItemId={(message) => message._id}
        title={title}
        icon={Icon}
        defaultExpanded={false}
        showControls={true}
        controlsVariant="compact"
        renderItem={(message, isExpanded, onToggle) => (
          <CollapsibleItem
            key={message._id}
            id={message._id}
            data={message}
            type="message"
            defaultExpanded={isExpanded}
            onToggle={(id, expanded) => {
              // Primero ejecutar onToggle para actualizar el estado visual
              onToggle();

              // Luego marcar como leído si es necesario (silenciosamente)
              if (expanded && message.status === 'unread') {
                // Usar setTimeout para evitar conflictos con el estado de expansión
                setTimeout(() => {
                  handleMarkAsRead(message._id, true); // true = silent
                }, 150); // Aumentar el delay para mejor UX
              }
            }}
          >
            <ProtectedMessageCard message={message} />
          </CollapsibleItem>
        )}
      />
    );
  }

  return (
    <div className="flex flex-col gap-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-semibold tracking-tight">Mensajes</h1>
          <p className="text-muted-foreground mt-2">
            Gestiona tus leads y consultas de propiedades
          </p>
        </div>

        <CreditsDisplay variant="compact" />
      </div>



      {/* Estadísticas */}
      {messageStats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Mail className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Nuevos</p>
                  <p className="text-2xl font-bold">{messageStats.unreadCount}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <MessageSquare className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Total Leads</p>
                  <p className="text-2xl font-bold">{messageStats.totalCount}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <CreditCard className="h-5 w-5 text-yellow-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Créditos Disponibles</p>
                  <p className="text-2xl font-bold">
                    {subscription ? (subscription.credits - subscription.creditsUsed) : 0}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <TrendingUp className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Visitas</p>
                  <p className="text-2xl font-bold">{messageStats.leadTypeStats.viewing || 0}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Buscador */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Buscar mensajes..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Button variant="outline" size="icon">
          <Filter className="h-4 w-4" />
        </Button>
      </div>

      {/* Tabs de Mensajes */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as "inbox" | "sent")}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="inbox" className="flex items-center gap-2">
            <Mail className="h-4 w-4" />
            Recibidos ({messageStats?.unreadCount || 0})
          </TabsTrigger>
          <TabsTrigger value="sent" className="flex items-center gap-2">
            <MailOpen className="h-4 w-4" />
            Enviados
          </TabsTrigger>
        </TabsList>

        <TabsContent value="inbox" className="space-y-4">
          {filteredInboxMessages.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <Mail className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No hay mensajes</h3>
                <p className="text-gray-500">Cuando recibas consultas sobre tus propiedades aparecerán aquí.</p>
              </CardContent>
            </Card>
          ) : (
            <CollapsibleMessagesList
              messages={filteredInboxMessages}
              title="Mensajes Recibidos"
              icon={Mail}
            />
          )}
        </TabsContent>

        <TabsContent value="sent" className="space-y-4">
          {filteredSentMessages.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <MailOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No hay mensajes enviados</h3>
                <p className="text-gray-500">Los mensajes que envíes aparecerán aquí.</p>
              </CardContent>
            </Card>
          ) : (
            filteredSentMessages.map((message: any) => (
              <Card key={message._id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    {/* Información de la propiedad */}
                    <div className="w-20 h-20 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0">
                      {message.property?.images?.[0] ? (
                        <Image
                          src={message.property.images[0]}
                          alt={message.property.title}
                          width={80}
                          height={80}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <Building2 className="h-8 w-8 text-gray-400" />
                        </div>
                      )}
                    </div>

                    {/* Contenido del mensaje */}
                    <div className="flex-1">
                      <div className="flex items-start justify-between mb-2">
                        <div>
                          <h3 className="font-semibold text-gray-900">{message.subject}</h3>
                          <p className="text-sm text-gray-600">
                            Enviado como: <span className="font-medium">{message.senderName}</span>
                          </p>
                        </div>
                        <Badge className={getLeadTypeBadge(message.leadType).color}>
                          {getLeadTypeBadge(message.leadType).label}
                        </Badge>
                      </div>

                      {/* Información de la propiedad */}
                      {message.property && (
                        <div className="bg-gray-50 rounded-lg p-3 mb-3">
                          <h4 className="font-medium text-gray-900">{message.property.title}</h4>
                          <p className="text-sm text-gray-600">{message.property.city}</p>
                        </div>
                      )}

                      {/* Mensaje */}
                      <p className="text-gray-700 mb-3 line-clamp-2">{message.message}</p>

                      {/* Timestamp */}
                      <div className="flex items-center gap-2 text-sm text-gray-500">
                        <Calendar className="h-4 w-4" />
                        {formatDistanceToNow(new Date(message.createdAt), { 
                          addSuffix: true, 
                          locale: es 
                        })}
                        <span className="mx-2">•</span>
                        <span className="capitalize">{message.status}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
} 