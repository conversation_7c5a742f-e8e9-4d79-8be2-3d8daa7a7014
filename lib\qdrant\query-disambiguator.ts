/**
 * Query Disambiguator - Desambiguador de Consultas
 * 
 * Sistema que maneja consultas ambiguas generando múltiples interpretaciones
 * posibles y rankeándolas por probabilidad de intención.
 * 
 * Día 6 - Análisis de Contexto Avanzado
 */

import OpenAI from 'openai';
import { ContextAnalysis } from './context-analyzer';

// Tipos para desambiguación
export interface QueryInterpretation {
  interpretation: string;
  probability: number;
  reasoning: string;
  suggestedFilters: {
    location?: string[];
    propertyType?: string[];
    priceRange?: { min: number; max: number };
    amenities?: string[];
    bedrooms?: number;
    bathrooms?: number;
  };
  confidence: number;
}

export interface DisambiguationResult {
  originalQuery: string;
  isAmbiguous: boolean;
  ambiguityLevel: 'low' | 'medium' | 'high';
  interpretations: QueryInterpretation[];
  recommendedInterpretation: QueryInterpretation;
  fallbackQuery: string;
  processingTime: number;
}

/**
 * Desambiguador de consultas inmobiliarias
 */
export class QueryDisambiguator {
  private openai: OpenAI;
  private model: string;

  // Patrones de ambigüedad comunes en Guatemala
  private readonly AMBIGUITY_PATTERNS = [
    // Ubicaciones ambiguas
    {
      pattern: /\b(centro|downtown)\b/i,
      ambiguity: 'location',
      interpretations: ['Zona 1 (Centro Histórico)', 'Zona 4 (Centro Comercial)', 'Zona 9 (Centro Financiero)']
    },
    {
      pattern: /\b(sur|south)\b/i,
      ambiguity: 'location',
      interpretations: ['Zona 12', 'Zona 13', 'Villa Nueva', 'Amatitlán']
    },
    {
      pattern: /\b(norte|north)\b/i,
      ambiguity: 'location',
      interpretations: ['Zona 17', 'Zona 18', 'Mixco']
    },

    // Tipos de propiedad ambiguos
    {
      pattern: /\b(propiedad|property)\b/i,
      ambiguity: 'propertyType',
      interpretations: ['apartamento', 'casa', 'oficina', 'local comercial']
    },
    {
      pattern: /\b(inmueble)\b/i,
      ambiguity: 'propertyType',
      interpretations: ['apartamento', 'casa', 'terreno', 'oficina']
    },

    // Presupuesto ambiguo
    {
      pattern: /\b(económico|barato|accesible)\b/i,
      ambiguity: 'budget',
      interpretations: ['<Q300k', 'Q300k-Q500k', 'Q500k-Q800k']
    },
    {
      pattern: /\b(caro|exclusivo|premium)\b/i,
      ambiguity: 'budget',
      interpretations: ['Q2M-Q5M', 'Q5M-Q10M', '>Q10M']
    },

    // Tamaño ambiguo
    {
      pattern: /\b(grande|amplio|espacioso)\b/i,
      ambiguity: 'size',
      interpretations: ['3+ habitaciones', '200+ m2', '4+ habitaciones']
    },
    {
      pattern: /\b(pequeño|compacto|acogedor)\b/i,
      ambiguity: 'size',
      interpretations: ['1 habitación', '2 habitaciones', '<100 m2']
    }
  ];

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
    this.model = process.env.AI_MODEL_DISAMBIGUATION || 'gpt-4o-mini';
  }

  /**
   * Desambigua una consulta generando múltiples interpretaciones
   */
  async disambiguateQuery(
    query: string, 
    contextAnalysis?: ContextAnalysis
  ): Promise<DisambiguationResult> {
    const startTime = Date.now();

    try {
      console.log(`🔀 Desambiguando consulta: "${query}"`);

      // 1. Detectar nivel de ambigüedad
      const ambiguityLevel = this.detectAmbiguityLevel(query);
      const isAmbiguous = ambiguityLevel !== 'low';

      if (!isAmbiguous) {
        // Si no es ambigua, retornar resultado simple
        return this.createSimpleResult(query, Date.now() - startTime);
      }

      // 2. Generar interpretaciones múltiples
      const interpretations = await this.generateInterpretations(query, contextAnalysis);

      // 3. Rankear interpretaciones por probabilidad
      const rankedInterpretations = this.rankInterpretations(interpretations, contextAnalysis);

      // 4. Seleccionar interpretación recomendada
      const recommendedInterpretation = rankedInterpretations[0];

      // 5. Generar consulta de fallback
      const fallbackQuery = this.generateFallbackQuery(query, rankedInterpretations);

      const result: DisambiguationResult = {
        originalQuery: query,
        isAmbiguous,
        ambiguityLevel,
        interpretations: rankedInterpretations,
        recommendedInterpretation,
        fallbackQuery,
        processingTime: Date.now() - startTime,
      };

      console.log(`✅ Desambiguación completada: ${rankedInterpretations.length} interpretaciones (${result.processingTime}ms)`);
      return result;

    } catch (error) {
      console.error('Error en desambiguación:', error);
      return this.createFallbackResult(query, Date.now() - startTime);
    }
  }

  /**
   * Detecta el nivel de ambigüedad de una consulta
   */
  private detectAmbiguityLevel(query: string): 'low' | 'medium' | 'high' {
    let ambiguityScore = 0;
    const queryLower = query.toLowerCase();

    // Verificar patrones de ambigüedad
    for (const pattern of this.AMBIGUITY_PATTERNS) {
      if (pattern.pattern.test(queryLower)) {
        ambiguityScore += 1;
      }
    }

    // Verificar términos vagos
    const vagueTerms = [
      'algo', 'bueno', 'bonito', 'nice', 'decent', 'propiedad', 'inmueble',
      'lugar', 'sitio', 'cerca', 'lejos', 'grande', 'pequeño'
    ];

    for (const term of vagueTerms) {
      if (queryLower.includes(term)) {
        ambiguityScore += 0.5;
      }
    }

    // Verificar falta de especificidad
    const specificTerms = [
      'zona', 'habitaciones', 'baños', 'metros', 'm2', 'quetzales', 'q',
      'apartamento', 'casa', 'oficina'
    ];

    let specificityScore = 0;
    for (const term of specificTerms) {
      if (queryLower.includes(term)) {
        specificityScore += 1;
      }
    }

    // Calcular nivel final
    const finalScore = ambiguityScore - (specificityScore * 0.3);

    if (finalScore >= 2) return 'high';
    if (finalScore >= 1) return 'medium';
    return 'low';
  }

  /**
   * Genera múltiples interpretaciones usando OpenAI
   */
  private async generateInterpretations(
    query: string, 
    contextAnalysis?: ContextAnalysis
  ): Promise<QueryInterpretation[]> {
    const prompt = this.buildDisambiguationPrompt(query, contextAnalysis);

    const response = await this.openai.chat.completions.create({
      model: this.model,
      messages: [
        {
          role: 'system',
          content: 'Eres un experto en desambiguación de consultas inmobiliarias en Guatemala. Genera múltiples interpretaciones posibles para consultas ambiguas.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.3,
      max_tokens: 1200,
    });

    const content = response.choices[0].message.content || '[]';
    return this.parseInterpretations(content);
  }

  /**
   * Construye el prompt para desambiguación
   */
  private buildDisambiguationPrompt(query: string, contextAnalysis?: ContextAnalysis): string {
    let prompt = `
Analiza esta consulta inmobiliaria AMBIGUA y genera 3-5 interpretaciones posibles:

CONSULTA: "${query}"
`;

    if (contextAnalysis) {
      prompt += `
CONTEXTO DETECTADO:
- Nivel económico: ${contextAnalysis.economicContext.budgetLevel}
- Zonas preferidas: ${contextAnalysis.locationContext.preferredZones.join(', ') || 'No especificadas'}
- Orientación familiar: ${contextAnalysis.familyContext.isFamilyOriented ? 'Sí' : 'No'}
- Preferencias: ${contextAnalysis.lifestyleContext.modernPreference ? 'Moderno' : ''} ${contextAnalysis.lifestyleContext.quietPreference ? 'Tranquilo' : ''}
`;
    }

    prompt += `
Para cada interpretación, considera:
1. Diferentes ubicaciones posibles en Guatemala
2. Diferentes tipos de propiedad
3. Diferentes rangos de precio
4. Diferentes necesidades de amenidades

Genera interpretaciones ESPECÍFICAS y REALISTAS para el mercado guatemalteco.

Responde SOLO en JSON array:
[
  {
    "interpretation": "Interpretación específica de la consulta",
    "probability": 0.0-1.0,
    "reasoning": "Por qué esta interpretación es probable",
    "suggestedFilters": {
      "location": ["Zona 14", "Zona 10"],
      "propertyType": ["apartamento"],
      "priceRange": {"min": 300000, "max": 800000},
      "amenities": ["piscina", "gimnasio"],
      "bedrooms": 2,
      "bathrooms": 2
    },
    "confidence": 0.0-1.0
  }
]
`;

    return prompt;
  }

  /**
   * Parsea las interpretaciones de la respuesta
   */
  private parseInterpretations(content: string): QueryInterpretation[] {
    try {
      const cleanContent = content.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      const parsed = JSON.parse(cleanContent);

      if (!Array.isArray(parsed)) {
        throw new Error('Respuesta no es un array');
      }

      return parsed.map((item: any) => ({
        interpretation: item.interpretation || 'Interpretación no especificada',
        probability: Math.max(0, Math.min(1, item.probability || 0.5)),
        reasoning: item.reasoning || 'Sin razonamiento',
        suggestedFilters: {
          location: item.suggestedFilters?.location || [],
          propertyType: item.suggestedFilters?.propertyType || [],
          priceRange: item.suggestedFilters?.priceRange || undefined,
          amenities: item.suggestedFilters?.amenities || [],
          bedrooms: item.suggestedFilters?.bedrooms || undefined,
          bathrooms: item.suggestedFilters?.bathrooms || undefined,
        },
        confidence: Math.max(0, Math.min(1, item.confidence || 0.5)),
      }));

    } catch (error) {
      console.warn('Error parseando interpretaciones:', error);
      return this.createDefaultInterpretations();
    }
  }

  /**
   * Rankea interpretaciones por probabilidad y contexto
   */
  private rankInterpretations(
    interpretations: QueryInterpretation[], 
    contextAnalysis?: ContextAnalysis
  ): QueryInterpretation[] {
    return interpretations
      .map(interpretation => {
        let adjustedProbability = interpretation.probability;

        // Ajustar probabilidad basado en contexto
        if (contextAnalysis) {
          // Boost para interpretaciones que coinciden con contexto económico
          if (interpretation.suggestedFilters.priceRange) {
            const contextRange = contextAnalysis.economicContext.priceRange;
            const overlap = this.calculatePriceOverlap(
              interpretation.suggestedFilters.priceRange,
              contextRange
            );
            adjustedProbability += overlap * 0.2;
          }

          // Boost para interpretaciones que coinciden con zonas preferidas
          if (contextAnalysis.locationContext.preferredZones.length > 0) {
            const locationMatch = interpretation.suggestedFilters.location?.some(loc =>
              contextAnalysis.locationContext.preferredZones.includes(loc)
            );
            if (locationMatch) {
              adjustedProbability += 0.15;
            }
          }
        }

        return {
          ...interpretation,
          probability: Math.min(1, adjustedProbability),
        };
      })
      .sort((a, b) => b.probability - a.probability);
  }

  /**
   * Calcula solapamiento entre rangos de precio
   */
  private calculatePriceOverlap(range1: { min: number; max: number }, range2: { min: number; max: number }): number {
    const overlapMin = Math.max(range1.min, range2.min);
    const overlapMax = Math.min(range1.max, range2.max);
    
    if (overlapMin >= overlapMax) return 0;
    
    const overlapSize = overlapMax - overlapMin;
    const range1Size = range1.max - range1.min;
    const range2Size = range2.max - range2.min;
    const avgRangeSize = (range1Size + range2Size) / 2;
    
    return overlapSize / avgRangeSize;
  }

  /**
   * Genera consulta de fallback
   */
  private generateFallbackQuery(query: string, interpretations: QueryInterpretation[]): string {
    if (interpretations.length === 0) return query;

    const topInterpretation = interpretations[0];
    
    // Usar la interpretación más probable como fallback
    return topInterpretation.interpretation;
  }

  /**
   * Crea resultado simple para consultas no ambiguas
   */
  private createSimpleResult(query: string, processingTime: number): DisambiguationResult {
    const singleInterpretation: QueryInterpretation = {
      interpretation: query,
      probability: 0.95,
      reasoning: 'Consulta específica sin ambigüedad detectada',
      suggestedFilters: {},
      confidence: 0.95,
    };

    return {
      originalQuery: query,
      isAmbiguous: false,
      ambiguityLevel: 'low',
      interpretations: [singleInterpretation],
      recommendedInterpretation: singleInterpretation,
      fallbackQuery: query,
      processingTime,
    };
  }

  /**
   * Crea interpretaciones por defecto
   */
  private createDefaultInterpretations(): QueryInterpretation[] {
    return [
      {
        interpretation: 'Búsqueda general de propiedades',
        probability: 0.6,
        reasoning: 'Interpretación por defecto',
        suggestedFilters: {},
        confidence: 0.3,
      }
    ];
  }

  /**
   * Crea resultado de fallback
   */
  private createFallbackResult(query: string, processingTime: number): DisambiguationResult {
    return {
      originalQuery: query,
      isAmbiguous: false,
      ambiguityLevel: 'low',
      interpretations: this.createDefaultInterpretations(),
      recommendedInterpretation: this.createDefaultInterpretations()[0],
      fallbackQuery: query,
      processingTime,
    };
  }
}
