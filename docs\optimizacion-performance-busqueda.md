# 🚀 Optimización de Performance del Motor de Búsqueda Semántica

**Fecha:** 2025-01-07  
**Estado:** Documentado - Pendiente de implementación  
**Prioridad:** Alta (implementar cuando haya tráfico real)

## 📊 Situación Actual

### Performance Observada
- **Tiempo promedio:** 7,000ms (7 segundos)
- **Consultas simples:** 4,000-7,000ms
- **Consultas complejas:** 9,000-11,000ms
- **Objetivo:** <2,000ms (2 segundos)

### Bottlenecks Identificados
1. **Generación de embeddings OpenAI:** 2,000-3,000ms (30-40%)
2. **Generación respuesta IA:** 3,000-5,000ms (40-60%)
3. **Búsqueda Qdrant:** 300-800ms (5-10%)
4. **Procesamiento aplicación:** 500-1,000ms (10-15%)

## 🎯 Estrategia de Optimización

### Fase 1: Optimizaciones Inmediatas (64% mejora)
**Tiempo estimado:** 1-2 días  
**Impacto:** 7,000ms → 2,500ms

#### 1.1 Respuesta IA Opcional para N8N
```json
// Cambio en configuración N8N (Inmo_Agent.json línea 1447)
"options": {
  "limit": 12,
  "includeResponse": false,  // ← Cambiar de true a false
  "responseLanguage": "es",
  "includeBreakdown": true
}
```

**Justificación:**
- N8N genera sus propias respuestas, no usa la explicación IA
- Ahorro inmediato de 4 segundos por consulta
- Sin pérdida de funcionalidad

#### 1.2 Cache Básico de Embeddings
```javascript
// Implementación en /app/api/v1/search/route.ts
const embeddingCache = new Map();

async function getCachedEmbedding(query) {
  const cacheKey = query.toLowerCase().trim();
  
  if (embeddingCache.has(cacheKey)) {
    console.log("Cache HIT:", cacheKey);
    return embeddingCache.get(cacheKey);
  }
  
  console.log("Cache MISS:", cacheKey);
  const embedding = await generateEmbedding(query);
  embeddingCache.set(cacheKey, embedding);
  
  // Limitar tamaño del cache
  if (embeddingCache.size > 1000) {
    const firstKey = embeddingCache.keys().next().value;
    embeddingCache.delete(firstKey);
  }
  
  return embedding;
}
```

### Fase 2: Cache Inteligente (80% mejora adicional)
**Tiempo estimado:** 1 semana después de tener datos reales  
**Impacto:** 2,500ms → 500ms para consultas populares

#### 2.1 Análisis de Consultas Reales
```javascript
// Sistema de analytics para identificar patrones
const queryAnalytics = {
  frequency: new Map(), // query → count
  performance: new Map(), // query → avg_time
  lastUsed: new Map() // query → timestamp
};

function trackQuery(query, responseTime) {
  const key = query.toLowerCase().trim();
  
  // Frecuencia
  queryAnalytics.frequency.set(key, 
    (queryAnalytics.frequency.get(key) || 0) + 1
  );
  
  // Performance
  const currentAvg = queryAnalytics.performance.get(key) || responseTime;
  const newAvg = (currentAvg + responseTime) / 2;
  queryAnalytics.performance.set(key, newAvg);
  
  // Timestamp
  queryAnalytics.lastUsed.set(key, Date.now());
}
```

#### 2.2 Pre-generación de Consultas Populares
```javascript
// Después de 1 semana de datos reales
function getTopQueries(minFrequency = 5, limit = 50) {
  return Array.from(queryAnalytics.frequency.entries())
    .filter(([query, count]) => count >= minFrequency)
    .sort(([,a], [,b]) => b - a)
    .slice(0, limit)
    .map(([query]) => query);
}

async function preGeneratePopularEmbeddings() {
  const topQueries = getTopQueries();
  console.log(`Pre-generando embeddings para ${topQueries.length} consultas populares`);
  
  for (const query of topQueries) {
    if (!embeddingCache.has(query)) {
      const embedding = await generateEmbedding(query);
      embeddingCache.set(query, embedding);
      console.log(`✅ Pre-generado: "${query}"`);
    }
  }
}
```

### Fase 3: Cache Proactivo con IA (95% cobertura)
**Tiempo estimado:** 2-4 semanas después  
**Impacto:** Cobertura casi total de consultas comunes

#### 3.1 Generación Inteligente de Consultas
```javascript
async function generateCommonQueries() {
  // Analizar propiedades actuales
  const propertyStats = await analyzeCurrentProperties();
  
  const prompt = `
Basado en estas propiedades disponibles en Guatemala:
- Ubicaciones: ${propertyStats.locations.join(', ')}
- Tipos: ${propertyStats.types.join(', ')}
- Rangos de precio: ${propertyStats.priceRanges.join(', ')}
- Características: ${propertyStats.features.join(', ')}

Genera las 100 consultas más comunes que haría un usuario real.
Enfócate en consultas naturales como:
- "apartamento zona 10"
- "casa con jardín zona 14"
- "alquiler 2 habitaciones"

Formato: JSON array de strings.
NO incluyas consultas artificiales como "z10" o "depto" solas.
`;

  const response = await openai.chat.completions.create({
    model: 'gpt-3.5-turbo',
    messages: [{ role: 'user', content: prompt }],
    temperature: 0.7
  });
  
  return JSON.parse(response.choices[0].message.content);
}
```

## 📈 Proyección de Resultados

### Métricas Esperadas

| Fase | Tiempo Promedio | Mejora | Cobertura Cache |
|------|----------------|--------|-----------------|
| **Actual** | 7,000ms | - | 0% |
| **Fase 1** | 2,500ms | 64% | 0% |
| **Fase 2** | 500ms* | 93%* | 60-80% |
| **Fase 3** | 300ms* | 96%* | 90-95% |

*Para consultas en cache

### Experiencia de Usuario

#### WhatsApp (N8N)
```
Antes: Usuario → [7 segundos] → Respuesta
Después: Usuario → [0.5-2.5 segundos] → Respuesta
```

#### Aplicación Web
```
Antes: Usuario → [7 segundos] → Propiedades + IA
Después: Usuario → [2.5 segundos] → Propiedades
                → [6 segundos] → + Explicación IA (opcional)
```

## 🛠️ Plan de Implementación

### Criterios para Inicio
- **Mínimo 100 consultas reales** por semana
- **Al menos 20 consultas únicas** diferentes
- **Patrones de uso identificables**

### Cronograma Sugerido
1. **Semana 1-2:** Monitoreo de consultas reales
2. **Semana 3:** Implementar Fase 1 (optimizaciones inmediatas)
3. **Semana 4:** Análisis de datos y Fase 2 (cache inteligente)
4. **Mes 2:** Evaluación y posible Fase 3 (cache proactivo)

### Métricas de Éxito
- **Tiempo promedio < 2,000ms** para nuevas consultas
- **Tiempo promedio < 500ms** para consultas en cache
- **Cache hit rate > 70%** después de 1 mes
- **Satisfacción usuario** (tiempo percibido)

## 🔧 Consideraciones Técnicas

### Gestión de Memoria
```javascript
// Configuración de cache con TTL y límites
const cacheConfig = {
  maxSize: 1000, // máximo 1000 consultas
  ttl: 24 * 60 * 60 * 1000, // 24 horas
  cleanupInterval: 60 * 60 * 1000 // limpiar cada hora
};
```

### Invalidación de Cache
- **TTL de 24 horas** para embeddings (no cambian)
- **Limpieza automática** cuando cache excede límite
- **Monitoreo de uso** para optimizar retención

### Fallback y Resilencia
```javascript
async function getCachedEmbeddingWithFallback(query) {
  try {
    return await getCachedEmbedding(query);
  } catch (error) {
    console.warn("Cache error, fallback to direct generation:", error);
    return await generateEmbedding(query);
  }
}
```

## 📊 Monitoreo y Analytics

### Métricas a Trackear
```javascript
const metrics = {
  totalQueries: 0,
  cacheHits: 0,
  cacheMisses: 0,
  avgResponseTime: 0,
  popularQueries: new Map(),
  errorRate: 0
};
```

### Dashboard Sugerido
- **Cache hit rate** en tiempo real
- **Top 20 consultas** más frecuentes
- **Tiempo de respuesta** promedio por hora
- **Distribución de tiempos** (histograma)

## 🚨 Riesgos y Mitigaciones

### Riesgos Identificados
1. **Memoria limitada** del servidor
2. **Consultas muy específicas** (bajo cache hit rate)
3. **Cambios en propiedades** afectando relevancia

### Mitigaciones
1. **Límites de cache** y limpieza automática
2. **Análisis continuo** de patrones de uso
3. **TTL apropiado** para balance entre performance y frescura

## 📝 Notas de Implementación

### Archivos a Modificar
- `/app/api/v1/search/route.ts` - Cache de embeddings
- `/agentes/Inmo_Agent.json` - Configuración N8N
- `/lib/qdrant/embeddings.ts` - Funciones de cache

### Variables de Entorno Nuevas
```env
# Cache configuration
EMBEDDING_CACHE_SIZE=1000
EMBEDDING_CACHE_TTL=86400000
ENABLE_QUERY_ANALYTICS=true
```

### Testing
- **Pruebas de carga** con consultas repetidas
- **Medición de memoria** con cache lleno
- **Validación de hit rate** con datos reales

---

**Documento creado:** 2025-01-07  
**Próxima revisión:** Cuando se alcancen 100 consultas reales/semana  
**Responsable:** Equipo de desarrollo
