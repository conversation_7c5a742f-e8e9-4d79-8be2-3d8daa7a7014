import { NextRequest, NextResponse } from 'next/server';
import { ConvexHttpClient } from 'convex/browser';
import { api } from '@/convex/_generated/api';

// Cliente Convex para llamadas HTTP
const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

// Validar API Key de administrador
function validateAdminApiKey(request: NextRequest): boolean {
  const apiKey = request.headers.get('X-API-Key');
  const validApiKey = process.env.RAG_ADMIN_API_KEY;
  
  if (!validApiKey) {
    console.warn('RAG_ADMIN_API_KEY not configured');
    return false;
  }
  
  return apiKey === validApiKey;
}

// Endpoint para re-indexar todas las propiedades
export async function POST(request: NextRequest) {
  try {
    // Validar API Key de administrador
    if (!validateAdminApiKey(request)) {
      return NextResponse.json(
        { error: 'Invalid or missing admin API key' },
        { status: 401 }
      );
    }

    // Parsear body
    const body = await request.json();
    const {
      force = false,
      batchSize = 20,
      dryRun = false,
    } = body;

    console.log(`Admin reindex request: force=${force}, batchSize=${batchSize}, dryRun=${dryRun}`);

    if (dryRun) {
      // Modo dry-run: solo obtener estadísticas sin hacer cambios
      const stats = await convex.action(api.rag.getIndexStats);
      const syncValidation = await convex.action(api.rag.validateSync);

      return NextResponse.json({
        dryRun: true,
        message: 'Dry run completed - no changes made',
        currentStats: stats,
        syncStatus: syncValidation,
        wouldReindex: syncValidation.sync.missing.length + (force ? syncValidation.sync.total : 0),
        timestamp: new Date().toISOString(),
      });
    }

    // Ejecutar re-indexación real
    const startTime = Date.now();
    
    const result = await convex.action(api.rag.reindexAllProperties, {
      force,
      batchSize,
    });

    const endTime = Date.now();
    const duration = Math.round((endTime - startTime) / 1000);

    // Obtener estadísticas finales
    const finalStats = await convex.action(api.rag.getIndexStats);

    const response = {
      success: true,
      message: result.message,
      reindexType: force ? 'full' : 'incremental',
      propertiesProcessed: result.count,
      duration: `${duration} seconds`,
      finalStats,
      timestamp: new Date().toISOString(),
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error in reindex API:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Endpoint para obtener estado del índice
export async function GET(request: NextRequest) {
  try {
    // Validar API Key de administrador
    if (!validateAdminApiKey(request)) {
      return NextResponse.json(
        { error: 'Invalid or missing admin API key' },
        { status: 401 }
      );
    }

    console.log('Admin index status request');

    // Obtener estadísticas del índice
    const stats = await convex.action(api.rag.getIndexStats);
    
    // Validar sincronización
    const syncValidation = await convex.action(api.rag.validateSync);
    
    // Probar conexiones
    const connectionTest = await convex.action(api.rag.testQdrantConnection);

    const response = {
      indexStats: stats,
      syncStatus: syncValidation,
      connections: connectionTest,
      health: {
        overall: connectionTest.overall && syncValidation.sync.inSync,
        qdrant: connectionTest.qdrant,
        embeddings: connectionTest.embeddings,
        synchronized: syncValidation.sync.inSync,
      },
      recommendations: generateRecommendations(syncValidation, connectionTest),
      timestamp: new Date().toISOString(),
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error getting index status:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Endpoint para limpiar vectores huérfanos
export async function DELETE(request: NextRequest) {
  try {
    // Validar API Key de administrador
    if (!validateAdminApiKey(request)) {
      return NextResponse.json(
        { error: 'Invalid or missing admin API key' },
        { status: 401 }
      );
    }

    console.log('Admin cleanup orphaned vectors request');

    const result = await convex.action(api.rag.cleanupOrphanedVectors);

    return NextResponse.json({
      success: true,
      message: result.message,
      deletedCount: result.deletedCount,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error cleaning up orphaned vectors:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Generar recomendaciones basadas en el estado del sistema
function generateRecommendations(syncValidation: any, connectionTest: any): string[] {
  const recommendations: string[] = [];

  if (!connectionTest.qdrant) {
    recommendations.push('❌ Qdrant connection failed - check QDRANT_URL and QDRANT_API_KEY');
  }

  if (!connectionTest.embeddings) {
    recommendations.push('❌ OpenAI embeddings failed - check OPENAI_API_KEY');
  }

  if (!syncValidation.sync.inSync) {
    if (syncValidation.sync.missing.length > 0) {
      recommendations.push(`⚠️ ${syncValidation.sync.missing.length} properties missing in Qdrant - run incremental reindex`);
    }
    if (syncValidation.sync.extra.length > 0) {
      recommendations.push(`⚠️ ${syncValidation.sync.extra.length} orphaned vectors in Qdrant - run cleanup`);
    }
  }

  if (syncValidation.sync.inSync && connectionTest.overall) {
    recommendations.push('✅ System is healthy and synchronized');
  }

  if (syncValidation.stats.totalVectors === 0) {
    recommendations.push('📝 No properties indexed yet - run initial indexing');
  }

  return recommendations;
}

// Documentación del endpoint
export async function OPTIONS() {
  return NextResponse.json({
    endpoint: '/api/v1/admin/reindex',
    description: 'Administrative endpoint for managing property indexing in Qdrant',
    authentication: 'X-API-Key header required (admin key)',
    methods: {
      POST: {
        description: 'Re-index all properties',
        parameters: {
          force: 'boolean (optional, default: false) - Force full reindex by recreating collection',
          batchSize: 'number (optional, default: 20) - Number of properties to process per batch',
          dryRun: 'boolean (optional, default: false) - Preview changes without executing',
        },
      },
      GET: {
        description: 'Get index status and health information',
        parameters: 'None',
      },
      DELETE: {
        description: 'Clean up orphaned vectors',
        parameters: 'None',
      },
    },
    examples: {
      incrementalReindex: {
        method: 'POST',
        body: { force: false, batchSize: 20 },
      },
      fullReindex: {
        method: 'POST',
        body: { force: true, batchSize: 50 },
      },
      dryRun: {
        method: 'POST',
        body: { dryRun: true },
      },
    },
    security: {
      note: 'This endpoint requires admin-level API key (RAG_ADMIN_API_KEY)',
      rateLimit: 'Limited to prevent abuse - use sparingly',
    },
  });
}
