import { mutation, query, internalQuery, internalMutation } from "./_generated/server";
import { v } from "convex/values";

// Guardar cada mensaje de conversación
export const saveMessage = mutation({
  args: {
    chatId: v.string(), // "<EMAIL>"
    userName: v.string(),
    messageId: v.string(),
    messageType: v.union(v.literal("user"), v.literal("assistant")),
    content: v.string(),
    timestamp: v.string(),
    
    // Análisis automático
    propertiesDiscussed: v.optional(v.array(v.string())), // IDs de propiedades mencionadas
    interestLevel: v.optional(v.number()), // 1-5 scale
    intent: v.optional(v.string()), // "buscar", "info", "agendar"
    budget: v.optional(v.object({
      min: v.number(),
      max: v.number()
    })),
    preferences: v.optional(v.object({
      type: v.string(),
      zones: v.array(v.string()),
      amenities: v.array(v.string())
    }))
  },
  handler: async (ctx, args) => {
    const now = new Date().toISOString();
    
    // Guardar mensaje
    const messageId = await ctx.db.insert("conversations", {
      ...args,
      createdAt: now,
      processed: false
    });

    // Si es mensaje de usuario, analizar interés
    if (args.messageType === "user") {
      await analyzeUserInterest(ctx, args);
    }

    return messageId;
  },
});

// Función para analizar interés del usuario
async function analyzeUserInterest(ctx: any, message: any) {
  const content = message.content.toLowerCase();
  
  // Detectar nivel de interés basado en palabras clave
  let interestLevel = 1;
  
  const highInterestWords = [
    "quiero ver", "agendar", "visita", "cuando puedo", "precio final",
    "me interesa mucho", "perfecto", "ideal", "me encanta"
  ];
  
  const mediumInterestWords = [
    "me gusta", "interesante", "podría ser", "dime más", "cuéntame"
  ];
  
  if (highInterestWords.some(word => content.includes(word))) {
    interestLevel = 5;
  } else if (mediumInterestWords.some(word => content.includes(word))) {
    interestLevel = 3;
  }

  // Actualizar perfil del lead
  await ctx.runMutation("conversations:updateLeadProfile", {
    chatId: message.chatId,
    updates: {
      lastInteraction: message.timestamp,
      interestLevel
    }
  });
}

// Actualizar perfil del lead
export const updateLeadProfile = mutation({
  args: {
    chatId: v.string(),
    updates: v.object({
      lastInteraction: v.string(),
      interestLevel: v.number(),
      budgetMin: v.optional(v.number()),
      budgetMax: v.optional(v.number()),
      preferredType: v.optional(v.string()),
      preferredZones: v.optional(v.array(v.string())),
      amenitiesWanted: v.optional(v.array(v.string()))
    })
  },
  handler: async (ctx, args) => {
    const existingLead = await ctx.db
      .query("leads")
      .withIndex("by_chatId", (q) => q.eq("chatId", args.chatId))
      .first();

    if (existingLead) {
      await ctx.db.patch(existingLead._id, {
        ...args.updates,
        updatedAt: new Date().toISOString()
      });
    } else {
      await ctx.db.insert("leads", {
        chatId: args.chatId,
        status: "new",
        source: "whatsapp",
        ...args.updates,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
    }
  },
});

// Generar reporte semanal
export const generateWeeklyReport = query({
  args: {
    startDate: v.string(),
    endDate: v.string()
  },
  handler: async (ctx, args) => {
    // Obtener todas las conversaciones de la semana
    const conversations = await ctx.db
      .query("conversations")
      .filter((q: any) => 
        q.and(
          q.gte(q.field("createdAt"), args.startDate),
          q.lte(q.field("createdAt"), args.endDate)
        )
      )
      .collect();

    // Obtener propiedades mencionadas
    const propertiesDiscussed = new Map();
    
    conversations.forEach(conv => {
      if (conv.propertiesDiscussed) {
        conv.propertiesDiscussed.forEach(propId => {
          if (!propertiesDiscussed.has(propId)) {
            propertiesDiscussed.set(propId, []);
          }
          propertiesDiscussed.get(propId).push({
            chatId: conv.chatId,
            userName: conv.userName,
            interestLevel: conv.interestLevel || 1,
            timestamp: conv.createdAt
          });
        });
      }
    });

    // Generar top 3 interesados por propiedad
    const propertyReports = Array.from(propertiesDiscussed.entries()).map(([propId, interests]) => {
             const topInterested = interests
         .sort((a: any, b: any) => (b.interestLevel || 1) - (a.interestLevel || 1))
         .slice(0, 3);

      return {
        propertyId: propId,
        totalInterested: interests.length,
        topInterested: topInterested
      };
    });

    // Generar briefs por usuario
    const userBriefs = new Map();
    
    conversations.forEach(conv => {
      if (!userBriefs.has(conv.chatId)) {
        userBriefs.set(conv.chatId, {
          userName: conv.userName,
          chatId: conv.chatId,
          messages: [],
          interests: new Set(),
          budget: null,
          preferences: {}
        });
      }
      
      const brief = userBriefs.get(conv.chatId);
      brief.messages.push({
        type: conv.messageType,
        content: conv.content.substring(0, 200),
        timestamp: conv.createdAt
      });
      
      if (conv.propertiesDiscussed) {
        conv.propertiesDiscussed.forEach(prop => brief.interests.add(prop));
      }
    });

    return {
      reportPeriod: {
        start: args.startDate,
        end: args.endDate
      },
      totalConversations: conversations.length,
      propertyReports,
      userBriefs: Array.from(userBriefs.values()).map(brief => ({
        ...brief,
        interests: Array.from(brief.interests)
      })),
      generatedAt: new Date().toISOString()
    };
  },
});

// Query para dashboard de agentes
export const getAgentDashboard = query({
  args: {
    agentId: v.optional(v.string()),
    days: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    const daysBack = args.days || 7;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - daysBack);

    const conversations = await ctx.db
      .query("conversations")
      .filter((q: any) => q.gte(q.field("createdAt"), startDate.toISOString()))
      .collect();

    const leads = await ctx.db
      .query("leads")
      .filter((q: any) => q.gte(q.field("lastInteraction"), startDate.toISOString()))
      .collect();

    return {
      stats: {
        totalConversations: conversations.length,
        totalLeads: leads.length,
        highInterestLeads: leads.filter(l => l.interestLevel >= 4).length,
        conversionRate: (leads.filter(l => l.status === "converted").length / leads.length * 100) || 0
      },
      recentActivity: conversations.slice(-10),
      hotLeads: leads
        .filter(l => l.interestLevel >= 4)
        .sort((a, b) => b.interestLevel - a.interestLevel)
        .slice(0, 5)
    };
  },
});

// Query para obtener historial de conversaciones de un chat específico
export const getChatHistory = query({
  args: {
    chatId: v.string(),
    limit: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    const conversations = await ctx.db
      .query("conversations")
      .withIndex("by_chatId", (q) => q.eq("chatId", args.chatId))
      .order("desc")
      .take(args.limit || 10);

    // Devolver en orden cronológico (más antiguo primero)
    return conversations.reverse().map(conv => ({
      messageId: conv.messageId,
      messageType: conv.messageType,
      content: conv.content,
      timestamp: conv.timestamp,
      userName: conv.userName,
      intent: conv.intent,
      interestLevel: conv.interestLevel,
      propertiesDiscussed: conv.propertiesDiscussed,
      budget: conv.budget,
      preferences: conv.preferences
    }));
  },
});

// Query para obtener perfil del lead
export const getLeadProfile = query({
  args: {
    chatId: v.string()
  },
  handler: async (ctx, args) => {
    const lead = await ctx.db
      .query("leads")
      .withIndex("by_chatId", (q) => q.eq("chatId", args.chatId))
      .first();

    if (!lead) {
      return null;
    }

    return {
      chatId: lead.chatId,
      userName: lead.userName,
      status: lead.status,
      interestLevel: lead.interestLevel,
      budgetMin: lead.budgetMin,
      budgetMax: lead.budgetMax,
      preferredType: lead.preferredType,
      preferredZones: lead.preferredZones,
      amenitiesWanted: lead.amenitiesWanted,
      lastInteraction: lead.lastInteraction,
      totalInteractions: lead.totalInteractions,
      nextFollowUp: lead.nextFollowUp,
      priority: lead.priority,
      notes: lead.notes,
      tags: lead.tags
    };
  },
});

// Query para obtener todas las conversaciones para análisis de IA
export const getAllConversationsForAnalysis = query({
  args: {
    limit: v.optional(v.number()),
    startDate: v.optional(v.string()),
    endDate: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    let query = ctx.db.query("conversations");

    // Filtrar por fechas si se proporcionan
    if (args.startDate && args.endDate) {
      query = query.filter((q: any) =>
        q.and(
          q.gte(q.field("createdAt"), args.startDate!),
          q.lte(q.field("createdAt"), args.endDate!)
        )
      );
    }

    const conversations = await query
      .order("desc")
      .take(args.limit || 100);

    // Agrupar por chatId para tener conversaciones completas
    const groupedConversations = new Map<string, {
      chatId: string;
      userName: string;
      messages: Array<{
        messageId: string;
        messageType: "user" | "assistant";
        content: string;
        timestamp: string;
        intent?: string;
        interestLevel?: number;
      }>;
    }>();

    conversations.forEach(conv => {
      if (!groupedConversations.has(conv.chatId)) {
        groupedConversations.set(conv.chatId, {
          chatId: conv.chatId,
          userName: conv.userName,
          messages: []
        });
      }

      groupedConversations.get(conv.chatId)!.messages.push({
        messageId: conv.messageId,
        messageType: conv.messageType,
        content: conv.content,
        timestamp: conv.timestamp,
        intent: conv.intent,
        interestLevel: conv.interestLevel
      });
    });

    // Convertir a array y ordenar mensajes por timestamp
    const result = Array.from(groupedConversations.values()).map(chat => ({
      ...chat,
      messages: chat.messages.sort((a: any, b: any) =>
        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
      )
    }));

    return result;
  },
});

/**
 * Función interna para obtener todas las conversaciones
 */
export const getAllConversationsInternal = internalQuery({
  args: {},
  handler: async (ctx) => {
    return await ctx.db.query("conversations").collect();
  },
});

/**
 * Función interna para obtener todos los leads
 */
export const getAllLeadsInternal = internalQuery({
  args: {},
  handler: async (ctx) => {
    return await ctx.db.query("leads").collect();
  },
});

/**
 * Función interna para eliminar una conversación
 */
export const deleteConversationInternal = internalMutation({
  args: { conversationId: v.id("conversations") },
  handler: async (ctx, { conversationId }) => {
    await ctx.db.delete(conversationId);
  },
});

/**
 * Función interna para eliminar un lead
 */
export const deleteLeadInternal = internalMutation({
  args: { leadId: v.id("leads") },
  handler: async (ctx, { leadId }) => {
    await ctx.db.delete(leadId);
  },
});