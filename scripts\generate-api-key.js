/**
 * Script para generar API keys seguras para Qdrant
 * Ejecutar con: node scripts/generate-api-key.js
 */

const crypto = require('crypto');

function generateSecureApiKey(length = 32) {
  return crypto.randomBytes(length).toString('hex');
}

function generateQdrantConfig() {
  const apiKey = generateSecureApiKey();
  
  console.log('🔐 API Key generada para Qdrant:');
  console.log('=====================================');
  console.log(`API_KEY: ${apiKey}`);
  console.log('');
  console.log('📋 Variables de entorno para EasyPanel:');
  console.log('=====================================');
  console.log(`QDRANT__SERVICE__API_KEY=${apiKey}`);
  console.log('QDRANT__SERVICE__ENABLE_CORS=true');
  console.log('QDRANT__SERVICE__HTTP_PORT=6333');
  console.log('');
  console.log('📝 Para tu .env.local:');
  console.log('=====================================');
  console.log(`QDRANT_API_KEY=${apiKey}`);
  console.log('');
  console.log('⚠️  IMPORTANTE:');
  console.log('- Guarda esta API key de forma segura');
  console.log('- Después de configurar en EasyPanel, reinicia el contenedor');
  console.log('- Actualiza tu .env.local con la API key generada');
}

// Ejecutar
generateQdrantConfig();
