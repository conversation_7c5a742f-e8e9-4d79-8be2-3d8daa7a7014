"use client";

import React, { useState } from 'react';
import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { useUser } from "@clerk/nextjs";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import {
  Settings,
  Globe,
  MapPin,
  CheckCircle,
  AlertTriangle,
  Loader2,
  Plus,
  Edit
} from "lucide-react";
import { useRouter } from "next/navigation";

export default function LocationConfigPage() {
  const { user } = useUser();
  const router = useRouter();
  const [loading, setLoading] = useState<string | null>(null);

  // Queries
  const currentUser = useQuery(api.users.getCurrentUser);
  const allConfigs = useQuery(api.locationConfig.getAllActiveConfigs);
  const guatemalaConfig = useQuery(api.locationConfig.getCountryConfig, { countryCode: "GT" });

  // Mutations
  const initializeGuatemala = useMutation(api.locationConfig.initializeGuatemalaConfig);
  const initializeCountry = useMutation(api.locationConfig.initializeCountryConfig);

  // Verificar que el usuario existe y es admin
  if (!currentUser || currentUser.role !== 'admin') {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="max-w-md mx-auto text-center bg-white rounded-lg shadow-lg p-8">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertTriangle className="h-8 w-8 text-red-600" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Acceso Denegado</h1>
          <p className="text-gray-600 mb-6">
            No tienes permisos para acceder a la configuración de ubicación.
          </p>
        </div>
      </div>
    );
  }

  const handleInitializeCountry = async (countryCode: string) => {
    setLoading(`initialize-${countryCode}`);
    try {
      const result = await initializeCountry({ countryCode });
      toast.success(result.message);
    } catch (error) {
      console.error("Error:", error);
      toast.error(`Error al inicializar configuración de ${countryCode}`);
    } finally {
      setLoading(null);
    }
  };

  const handleInitializeGuatemala = async () => {
    setLoading("initialize");
    try {
      const result = await initializeGuatemala();
      toast.success(result.message);
    } catch (error) {
      console.error("Error:", error);
      toast.error("Error al inicializar configuración de Guatemala");
    } finally {
      setLoading(null);
    }
  };



  return (
    <div className="flex flex-col gap-6 p-4 md:p-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-semibold tracking-tight flex items-center gap-2">
            <Globe className="h-6 w-6 md:h-8 md:w-8" />
            Configuración de Ubicación
          </h1>
          <p className="text-muted-foreground mt-2 text-sm md:text-base">
            Administra la configuración geográfica del sistema
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={() => router.push("/dashboard/admin")}
            variant="outline"
          >
            <Settings className="h-4 w-4 mr-2" />
            Volver al Panel
          </Button>
          <Button
            onClick={() => router.push("/dashboard/admin/location-config/custom")}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Plus className="h-4 w-4 mr-2" />
            Crear País Personalizado
          </Button>
        </div>
      </div>

      {/* Estado de Guatemala */}
      <Card className={guatemalaConfig ? "border-green-200 bg-green-50" : "border-yellow-200 bg-yellow-50"}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            Configuración de Guatemala
            {guatemalaConfig ? (
              <Badge className="bg-green-600">Configurado</Badge>
            ) : (
              <Badge variant="secondary">Pendiente</Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {guatemalaConfig ? (
            <div className="space-y-3">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-green-600">País</p>
                  <p className="font-medium text-green-900">{guatemalaConfig.countryName}</p>
                </div>
                <div>
                  <p className="text-sm text-green-600">Código</p>
                  <p className="font-medium text-green-900">{guatemalaConfig.countryCode}</p>
                </div>
              </div>
              
              <div>
                <p className="text-sm text-green-600 mb-2">Jerarquía configurada:</p>
                <ul className="text-sm text-green-800 space-y-1">
                  <li>• {guatemalaConfig.hierarchy.level1.name} {guatemalaConfig.hierarchy.level1.required ? "(requerido)" : "(opcional)"}</li>
                  <li>• {guatemalaConfig.hierarchy.level2.name} {guatemalaConfig.hierarchy.level2.required ? "(requerido)" : "(opcional)"}</li>
                  {guatemalaConfig.hierarchy.level3 && (
                    <li>• {guatemalaConfig.hierarchy.level3.name} {guatemalaConfig.hierarchy.level3.required ? "(requerido)" : "(opcional)"}</li>
                  )}
                  {guatemalaConfig.hierarchy.level4 && (
                    <li>• {guatemalaConfig.hierarchy.level4.name} {guatemalaConfig.hierarchy.level4.required ? "(requerido)" : "(opcional)"}</li>
                  )}
                </ul>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <p className="text-yellow-800">
                La configuración para Guatemala no está inicializada. Esto es necesario para usar el nuevo sistema de ubicación.
              </p>
              <Button
                onClick={handleInitializeGuatemala}
                disabled={loading === "initialize"}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {loading === "initialize" ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Settings className="h-4 w-4 mr-2" />
                )}
                Inicializar Configuración de Guatemala
              </Button>
            </div>
          )}
        </CardContent>
      </Card>





      {/* Países Disponibles para Configurar */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Países Disponibles
          </CardTitle>
          <CardDescription>
            Configuraciones predefinidas disponibles para inicializar
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Guatemala */}
            <div className="border rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <h3 className="font-medium">🇬🇹 Guatemala</h3>
                  <p className="text-sm text-gray-500">4 niveles: Departamento → Municipio → Zona → Colonia</p>
                </div>
                {guatemalaConfig ? (
                  <Badge className="bg-green-600">Configurado</Badge>
                ) : (
                  <Button
                    size="sm"
                    onClick={() => handleInitializeCountry("GT")}
                    disabled={loading === "initialize-GT"}
                  >
                    {loading === "initialize-GT" ? (
                      <Loader2 className="h-3 w-3 animate-spin" />
                    ) : (
                      "Configurar"
                    )}
                  </Button>
                )}
              </div>
            </div>

            {/* México */}
            <div className="border rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <h3 className="font-medium">🇲🇽 México</h3>
                  <p className="text-sm text-gray-500">5 niveles: Estado → Delegación → Colonia → Sector → Manzana</p>
                </div>
                <Button
                  size="sm"
                  onClick={() => handleInitializeCountry("MX")}
                  disabled={loading === "initialize-MX"}
                >
                  {loading === "initialize-MX" ? (
                    <Loader2 className="h-3 w-3 animate-spin" />
                  ) : (
                    "Configurar"
                  )}
                </Button>
              </div>
            </div>

            {/* Estados Unidos */}
            <div className="border rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <h3 className="font-medium">🇺🇸 Estados Unidos</h3>
                  <p className="text-sm text-gray-500">3 niveles: State → City → Neighborhood</p>
                </div>
                <Button
                  size="sm"
                  onClick={() => handleInitializeCountry("US")}
                  disabled={loading === "initialize-US"}
                >
                  {loading === "initialize-US" ? (
                    <Loader2 className="h-3 w-3 animate-spin" />
                  ) : (
                    "Configurar"
                  )}
                </Button>
              </div>
            </div>

            {/* España */}
            <div className="border rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <h3 className="font-medium">🇪🇸 España</h3>
                  <p className="text-sm text-gray-500">6 niveles: C. Autónoma → Provincia → Municipio → Distrito → Barrio → Calle</p>
                </div>
                <Button
                  size="sm"
                  onClick={() => handleInitializeCountry("ES")}
                  disabled={loading === "initialize-ES"}
                >
                  {loading === "initialize-ES" ? (
                    <Loader2 className="h-3 w-3 animate-spin" />
                  ) : (
                    "Configurar"
                  )}
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Configuraciones Activas */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            Países Configurados
          </CardTitle>
        </CardHeader>
        <CardContent>
          {allConfigs && allConfigs.length > 0 ? (
            <div className="space-y-3">
              {allConfigs.map((config: any) => (
                <div key={config._id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <p className="font-medium text-lg">{config.countryName}</p>
                      <Badge className="bg-green-600">Activo</Badge>
                    </div>
                    <p className="text-sm text-gray-500 mb-1">Código: {config.countryCode}</p>
                    <p className="text-xs text-gray-400">
                      {Object.keys(config.hierarchy).length} niveles configurados
                    </p>

                    {/* Mostrar jerarquía */}
                    <div className="mt-2">
                      <p className="text-xs text-gray-500 mb-1">Estructura:</p>
                      <div className="flex flex-wrap gap-1">
                        {Object.entries(config.hierarchy).map(([levelKey, levelConfig]: [string, any]) => (
                          <span key={levelKey} className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                            {levelConfig.name}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col gap-2 ml-4">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => router.push(`/dashboard/admin/location-config/manage/${config.countryCode}`)}
                      className="flex items-center gap-2"
                    >
                      <Edit className="h-4 w-4" />
                      Administrar Datos
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500 text-center py-4">
              No hay configuraciones de países activas
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
