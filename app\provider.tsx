"use client";
import { <PERSON><PERSON><PERSON><PERSON>, useAuth } from "@clerk/nextjs";
import { esES } from "@clerk/localizations";
import { ConvexReactClient } from "convex/react";
import { ConvexProviderWithClerk } from "convex/react-clerk";
import { ReactNode, createContext, useContext, useEffect, useState } from "react";
import PageWrapper from "@/components/wrapper/page-wrapper";
import StoreUserEffect from "@/components/store-user-effect";
import InmovaLogo from "@/components/auth/inmova-logo";
import Image from "next/image";

const convex = new ConvexReactClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

// Contexto para el tema de colores
interface ThemeColorContextType {
  themeColor: string;
  setThemeColor: (color: string) => void;
}

const ThemeColorContext = createContext<ThemeColorContextType>({
  themeColor: 'blue',
  setThemeColor: () => {},
});

export const useThemeColor = () => useContext(ThemeColorContext);

// Función para aplicar colores del tema dinámicamente
const applyThemeColors = (color: string) => {
  const root = document.documentElement;
  
  const colorMap: Record<string, string> = {
    blue: '#2563eb',
    purple: '#7c3aed',
    green: '#16a34a',
    red: '#dc2626',
    orange: '#ea580c',
    pink: '#db2777',
    teal: '#0d9488',
    amber: '#d97706',
  };

  const primaryColor = colorMap[color] || colorMap.blue;
  
  // Aplicar variables CSS personalizadas
  root.style.setProperty('--color-primary', primaryColor);
  
  // Calcular variaciones automáticamente
  const hex = primaryColor.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);
  
  // Versión más oscura para hover
  const darkerR = Math.max(0, r - 30);
  const darkerG = Math.max(0, g - 30);
  const darkerB = Math.max(0, b - 30);
  const darkerColor = `rgb(${darkerR}, ${darkerG}, ${darkerB})`;
  
  root.style.setProperty('--color-primary-dark', darkerColor);
  
  // Versión más clara para backgrounds
  const lighterColor = `rgba(${r}, ${g}, ${b}, 0.1)`;
  root.style.setProperty('--color-primary-light', lighterColor);
  
  // Actualizar el valor de Tailwind CSS dinámicamente
  root.style.setProperty('--tw-color-primary', `${r} ${g} ${b}`);
};

function ThemeColorProvider({ children }: { children: ReactNode }) {
  const [themeColor, setThemeColorState] = useState('blue');

  const setThemeColor = (color: string) => {
    setThemeColorState(color);
    applyThemeColors(color);
    localStorage.setItem('theme-color', color);
  };

  useEffect(() => {
    // Cargar color guardado al inicializar
    const savedColor = localStorage.getItem('theme-color') || 'blue';
    setThemeColorState(savedColor);
    applyThemeColors(savedColor);
  }, []);

  return (
    <ThemeColorContext.Provider value={{ themeColor, setThemeColor }}>
      {children}
    </ThemeColorContext.Provider>
  );
}

export default function Provider({ children }: { children: ReactNode }) {
  return (
    <ClerkProvider
      publishableKey={process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY!}
      localization={esES}
      appearance={{
        variables: {
          colorPrimary: "#2563eb",
          borderRadius: "0.75rem",
          colorBackground: "#ffffff",
          colorText: "#1f2937",
          colorTextSecondary: "#6b7280",
          colorNeutral: "#f9fafb",
          colorInputBackground: "#ffffff",
          colorInputText: "#1f2937",
          fontFamily: "Inter, system-ui, sans-serif",
          fontSize: "0.875rem",
          fontWeight: {
            normal: 400,
            medium: 500,
            bold: 700
          }
        },
        layout: {
          logoImageUrl: "data:image/svg+xml;base64," + btoa(`
            <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="8" y="8" width="48" height="48" rx="12" fill="#2563eb"/>
              <svg x="20" y="20" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                <polyline points="9,22 9,12 15,12 15,22"/>
              </svg>
              <circle cx="52" cy="12" r="6" fill="#f97316"/>
            </svg>
          `),
          logoLinkUrl: "/",
          socialButtonsVariant: "blockButton",
          socialButtonsPlacement: "top"
        },
        elements: {
          formFieldInput: {
            borderRadius: "0.75rem",
            borderWidth: "1px",
            borderColor: "#d1d5db",
            backgroundColor: "#ffffff",
            fontSize: "0.875rem",
            padding: "0.75rem 1rem",
            "&:focus": {
              borderColor: "#2563eb",
              boxShadow: "0 0 0 3px rgba(37, 99, 235, 0.1)"
            }
          },
          formFieldLabel: {
            fontSize: "0.875rem",
            fontWeight: 500,
            color: "#374151",
            marginBottom: "0.5rem"
          },
          socialButtonsBlockButton: {
            borderRadius: "0.75rem",
            borderWidth: "1px",
            borderColor: "#d1d5db",
            backgroundColor: "#ffffff",
            fontSize: "0.875rem",
            fontWeight: 500,
            padding: "0.75rem 1rem",
            "&:hover": {
              backgroundColor: "#f9fafb",
              borderColor: "#9ca3af"
            }
          },
          socialButtonsBlockButtonText: {
            fontSize: "0.875rem",
            fontWeight: 500,
            color: "#374151"
          }
        }
      }}
      signInFallbackRedirectUrl="/dashboard"
      signUpFallbackRedirectUrl="/onboarding"
    >
      <ConvexProviderWithClerk client={convex} useAuth={useAuth}>
        <StoreUserEffect />
        <ThemeColorProvider>
          <PageWrapper>
            {children}
          </PageWrapper>
        </ThemeColorProvider>
      </ConvexProviderWithClerk>
    </ClerkProvider>
  );
}

