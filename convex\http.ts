import { httpRouter } from "convex/server";
import { httpAction } from "./_generated/server";
import { internal, api } from "./_generated/api";

const http = httpRouter();

// Endpoint para obtener todas las propiedades (para n8n)
http.route({
  path: "/api/properties",
  method: "GET",
  handler: httpAction(async (ctx, request) => {
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get("limit") || "50");
    const offset = parseInt(url.searchParams.get("offset") || "0");
    
    const properties = await ctx.runQuery(api.properties.getProperties, {
      limit,
      offset
    });
    
    return new Response(JSON.stringify({
      success: true,
      data: properties,
      pagination: {
        limit,
        offset,
        total: properties.length
      }
    }), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE",
        "Access-Control-Allow-Headers": "Content-Type, Authorization"
      }
    });
  })
});

// Endpoint para obtener una propiedad específica
http.route({
  path: "/api/properties/{id}",
  method: "GET", 
  handler: httpAction(async (ctx, request) => {
    const url = new URL(request.url);
    const id = url.pathname.split("/").pop();
    
    if (!id) {
      return new Response(JSON.stringify({
        success: false,
        error: "ID de propiedad requerido"
      }), { status: 400 });
    }
    
    try {
      // Validar que el ID no sea "undefined" o inválido
      if (!id || id === "undefined") {
        return new Response(JSON.stringify({
          success: false,
          error: "ID de propiedad no válido"
        }), { status: 400 });
      }

      const property = await ctx.runQuery(api.properties.getPropertyById, {
        id: id as any
      });
      
      if (!property) {
        return new Response(JSON.stringify({
          success: false,
          error: "Propiedad no encontrada"
        }), { status: 404 });
      }
      
      return new Response(JSON.stringify({
        success: true,
        data: property
      }), {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*"
        }
      });
    } catch (error) {
      return new Response(JSON.stringify({
        success: false,
        error: "Error interno del servidor"
      }), { status: 500 });
    }
  })
});

// Endpoint para crear propiedades (para n8n)
http.route({
  path: "/api/properties",
  method: "POST",
  handler: httpAction(async (ctx, request) => {
    try {
      const body = await request.json();
      
      // Validación básica
      if (!body.title || !body.price || !body.address) {
        return new Response(JSON.stringify({
          success: false,
          error: "Campos requeridos: title, price, address"
        }), { status: 400 });
      }
      
      const newProperty = await ctx.runMutation(api.properties.createProperty, {
        ...body,
        status: "draft" // Por defecto como borrador
      });
      
      return new Response(JSON.stringify({
        success: true,
        data: newProperty,
        message: "Propiedad creada exitosamente"
      }), {
        status: 201,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*"
        }
      });
    } catch (error) {
      return new Response(JSON.stringify({
        success: false,
        error: "Error procesando la solicitud"
      }), { status: 500 });
    }
  })
});

// TEMPORALMENTE DESHABILITADO - No necesario para MVP
// import { paymentWebhook } from "./subscriptions";
// http.route({
//     path: "/payments/webhook", 
//     method: "POST",
//     handler: paymentWebhook,
// });

// 🤖 ENDPOINT PARA ANÁLISIS DE CALIDAD DEL AGENTE
http.route({
  path: "/analyzeAgentResponse",
  method: "POST",
  handler: httpAction(async (ctx, request) => {
    try {
      // Validar autenticación usando el mismo sistema existente
      const apiKey = request.headers.get("x-api-key");
      const validApiKey = process.env.N8N_API_KEY;

      if (!validApiKey || apiKey !== validApiKey) {
        return new Response(JSON.stringify({
          success: false,
          error: "No autorizado - API Key inválida"
        }), {
          status: 401,
          headers: { "Content-Type": "application/json" }
        });
      }

      const requestBody = await request.json();
      const { chatId, userMessage, agentResponse, messageId } = requestBody;

      if (!chatId || !userMessage || !agentResponse || !messageId) {
        return new Response(JSON.stringify({
          success: false,
          error: "Faltan parámetros requeridos: chatId, userMessage, agentResponse, messageId"
        }), {
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }

      // Analizar la calidad de la conversación
      const analysis = await ctx.runAction(internal.agentImprovement.analyzeConversationQuality, {
        chatId,
        userMessage,
        agentResponse,
        messageId,
      });

      return new Response(JSON.stringify({
        success: true,
        analysis,
        timestamp: new Date().toISOString()
      }), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });

    } catch (error) {
      console.error("Error en análisis de agente:", error);
      return new Response(JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : "Error interno del servidor"
      }), {
        status: 500,
        headers: { "Content-Type": "application/json" }
      });
    }
  })
});

// 🤖 ENDPOINT PARA OBTENER SYSTEM MESSAGE DINÁMICO
http.route({
  path: "/getDynamicSystemMessage",
  method: "GET",
  handler: httpAction(async (ctx, request) => {
    try {
      // Validar autenticación usando el mismo sistema existente
      const apiKey = request.headers.get("x-api-key");
      const validApiKey = process.env.N8N_API_KEY;

      if (!validApiKey || apiKey !== validApiKey) {
        return new Response(JSON.stringify({
          success: false,
          error: "No autorizado - API Key inválida"
        }), {
          status: 401,
          headers: { "Content-Type": "application/json" }
        });
      }

      // Construir system message dinámico
      const systemMessageData = await ctx.runQuery(internal.agentImprovement.buildDynamicSystemMessage, {});

      return new Response(JSON.stringify({
        success: true,
        systemMessage: systemMessageData.systemMessage,
        rulesCount: systemMessageData.rulesCount,
        lastUpdated: systemMessageData.lastUpdated,
        timestamp: new Date().toISOString()
      }), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });

    } catch (error) {
      console.error("Error obteniendo system message dinámico:", error);
      return new Response(JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : "Error interno del servidor"
      }), {
        status: 500,
        headers: { "Content-Type": "application/json" }
      });
    }
  })
});

// Endpoint para el agente de WhatsApp - Búsqueda de propiedades
export const searchPropertiesForAgent = httpAction(async (ctx, request) => {
  try {
    // Validar autenticación
    if (!validateApiKey(request)) {
      return new Response(JSON.stringify({
        success: false,
        error: "No autorizado - API Key inválida"
      }), {
        status: 401,
        headers: { "Content-Type": "application/json" }
      });
    }

    const requestBody = await request.json();

    // Aceptar tanto searchQuery como searchCriteria para compatibilidad
    const rawSearchCriteria = requestBody.searchQuery ?
      { searchText: requestBody.searchQuery } :
      (requestBody.searchCriteria || {});
    
    // Validar que hay criterios de búsqueda
    if (!rawSearchCriteria && !requestBody.searchQuery) {
      return new Response(JSON.stringify({
        success: false,
        error: "Criterios de búsqueda requeridos"
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }

    console.log("Criterios RAW desde N8N:", rawSearchCriteria);

    // Búsqueda simple por texto - deja que la base de datos haga el trabajo
    let actualSearchCriteria = rawSearchCriteria;
    
    if (rawSearchCriteria.searchQuery) {
      // Búsqueda directa por texto en todos los campos
      actualSearchCriteria = {
        searchText: rawSearchCriteria.searchQuery
      };
      console.log("Búsqueda por texto:", actualSearchCriteria);
    } else if (rawSearchCriteria.searchCriteria) {
      // Fallback si viene el formato anterior
      try {
        if (typeof rawSearchCriteria.searchCriteria === 'string') {
          actualSearchCriteria = JSON.parse(rawSearchCriteria.searchCriteria);
        } else {
          actualSearchCriteria = rawSearchCriteria.searchCriteria;
        }
      } catch (error) {
        actualSearchCriteria = { searchText: rawSearchCriteria.searchCriteria };
      }
    }

    // Función para limpiar valores que pueden venir de N8N
    const cleanSearchValue = (value: any): any => {
      // Si es null o undefined, retorna undefined
      if (value === null || value === undefined) {
        return undefined;
      }
      
      // Si es string
      if (typeof value === 'string') {
        const trimmed = value.trim();
        // Si está vacío, es "null", "undefined", o contiene solo espacios
        if (trimmed === '' || trimmed === 'null' || trimmed === 'undefined') {
          return undefined;
        }
        return trimmed;
      }
      
      // Si es número, verificar que sea válido
      if (typeof value === 'number') {
        return isNaN(value) ? undefined : value;
      }
      
      return value;
    };

    // Limpiar y construir criterios válidos
    const searchCriteria: any = {};
    
    Object.entries(actualSearchCriteria).forEach(([key, value]) => {
      const cleanedValue = cleanSearchValue(value);
      if (cleanedValue !== undefined) {
        searchCriteria[key] = cleanedValue;
      }
    });

    console.log("Criterios después de limpieza:", searchCriteria);

    // Convertir campos numéricos a número
    const numericFields = ['minPrice', 'maxPrice', 'bedrooms', 'bathrooms', 'minArea', 'maxArea', 'parking', 'builtYear'];
    numericFields.forEach(field => {
      if (field in searchCriteria && typeof searchCriteria[field] === 'string') {
        const numValue = parseFloat(searchCriteria[field] as string);
        // Si no es un número válido, eliminar el campo
        if (isNaN(numValue)) {
          delete searchCriteria[field];
        } else {
          searchCriteria[field] = numValue;
        }
      }
    });
    
    // Procesar amenidades si existen
    if ('amenities' in searchCriteria && typeof searchCriteria.amenities === 'string') {
      searchCriteria.amenities = searchCriteria.amenities.split(',').map((a: string) => a.trim());
    }
    
    console.log("Criterios de búsqueda procesados:", searchCriteria);

    // Usar búsqueda básica estructurada
    const results = await ctx.runQuery(internal.properties.searchForAgent, { searchCriteria });

    return new Response(JSON.stringify({
      success: true,
      properties: results.properties,
      total: results.total,
      searchCriteria: searchCriteria
    }), {
      headers: { "Content-Type": "application/json" }
    });

  } catch (error) {
    console.error("Error en búsqueda de propiedades:", error);
    return new Response(JSON.stringify({
      success: false,
      error: "Error interno del servidor"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
});

// Endpoint para obtener detalles completos de una propiedad
export const getPropertyDetailsForAgent = httpAction(async (ctx, request) => {
  try {
    // Validar autenticación
    if (!validateApiKey(request)) {
      return new Response(JSON.stringify({
        success: false,
        error: "No autorizado - API Key inválida"
      }), {
        status: 401,
        headers: { "Content-Type": "application/json" }
      });
    }

    const { propertyId } = await request.json();

    if (!propertyId) {
      return new Response(JSON.stringify({
        success: false,
        error: "ID de propiedad requerido"
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }

    // Llamar a la función interna para obtener detalles
    const result = await ctx.runQuery(internal.properties.getDetailsForAgent, { propertyId });

    if (!result.property) {
      return new Response(JSON.stringify({
        success: false,
        error: "Propiedad no encontrada"
      }), {
        status: 404,
        headers: { "Content-Type": "application/json" }
      });
    }

    return new Response(JSON.stringify({
      success: true,
      property: result.property
    }), {
      headers: { "Content-Type": "application/json" }
    });

  } catch (error) {
    console.error("Error obteniendo detalles de propiedad:", error);
    return new Response(JSON.stringify({
      success: false,
      error: "Error interno del servidor"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
});

// Endpoint para obtener imágenes de una propiedad específica para el agente
export const getPropertyImagesForAgent = httpAction(async (ctx, request) => {
  try {
    // Validar autenticación
    if (!validateApiKey(request)) {
      return new Response(JSON.stringify({
        success: false,
        error: "No autorizado - API Key inválida"
      }), {
        status: 401,
        headers: { "Content-Type": "application/json" }
      });
    }

    const { propertyId } = await request.json();

    if (!propertyId) {
      return new Response(JSON.stringify({
        success: false,
        error: "ID de propiedad requerido"
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }

    // Obtener la propiedad
    const property = await ctx.runQuery(internal.properties.getPropertyInternal, { id: propertyId });

    if (!property) {
      return new Response(JSON.stringify({
        success: false,
        error: "Propiedad no encontrada"
      }), {
        status: 404,
        headers: { "Content-Type": "application/json" }
      });
    }

    // Preparar respuesta con información de imágenes
    const images = property.images || [];
    const hasImages = images.length > 0;

    return new Response(JSON.stringify({
      success: true,
      property: {
        id: property._id,
        title: property.title,
        address: property.address,
        hasImages,
        totalImages: images.length,
        images: images,
        mainImage: images[0] || null,
        // URLs optimizadas para diferentes tamaños
        optimizedImages: images.map(url => ({
          original: url,
          thumbnail: url.replace('/upload/', '/upload/w_300,h_200,c_fill/'),
          medium: url.replace('/upload/', '/upload/w_600,h_400,c_fill/'),
          large: url.replace('/upload/', '/upload/w_1200,h_800,c_fill/')
        }))
      }
    }), {
      headers: { "Content-Type": "application/json" }
    });

  } catch (error) {
    console.error("Error obteniendo imágenes de propiedad:", error);
    return new Response(JSON.stringify({
      success: false,
      error: "Error interno del servidor"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
});

// Función para validar API Key
function validateApiKey(request: Request): boolean {
  const apiKey = request.headers.get("x-api-key");
  const validApiKey = process.env.N8N_API_KEY;

  if (!validApiKey) {
    console.error("N8N_API_KEY no está configurada en las variables de entorno");
    return false;
  }

  return apiKey === validApiKey;
}

// Función para ejecutar pruebas simples del motor de búsqueda
async function runSimpleSearchTests(ctx: any) {
  console.log("🧪 Iniciando test simple del motor de búsqueda...");

  const testCases = [
    // Pruebas básicas de tipo
    {
      id: "test_01",
      query: "Busco un depa en zona 14",
      description: "Test básico: depa → apartment"
    },
    {
      id: "test_02",
      query: "casa en zona 10",
      description: "Test básico: casa → house"
    },

    // Pruebas de rangos de precios
    {
      id: "test_03",
      query: "apartamento para comprar zona 14 entre 150k y 350k",
      description: "Test rango de precios con 'k'"
    },
    {
      id: "test_04",
      query: "casa hasta 500000 dolares zona 15",
      description: "Test precio máximo en dólares"
    },
    {
      id: "test_05",
      query: "depa desde 200000 hasta 400000 zona 10",
      description: "Test rango completo en números"
    },
    {
      id: "test_06",
      query: "apartamento máximo 250k zona 14",
      description: "Test precio máximo con 'k'"
    },

    // Pruebas de variaciones de zona
    {
      id: "test_07",
      query: "casa en z 15",
      description: "Test formato 'z 15' → 'Zona 15'"
    },
    {
      id: "test_08",
      query: "depa zona14",
      description: "Test formato 'zona14' sin espacios"
    },

    // Pruebas de intención (comprar vs alquilar)
    {
      id: "test_09",
      query: "apartamento para alquilar zona 15",
      description: "Test intención de alquiler"
    },
    {
      id: "test_10",
      query: "casa en venta zona 10",
      description: "Test intención de venta"
    },

    // Pruebas de variaciones de palabras
    {
      id: "test_11",
      query: "departamento en zona 14",
      description: "Test 'departamento' → apartment"
    },
    {
      id: "test_12",
      query: "apto zona 10 para comprar",
      description: "Test 'apto' → apartment"
    },

    // Pruebas complejas
    {
      id: "test_13",
      query: "depa 3 habitaciones zona 14 para comprar 200k",
      description: "Test complejo: tipo + habitaciones + zona + intención + precio"
    },
    {
      id: "test_14",
      query: "casa moderna zona 10 con piscina hasta 600k",
      description: "Test con amenidades y precio"
    },

    // Pruebas de búsqueda general
    {
      id: "test_15",
      query: "propiedades destacadas zona 14",
      description: "Test búsqueda de propiedades destacadas/premium"
    },
    {
      id: "test_16",
      query: "mejores propiedades zona 10",
      description: "Test búsqueda de mejores propiedades"
    }
  ];

  const results: any[] = [];

  for (const testCase of testCases) {
    console.log(`\n🔍 Test ${testCase.id}: ${testCase.description}`);
    console.log(`Query: "${testCase.query}"`);

    try {
      // Test básico de búsqueda estructurada
      const searchResult = await ctx.runQuery(internal.properties.searchForAgent, {
        searchCriteria: { searchText: testCase.query }
      });

      const result = {
        testId: testCase.id,
        query: testCase.query,
        description: testCase.description,
        basicSearch: {
          success: true,
          propertiesFound: searchResult.properties?.length || 0,
          properties: searchResult.properties?.slice(0, 2).map((p: any) => ({
            id: p.id,
            title: p.title || 'Sin título',
            type: p.type,
            price: p.price,
            currency: p.currency,
            zone: p.address?.includes('Zona') ? p.address.match(/Zona \d+/)?.[0] : 'N/A',
            featured: p.featured || false
          })) || [],
          error: null,
          searchType: 'basic'
        }
      };

      results.push(result);

    } catch (error) {
      results.push({
        testId: testCase.id,
        query: testCase.query,
        error: error instanceof Error ? error.message : "Error desconocido"
      });
    }
  }

  // Generar resumen
  const successful = results.filter(r => !r.error && r.basicSearch?.success);
  const failed = results.filter(r => r.error || !r.basicSearch?.success);

  const summary = {
    totalTests: results.length,
    successful: successful.length,
    failed: failed.length,
    successRate: `${((successful.length / results.length) * 100).toFixed(1)}%`,
    results: results
  };

  console.log(`\n🎯 RESUMEN:`);
  console.log(`Total: ${summary.totalTests}`);
  console.log(`Exitosos: ${summary.successful}`);
  console.log(`Fallidos: ${summary.failed}`);
  console.log(`Tasa de éxito: ${summary.successRate}`);

  return summary;
}

// Endpoint para guardar conversaciones de WhatsApp
export const saveConversation = httpAction(async (ctx, request) => {
  try {
    // Validar autenticación
    if (!validateApiKey(request)) {
      return new Response(JSON.stringify({
        success: false,
        error: "No autorizado - API Key inválida"
      }), {
        status: 401,
        headers: { "Content-Type": "application/json" }
      });
    }

    const { chatId, userName, messageId, messageType, content, timestamp } = await request.json();
    
    if (!chatId || !messageId || !messageType || !content) {
      return new Response(JSON.stringify({
        success: false,
        error: "Campos requeridos: chatId, messageId, messageType, content"
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }

    // Guardar conversación en Convex
    const conversationId = await ctx.runMutation(api.conversations.saveMessage, {
      chatId,
      userName: userName || "Usuario",
      messageId,
      messageType,
      content,
      timestamp: timestamp || new Date().toISOString()
    });

    return new Response(JSON.stringify({
      success: true,
      conversationId,
      message: "Conversación guardada exitosamente"
    }), {
      headers: { "Content-Type": "application/json" }
    });

  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: "Error interno del servidor"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
});

// Endpoint para generar reportes semanales
export const generateWeeklyReport = httpAction(async (ctx, request) => {
  try {
    // Validar autenticación
    if (!validateApiKey(request)) {
      return new Response(JSON.stringify({
        success: false,
        error: "No autorizado - API Key inválida"
      }), {
        status: 401,
        headers: { "Content-Type": "application/json" }
      });
    }

    const { startDate, endDate } = await request.json();
    
    if (!startDate || !endDate) {
      return new Response(JSON.stringify({
        success: false,
        error: "Campos requeridos: startDate, endDate"
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }

    // Generar reporte
    const report = await ctx.runQuery(api.conversations.generateWeeklyReport, {
      startDate,
      endDate
    });

    return new Response(JSON.stringify({
      success: true,
      report
    }), {
      headers: { "Content-Type": "application/json" }
    });

  } catch (error) {
    console.error("Error generando reporte:", error);
    return new Response(JSON.stringify({
      success: false,
      error: "Error interno del servidor"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
});

// Endpoint para obtener historial de conversaciones para el agente
export const getChatHistoryForAgent = httpAction(async (ctx, request) => {
  try {
    // Validar autenticación
    if (!validateApiKey(request)) {
      return new Response(JSON.stringify({
        success: false,
        error: "No autorizado - API Key inválida"
      }), {
        status: 401,
        headers: { "Content-Type": "application/json" }
      });
    }

    const { chatId, limit } = await request.json();
    
    if (!chatId) {
      return new Response(JSON.stringify({
        success: false,
        error: "ChatId requerido"
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }

    // Obtener conversaciones del usuario
    const conversations = await ctx.runQuery(api.conversations.getChatHistory, {
      chatId,
      limit: limit || 10
    });

    // Obtener perfil del lead si existe
    const leadProfile = await ctx.runQuery(api.conversations.getLeadProfile, {
      chatId
    });

    return new Response(JSON.stringify({
      success: true,
      conversations,
      leadProfile,
      chatId
    }), {
      headers: { "Content-Type": "application/json" }
    });

  } catch (error) {
    console.error("Error obteniendo historial:", error);
    return new Response(JSON.stringify({
      success: false,
      error: "Error interno del servidor"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
});

// Registrar endpoints para el agente de WhatsApp en el router
http.route({
  path: "/searchPropertiesForAgent",
  method: "POST",
  handler: searchPropertiesForAgent
});

http.route({
  path: "/getPropertyDetailsForAgent", 
  method: "POST",
  handler: getPropertyDetailsForAgent
});

// Endpoint para asignar propiedad a agente (admin)
export const assignPropertyToAgentAdmin = httpAction(async (ctx, request) => {
  try {
    const { propertyId, agentId } = await request.json();
    
    if (!propertyId || !agentId) {
      return new Response(JSON.stringify({
        success: false,
        error: "Property ID y Agent ID son requeridos"
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }

    // Llamar a la función interna de asignación
    const result = await ctx.runAction(internal.properties.adminAssignPropertyToAgent, {
      propertyId,
      agentId
    });

    return new Response(JSON.stringify({
      success: true,
      message: result.message,
      propertyId: result.propertyId,
      agentId: result.agentId
    }), {
      headers: { "Content-Type": "application/json" }
    });

  } catch (error) {
    console.error("Error asignando propiedad:", error);
    return new Response(JSON.stringify({
      success: false,
      error: "Error interno del servidor"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
});

http.route({
  path: "/assignPropertyToAgent", 
  method: "POST",
  handler: assignPropertyToAgentAdmin
});

// Registrar nuevos endpoints
http.route({
  path: "/saveConversation",
  method: "POST",
  handler: saveConversation
});

http.route({
  path: "/generateWeeklyReport",
  method: "POST", 
  handler: generateWeeklyReport
});

// Registrar nuevo endpoint
http.route({
  path: "/getChatHistoryForAgent",
  method: "POST",
  handler: getChatHistoryForAgent
});

// Endpoint para verificar disponibilidad de propiedades
export const checkPropertyAvailability = httpAction(async (ctx, request) => {
  try {
    // Validar autenticación
    if (!validateApiKey(request)) {
      return new Response(JSON.stringify({
        success: false,
        error: "No autorizado - API Key inválida"
      }), {
        status: 401,
        headers: { "Content-Type": "application/json" }
      });
    }

    const { propertyId, preferredDates, duration } = await request.json();

    if (!propertyId) {
      return new Response(JSON.stringify({
        success: false,
        error: "propertyId es requerido"
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }

    const result = await ctx.runQuery(api.aiAppointments.checkPropertyAvailability, {
      propertyId,
      preferredDates: preferredDates || [],
      duration: duration || 60
    });

    return new Response(JSON.stringify({
      success: true,
      data: result
    }), {
      headers: { "Content-Type": "application/json" }
    });

  } catch (error) {
    console.error("Error verificando disponibilidad:", error);
    return new Response(JSON.stringify({
      success: false,
      error: "Error interno del servidor"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
});

// Endpoint para crear solicitud de cita
export const createAppointmentRequest = httpAction(async (ctx, request) => {
  try {
    // Validar autenticación
    if (!validateApiKey(request)) {
      return new Response(JSON.stringify({
        success: false,
        error: "No autorizado - API Key inválida"
      }), {
        status: 401,
        headers: { "Content-Type": "application/json" }
      });
    }

    const appointmentData = await request.json();

    if (!appointmentData.propertyId || !appointmentData.guestName || !appointmentData.guestEmail) {
      return new Response(JSON.stringify({
        success: false,
        error: "propertyId, guestName y guestEmail son requeridos"
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }

    // Validar formato de fechas
    if (appointmentData.requestedStartTime) {
      const startDate = new Date(appointmentData.requestedStartTime);
      if (isNaN(startDate.getTime())) {
        return new Response(JSON.stringify({
          success: false,
          error: "requestedStartTime debe ser una fecha válida en formato ISO"
        }), {
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }
    }

    if (appointmentData.requestedEndTime) {
      const endDate = new Date(appointmentData.requestedEndTime);
      if (isNaN(endDate.getTime())) {
        return new Response(JSON.stringify({
          success: false,
          error: "requestedEndTime debe ser una fecha válida en formato ISO"
        }), {
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }
    }

    const result = await ctx.runMutation(api.aiAppointments.createAIAppointmentRequest, appointmentData);

    return new Response(JSON.stringify({
      success: true,
      data: result
    }), {
      headers: { "Content-Type": "application/json" }
    });

  } catch (error) {
    console.error("Error creando solicitud de cita:", error);
    return new Response(JSON.stringify({
      success: false,
      error: "Error interno del servidor"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
});

// Endpoint para consultar estado de citas
export const consultarEstadoCita = httpAction(async (ctx, request) => {
  try {
    // Validar autenticación
    if (!validateApiKey(request)) {
      return new Response(JSON.stringify({
        success: false,
        error: "No autorizado - API Key inválida"
      }), {
        status: 401,
        headers: { "Content-Type": "application/json" }
      });
    }

    const searchCriteria = await request.json();

    const result = await ctx.runQuery(api.aiAppointments.consultarEstadoCita, searchCriteria);

    return new Response(JSON.stringify({
      success: true,
      data: result
    }), {
      headers: { "Content-Type": "application/json" }
    });

  } catch (error) {
    console.error("Error consultando estado de cita:", error);
    return new Response(JSON.stringify({
      success: false,
      error: "Error interno del servidor"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
});

// Endpoint para obtener contexto de propiedad
export const getPropertyContextForAI = httpAction(async (ctx, request) => {
  try {
    // Validar autenticación
    if (!validateApiKey(request)) {
      return new Response(JSON.stringify({
        success: false,
        error: "No autorizado - API Key inválida"
      }), {
        status: 401,
        headers: { "Content-Type": "application/json" }
      });
    }

    const { propertyId } = await request.json();

    if (!propertyId) {
      return new Response(JSON.stringify({
        success: false,
        error: "propertyId es requerido"
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }

    const result = await ctx.runQuery(api.aiAppointments.getPropertyContextForAI, { propertyId });

    return new Response(JSON.stringify({
      success: true,
      data: result
    }), {
      headers: { "Content-Type": "application/json" }
    });

  } catch (error) {
    console.error("Error obteniendo contexto de propiedad:", error);
    return new Response(JSON.stringify({
      success: false,
      error: "Error interno del servidor"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
});

// Registrar endpoints de citas
http.route({
  path: "/checkPropertyAvailability",
  method: "POST",
  handler: checkPropertyAvailability
});

http.route({
  path: "/createAppointmentRequest",
  method: "POST",
  handler: createAppointmentRequest
});

// Endpoint para diagnosticar datos de propiedades y usuarios
export const diagnosePropertyData = httpAction(async (ctx, request) => {
  try {
    // Validar autenticación
    if (!validateApiKey(request)) {
      return new Response(JSON.stringify({
        success: false,
        error: "No autorizado - API Key inválida"
      }), {
        status: 401,
        headers: { "Content-Type": "application/json" }
      });
    }

    const url = new URL(request.url);
    const propertyId = url.searchParams.get("propertyId");
    const limit = url.searchParams.get("limit");

    const result = await ctx.runQuery(api.aiAppointments.diagnosePropertyUserData, {
      propertyId: propertyId ? propertyId as any : undefined,
      limit: limit ? parseInt(limit) : undefined
    });

    return new Response(JSON.stringify({
      success: true,
      data: result
    }), {
      headers: { "Content-Type": "application/json" }
    });

  } catch (error) {
    console.error("Error diagnosticando datos:", error);
    return new Response(JSON.stringify({
      success: false,
      error: "Error interno del servidor"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
});

http.route({
  path: "/diagnosePropertyData",
  method: "GET",
  handler: diagnosePropertyData
});

http.route({
  path: "/consultarEstadoCita",
  method: "POST",
  handler: consultarEstadoCita
});

http.route({
  path: "/getPropertyContextForAI",
  method: "POST",
  handler: getPropertyContextForAI
});

// Endpoint para verificar duplicados de citas
export const checkAppointmentDuplicates = httpAction(async (ctx, request) => {
  try {
    // Validar autenticación
    if (!validateApiKey(request)) {
      return new Response(JSON.stringify({
        success: false,
        error: "No autorizado - API Key inválida"
      }), {
        status: 401,
        headers: { "Content-Type": "application/json" }
      });
    }

    const { propertyId, guestEmail, requestedStartTime } = await request.json();

    if (!propertyId || !guestEmail || !requestedStartTime) {
      return new Response(JSON.stringify({
        success: false,
        error: "propertyId, guestEmail y requestedStartTime son requeridos"
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }

    const result = await ctx.runQuery(api.aiAppointments.checkAppointmentDuplicates, {
      propertyId,
      guestEmail,
      requestedStartTime
    });

    return new Response(JSON.stringify({
      success: true,
      data: result
    }), {
      headers: { "Content-Type": "application/json" }
    });

  } catch (error) {
    console.error("Error verificando duplicados:", error);
    return new Response(JSON.stringify({
      success: false,
      error: "Error interno del servidor"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
});

http.route({
  path: "/checkAppointmentDuplicates",
  method: "POST",
  handler: checkAppointmentDuplicates
});

// === ENDPOINT PARA TESTING DE EMAILS ===

// 🧪 Endpoint para probar configuración de emails
export const testEmailConfig = httpAction(async (ctx, request) => {
  // Verificar método
  if (request.method !== "POST") {
    return new Response("Method not allowed", { status: 405 });
  }

  try {
    const { testEmail } = await request.json();

    if (!testEmail) {
      return new Response(JSON.stringify({
        success: false,
        error: "testEmail es requerido"
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }

    // Ejecutar la función de testing
    const result = await ctx.runAction(api.emails.testEmailConfiguration, {
      testEmail
    });

    return new Response(JSON.stringify({
      success: true,
      data: result
    }), {
      headers: { "Content-Type": "application/json" }
    });

  } catch (error) {
    console.error("Error en test de email:", error);
    return new Response(JSON.stringify({
      success: false,
      error: error instanceof Error ? error.message : "Error desconocido"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
});

// Registrar endpoint de testing de emails
http.route({
  path: "/testEmailConfig",
  method: "POST",
  handler: testEmailConfig
});

// === ENDPOINTS PARA SISTEMA DE APRENDIZAJE AUTOMÁTICO ===

// ❌ TEMPORALMENTE COMENTADO - Funciones que usaban agentLearning (tabla eliminada)
/*
// Obtener conversaciones no analizadas
export const getUnanalyzedConversations = httpAction(async (ctx, request) => {
  // ... función comentada temporalmente
});

// Guardar análisis de IA
export const saveAIAnalysis = httpAction(async (ctx, request) => {
  // ... función comentada temporalmente
});

// Obtener aprendizajes activos
export const getActiveLearnings = httpAction(async (ctx, request) => {
  // ... función comentada temporalmente
});

http.route({
  path: "/getUnanalyzedConversations",
  method: "POST",
  handler: getUnanalyzedConversations
});

http.route({
  path: "/saveAIAnalysis",
  method: "POST",
  handler: saveAIAnalysis
});

http.route({
  path: "/getActiveLearnings",
  method: "POST",
  handler: getActiveLearnings
});
*/

// Endpoint para obtener conversaciones para análisis de IA
export const getConversationsForAnalysis = httpAction(async (ctx, request) => {
  try {
    // Validar autenticación
    if (!validateApiKey(request)) {
      return new Response(JSON.stringify({
        success: false,
        error: "API key inválida"
      }), {
        status: 401,
        headers: { "Content-Type": "application/json" }
      });
    }

    const { limit, startDate, endDate } = await request.json();

    const result = await ctx.runQuery(api.conversations.getAllConversationsForAnalysis, {
      limit,
      startDate,
      endDate
    });

    return new Response(JSON.stringify({
      success: true,
      data: result
    }), {
      headers: { "Content-Type": "application/json" }
    });

  } catch (error) {
    console.error("Error obteniendo conversaciones:", error);
    return new Response(JSON.stringify({
      success: false,
      error: "Error interno del servidor"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
});

http.route({
  path: "/getConversationsForAnalysis",
  method: "POST",
  handler: getConversationsForAnalysis
});

// === ENDPOINTS PARA KNOWLEDGE BASE ===

// Endpoint para guardar consideraciones en la base de conocimiento
export const saveKnowledgeBaseConsiderations = httpAction(async (ctx, request) => {
  try {
    // Validar autenticación
    if (!validateApiKey(request)) {
      return new Response(JSON.stringify({
        success: false,
        error: "API key inválida"
      }), {
        status: 401,
        headers: { "Content-Type": "application/json" }
      });
    }

    const body = await request.json();
    const { considerations, skipDuplicateCheck = false } = body;

    // Validar que se envíen consideraciones
    if (!considerations || !Array.isArray(considerations) || considerations.length === 0) {
      return new Response(JSON.stringify({
        success: false,
        error: "Se requiere un array de consideraciones"
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }

    // Validar estructura de cada consideración
    for (const consideration of considerations) {
      if (!consideration.consideration || typeof consideration.consideration !== 'string') {
        return new Response(JSON.stringify({
          success: false,
          error: "Cada consideración debe tener un campo 'consideration' de tipo string"
        }), {
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }

      if (consideration.priority && !['high', 'medium', 'low'].includes(consideration.priority)) {
        return new Response(JSON.stringify({
          success: false,
          error: "La prioridad debe ser 'high', 'medium' o 'low'"
        }), {
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }
    }

    // Procesar consideraciones
    const result = await ctx.runMutation(api.knowledgeBase.createMultipleConsiderations, {
      considerations: considerations.map(c => ({
        consideration: c.consideration,
        category: c.category || "ai_analysis",
        priority: c.priority || "medium",
        sourceAnalysis: c.sourceAnalysis || []
      })),
      skipDuplicateCheck
    });

    return new Response(JSON.stringify({
      success: true,
      message: "Consideraciones procesadas exitosamente",
      data: result
    }), {
      headers: { "Content-Type": "application/json" }
    });

  } catch (error) {
    console.error("Error guardando consideraciones:", error);
    return new Response(JSON.stringify({
      success: false,
      error: error instanceof Error ? error.message : "Error interno del servidor"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
});

// Endpoint para obtener consideraciones activas
export const getActiveKnowledgeBase = httpAction(async (ctx, request) => {
  try {
    // Validar autenticación
    if (!validateApiKey(request)) {
      return new Response(JSON.stringify({
        success: false,
        error: "API key inválida"
      }), {
        status: 401,
        headers: { "Content-Type": "application/json" }
      });
    }

    const { category, priority, limit } = await request.json();

    const considerations = await ctx.runQuery(api.knowledgeBase.getActiveConsiderations, {
      category,
      priority,
      limit
    });

    return new Response(JSON.stringify({
      success: true,
      data: considerations
    }), {
      headers: { "Content-Type": "application/json" }
    });

  } catch (error) {
    console.error("Error obteniendo consideraciones:", error);
    return new Response(JSON.stringify({
      success: false,
      error: "Error interno del servidor"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
});

// Endpoint para obtener estadísticas de la base de conocimiento
export const getKnowledgeBaseStats = httpAction(async (ctx, request) => {
  try {
    // Validar autenticación
    if (!validateApiKey(request)) {
      return new Response(JSON.stringify({
        success: false,
        error: "API key inválida"
      }), {
        status: 401,
        headers: { "Content-Type": "application/json" }
      });
    }

    const stats = await ctx.runQuery(api.knowledgeBase.getKnowledgeBaseStats);

    return new Response(JSON.stringify({
      success: true,
      data: stats
    }), {
      headers: { "Content-Type": "application/json" }
    });

  } catch (error) {
    console.error("Error obteniendo estadísticas:", error);
    return new Response(JSON.stringify({
      success: false,
      error: "Error interno del servidor"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
});

// Registrar rutas de Knowledge Base
http.route({
  path: "/storeConsiderations",
  method: "POST",
  handler: saveKnowledgeBaseConsiderations
});

http.route({
  path: "/getActiveKnowledgeBase",
  method: "POST",
  handler: getActiveKnowledgeBase
});

http.route({
  path: "/getKnowledgeBaseStats",
  method: "POST",
  handler: getKnowledgeBaseStats
});

// 🧪 ENDPOINT PARA PRUEBAS DEL MOTOR DE BÚSQUEDA (DESHABILITADO)
export const runSearchEngineTests = httpAction(async (ctx, request) => {
  try {
    // Validar autenticación
    if (!validateApiKey(request)) {
      return new Response(JSON.stringify({
        success: false,
        error: "No autorizado - API Key inválida"
      }), {
        status: 401,
        headers: { "Content-Type": "application/json" }
      });
    }

    // Test deshabilitado temporalmente
    const testResults = {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      results: [],
      summary: "Test complejo deshabilitado temporalmente - usar testSearchEngineSimple"
    };

    return new Response(JSON.stringify({
      success: true,
      ...testResults
    }), {
      headers: { "Content-Type": "application/json" }
    });

  } catch (error) {
    console.error("Error ejecutando pruebas:", error);
    return new Response(JSON.stringify({
      success: false,
      error: "Error interno del servidor"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
});

http.route({
  path: "/runSearchEngineTests",
  method: "POST",
  handler: runSearchEngineTests,
});

// 🧪 ENDPOINT SIMPLE PARA PRUEBAS DEL MOTOR DE BÚSQUEDA
export const testSearchEngineSimple = httpAction(async (ctx, request) => {
  try {
    // Validar autenticación
    if (!validateApiKey(request)) {
      return new Response(JSON.stringify({
        success: false,
        error: "No autorizado - API Key inválida"
      }), {
        status: 401,
        headers: { "Content-Type": "application/json" }
      });
    }

    // Ejecutar pruebas directamente aquí
    const testResults = await runSimpleSearchTests(ctx);

    return new Response(JSON.stringify({
      success: true,
      ...testResults
    }), {
      headers: { "Content-Type": "application/json" }
    });

  } catch (error) {
    console.error("Error ejecutando pruebas simples:", error);
    return new Response(JSON.stringify({
      success: false,
      error: error instanceof Error ? error.message : "Error interno del servidor"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
});

http.route({
  path: "/testSearchEngineSimple",
  method: "POST",
  handler: testSearchEngineSimple,
});

// === ENDPOINTS PARA MODIFICACIÓN DE CITAS ===

// Endpoint para actualizar cita
export const updateAppointment = httpAction(async (ctx, request) => {
  try {
    // Validar autenticación
    if (!validateApiKey(request)) {
      return new Response(JSON.stringify({
        success: false,
        error: "No autorizado - API Key inválida"
      }), {
        status: 401,
        headers: { "Content-Type": "application/json" }
      });
    }

    const updateData = await request.json();

    if (!updateData.appointmentId) {
      return new Response(JSON.stringify({
        success: false,
        error: "appointmentId es requerido"
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }

    const result = await ctx.runMutation(api.appointments.updateAppointment, updateData);

    return new Response(JSON.stringify({
      success: true,
      data: result,
      message: "Cita actualizada exitosamente"
    }), {
      headers: { "Content-Type": "application/json" }
    });

  } catch (error) {
    console.error("Error actualizando cita:", error);
    return new Response(JSON.stringify({
      success: false,
      error: "Error interno del servidor"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
});

// Endpoint para cancelar cita
export const cancelAppointmentHttp = httpAction(async (ctx, request) => {
  try {
    // Validar autenticación
    if (!validateApiKey(request)) {
      return new Response(JSON.stringify({
        success: false,
        error: "No autorizado - API Key inválida"
      }), {
        status: 401,
        headers: { "Content-Type": "application/json" }
      });
    }

    const cancelData = await request.json();

    if (!cancelData.appointmentId) {
      return new Response(JSON.stringify({
        success: false,
        error: "appointmentId es requerido"
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }

    const result = await ctx.runMutation(api.appointments.cancelAppointment, cancelData);

    return new Response(JSON.stringify({
      success: true,
      data: result,
      message: "Cita cancelada exitosamente"
    }), {
      headers: { "Content-Type": "application/json" }
    });

  } catch (error) {
    console.error("Error cancelando cita:", error);
    return new Response(JSON.stringify({
      success: false,
      error: "Error interno del servidor"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
});

// Registrar endpoints de modificación de citas
http.route({
  path: "/updateAppointment",
  method: "POST",
  handler: updateAppointment
});

http.route({
  path: "/cancelAppointment",
  method: "POST",
  handler: cancelAppointmentHttp
});

// Convex expects the router to be the default export of `convex/http.js`.
export default http;