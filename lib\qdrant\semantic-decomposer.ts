/**
 * Semantic Query Decomposer
 * 
 * Descompone consultas de búsqueda en componentes semánticos específicos
 * para generar embeddings especializados por categoría.
 */

import OpenAI from 'openai';

// Tipos para la descomposición semántica
export interface DecomposedQuery {
  location: string;
  property: string;
  amenities: string;
  price: string;
  intent: string;
  priceContext: string;
  characteristics: {
    bedrooms?: number;
    bathrooms?: number;
    area?: number;
  };
  original: string;
}

export interface DecompositionResult {
  decomposed: DecomposedQuery;
  confidence: number;
  detectedProfile: string;
  processingTime: number;
}

/**
 * Clase para descomponer consultas en componentes semánticos
 */
export class SemanticQueryDecomposer {
  private openai: OpenAI;
  private model: string;

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
    this.model = process.env.AI_MODEL_CRITERIA_EXTRACTION || 'gpt-3.5-turbo';
  }

  /**
   * Descompone una consulta en sus componentes semánticos
   */
  async decompose(query: string): Promise<DecompositionResult> {
    const startTime = Date.now();
    
    try {
      // Validar entrada
      if (!query || query.trim().length === 0) {
        return this.createEmptyResult(query, Date.now() - startTime);
      }

      // Preparar prompt para descomposición
      const prompt = this.buildDecompositionPrompt(query);
      
      // Llamar a OpenAI
      const response = await this.openai.chat.completions.create({
        model: this.model,
        messages: [
          {
            role: 'system',
            content: 'Eres un experto en análisis de consultas inmobiliarias. Descompón consultas en componentes semánticos específicos.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.1,
        max_tokens: 500,
      });

      // Procesar respuesta
      const result = this.parseDecompositionResponse(
        response.choices[0].message.content || '{}',
        query,
        Date.now() - startTime
      );

      console.log(`🔍 Query decomposed: "${query}" -> ${JSON.stringify(result.decomposed)}`);
      return result;

    } catch (error) {
      console.error('Error decomposing query:', error);
      return this.createEmptyResult(query, Date.now() - startTime);
    }
  }

  /**
   * Construye el prompt para la descomposición
   */
  private buildDecompositionPrompt(query: string): string {
    return `
Eres un experto en búsquedas inmobiliarias de Guatemala. Analiza la consulta y descompónla aplicando TODAS las correcciones y normalizaciones posibles:

CONSULTA: "${query}"

APLICA ESTAS CORRECCIONES Y NORMALIZACIONES AGRESIVAMENTE:

1. ERRORES TIPOGRÁFICOS COMUNES:
   - "sona" → "zona", "apartamneto" → "apartamento", "casa" → "casa"
   - "piscna" → "piscina", "gimnacio" → "gimnasio"

2. ABREVIACIONES Y VARIACIONES:
   - "apto", "apt" → "apartamento"
   - "depto", "dpto" → "departamento"
   - "z1", "z 1", "z14", "z 14" → "zona 1", "zona 14"
   - "gym" → "gimnasio", "alberca" → "piscina"
   - "cochera", "garaje" → "estacionamiento"

3. SINÓNIMOS LOCALES GUATEMALA:
   - "zona viva" → "zona 10"
   - "centro", "centro histórico" → "zona 1"
   - "la villa" → "zona 14"
   - "cayalá" → "zona 16"
   - "oakland" → "zona 10"

4. VARIACIONES NUMÉRICAS:
   - "zona catorce", "zona 14" → "zona 14"
   - "zona diez", "zona 10" → "zona 10"
   - "zona uno", "zona 1" → "zona 1"

5. TRANSACCIONES:
   - "comprar", "compra", "en venta" → "venta"
   - "alquilar", "rentar", "arrendar" → "alquiler"

INSTRUCCIONES:
- SIEMPRE corrige errores tipográficos obvios
- SIEMPRE normaliza abreviaciones conocidas
- SIEMPRE aplica sinónimos locales
- Si no reconoces algo, mantenlo como está
- Si no hay información para un componente, déjalo vacío ("")

Descompón en estos componentes DESPUÉS de aplicar todas las correcciones:

1. LOCATION: Ubicaciones corregidas y normalizadas
2. PROPERTY: Tipos de propiedad corregidos
3. AMENITIES: Amenidades corregidas y normalizadas
4. PRICE: Información de precio/presupuesto
5. INTENT: Intención de transacción (buy/rent/neutral)
6. PRICE_CONTEXT: Contexto de precio (budget/luxury/neutral)

Responde ÚNICAMENTE con JSON válido:
{
  "location": "ubicación corregida/normalizada o vacío",
  "property": "tipo corregido o vacío",
  "amenities": "amenidades corregidas o vacío",
  "price": "precio o vacío",
  "intent": "buy/rent/neutral",
  "priceContext": "budget/luxury/neutral",
  "characteristics": {
    "bedrooms": número_o_null,
    "bathrooms": número_o_null,
    "area": número_o_null
  },
  "confidence": 0.95
}`;
  }

  /**
   * Parsea la respuesta de OpenAI
   */
  private parseDecompositionResponse(
    response: string,
    originalQuery: string,
    processingTime: number
  ): DecompositionResult {
    try {
      // Limpiar markdown si está presente
      let cleanResponse = response.trim();
      if (cleanResponse.startsWith('```json')) {
        cleanResponse = cleanResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanResponse.startsWith('```')) {
        cleanResponse = cleanResponse.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }

      const parsed = JSON.parse(cleanResponse);
      
      const decomposed: DecomposedQuery = {
        location: this.cleanComponent(parsed.location || ''),
        property: this.cleanComponent(parsed.property || ''),
        amenities: this.cleanComponent(parsed.amenities || ''),
        price: this.cleanComponent(parsed.price || ''),
        intent: this.cleanComponent(parsed.intent || 'neutral'),
        priceContext: this.cleanComponent(parsed.priceContext || 'neutral'),
        characteristics: {
          bedrooms: parsed.characteristics?.bedrooms || null,
          bathrooms: parsed.characteristics?.bathrooms || null,
          area: parsed.characteristics?.area || null,
        },
        original: originalQuery,
      };

      return {
        decomposed,
        confidence: parsed.confidence || 0.8,
        detectedProfile: this.detectProfileFromComponents(decomposed),
        processingTime,
      };

    } catch (error) {
      console.error('Error parsing decomposition response:', error);
      return this.createEmptyResult(originalQuery, processingTime);
    }
  }

  /**
   * Limpia y normaliza un componente extraído
   */
  private cleanComponent(component: string): string {
    if (!component || typeof component !== 'string') {
      return '';
    }
    
    return component
      .trim()
      .replace(/^["']|["']$/g, '') // Remover comillas
      .replace(/\s+/g, ' ') // Normalizar espacios
      .toLowerCase();
  }

  /**
   * Detecta el perfil de pesos basado en los componentes
   */
  private detectProfileFromComponents(decomposed: DecomposedQuery): string {
    const hasLocation = decomposed.location.length > 0;
    const amenityCount = decomposed.amenities.split(',').filter(a => a.trim().length > 0).length;
    
    if (hasLocation && amenityCount < 3) {
      return 'LOCATION_FOCUSED';
    }
    
    if (amenityCount >= 3) {
      return 'FEATURE_FOCUSED';
    }
    
    return 'BALANCED';
  }

  /**
   * Crea un resultado vacío para casos de error o consulta vacía
   */
  private createEmptyResult(originalQuery: string, processingTime: number): DecompositionResult {
    return {
      decomposed: {
        location: '',
        property: '',
        amenities: '',
        price: '',
        intent: '',
        priceContext: '',
        characteristics: {},
        original: originalQuery,
      },
      confidence: 0.0,
      detectedProfile: 'BALANCED',
      processingTime,
    };
  }

  /**
   * Valida si una descomposición es útil
   */
  isValidDecomposition(result: DecompositionResult): boolean {
    const { decomposed } = result;
    const hasAnyComponent = 
      decomposed.location.length > 0 ||
      decomposed.property.length > 0 ||
      decomposed.amenities.length > 0 ||
      decomposed.price.length > 0;
    
    return hasAnyComponent && result.confidence > 0.5;
  }

  /**
   * Obtiene estadísticas de la descomposición
   */
  getDecompositionStats(result: DecompositionResult): {
    componentCount: number;
    primaryComponent: string;
    isEmpty: boolean;
    confidence: number;
  } {
    const { decomposed } = result;
    const components = [
      { name: 'location', value: decomposed.location },
      { name: 'property', value: decomposed.property },
      { name: 'amenities', value: decomposed.amenities },
      { name: 'price', value: decomposed.price },
    ];

    const nonEmptyComponents = components.filter(c => c.value.length > 0);
    const primaryComponent = nonEmptyComponents.length > 0 
      ? nonEmptyComponents.reduce((a, b) => a.value.length > b.value.length ? a : b).name
      : 'none';

    return {
      componentCount: nonEmptyComponents.length,
      primaryComponent,
      isEmpty: nonEmptyComponents.length === 0,
      confidence: result.confidence,
    };
  }
}
