/**
 * Script para convertir un usuario en administrador
 * Ejecutar con: node scripts/make-admin.js [email]
 */

const { ConvexHttpClient } = require("convex/browser");

// Configuración
require('dotenv').config({ path: '.env.local' });
const CONVEX_URL = process.env.NEXT_PUBLIC_CONVEX_URL || "https://capable-cod-213.convex.cloud";

async function makeUserAdmin(email) {
  console.log('👑 Convirtiendo usuario en administrador...');
  console.log('================================');
  console.log(`Convex URL: ${CONVEX_URL}`);
  console.log(`Email: ${email}`);
  console.log('');

  try {
    // Crear cliente de Convex
    const client = new ConvexHttpClient(CONVEX_URL);

    // 1. Verificar conexión
    console.log('1️⃣ Verificando conexión con Convex...');

    // 2. Intentar convertir en admin
    console.log('2️⃣ Convirtiendo usuario en administrador...');
    const result = await client.mutation("users:makeAdmin", {
      email: email
    });

    console.log('   ✅ Usuario convertido exitosamente');
    console.log(`   📊 ${result.message}`);
    console.log(`   👤 ID de usuario: ${result.userId}`);

    console.log('');
    console.log('🎉 ¡Conversión completada!');
    console.log('✅ Ahora puedes acceder a:');
    console.log('   - /dashboard/admin (Panel de administración)');
    console.log('   - /dashboard/admin/location-config (Configuración de ubicaciones)');
    console.log('   - /dashboard/admin/system-messages (Mensajes del sistema)');
    console.log('');
    console.log('💡 Recarga la página para que los cambios tomen efecto');

  } catch (error) {
    console.error('❌ Error durante la conversión:', error.message);
    console.log('');
    console.log('🔧 Posibles causas:');
    console.log('1. El email no existe en la base de datos');
    console.log('2. Convex no está ejecutándose');
    console.log('3. Variables de entorno no configuradas');
    console.log('4. El usuario ya es administrador');

    // Mostrar detalles del error si están disponibles
    if (error.stack) {
      console.log('');
      console.log('📋 Detalles del error:');
      console.log(error.stack);
    }
  }
}

// Obtener email desde argumentos de línea de comandos
const email = process.argv[2];

if (!email) {
  console.log('❌ Error: Debes proporcionar un email');
  console.log('');
  console.log('📋 Uso:');
  console.log('   node scripts/make-admin.js <EMAIL>');
  console.log('');
  console.log('💡 Ejemplo:');
  console.log('   node scripts/make-admin.js <EMAIL>');
  process.exit(1);
}

// Validar formato de email básico
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
if (!emailRegex.test(email)) {
  console.log('❌ Error: El formato del email no es válido');
  console.log(`   Email proporcionado: ${email}`);
  process.exit(1);
}

// Ejecutar conversión
makeUserAdmin(email).catch(console.error);