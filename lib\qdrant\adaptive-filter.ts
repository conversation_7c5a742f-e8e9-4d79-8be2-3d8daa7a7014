/**
 * Sistema de filtrado adaptativo basado en similitud semántica
 * NO usa hardcoding, todo basado en embeddings y análisis semántico
 */

import { generateEmbedding } from './embeddings';
import type { DecomposedQuery } from './semantic-decomposer';

export interface SearchResult {
  payload: any;
  score: number;
  intentScore?: number;
  amenitiesScore?: number;
  fallbackApplied?: boolean;
}

export class AdaptiveSemanticFilter {
  
  /**
   * Filtra resultados basado en similitud semántica, no reglas hardcodeadas
   */
  async applySemanticFilters(
    results: SearchResult[],
    originalQuery: string,
    decomposedQuery: DecomposedQuery
  ): Promise<SearchResult[]> {
    
    // Si no hay criterios específicos, devolver resultados originales
    if (!this.hasSpecificCriteria(decomposedQuery)) {
      return results;
    }

    let filteredResults = [...results];

    // 1. FILTRO POR INTENCIÓN (usando embeddings, no hardcoding)
    if (decomposedQuery.intent && decomposedQuery.intent !== 'neutral') {
      filteredResults = await this.filterByIntentSemantically(
        filteredResults, 
        decomposedQuery.intent,
        originalQuery
      );
    }

    // 2. FILTRO POR AMENIDADES (usando similitud semántica)
    if (decomposedQuery.amenities && decomposedQuery.amenities.trim().length > 0) {
      filteredResults = await this.filterByAmenitiesSemantically(
        filteredResults,
        decomposedQuery.amenities,
        originalQuery
      );
    }

    // 3. FILTRO POR CONTEXTO DE PRECIO (usando embeddings)
    if (decomposedQuery.priceContext && decomposedQuery.priceContext !== 'neutral') {
      filteredResults = await this.filterByPriceContextSemantically(
        filteredResults,
        decomposedQuery.priceContext,
        originalQuery
      );
    }

    // 4. FILTRO POR CARACTERÍSTICAS (usando lógica estructurada cuando es clara)
    if (decomposedQuery.characteristics) {
      filteredResults = this.filterByCharacteristics(
        filteredResults,
        decomposedQuery.characteristics
      );
    }

    // Si los filtros eliminan todo, usar fallback inteligente
    if (filteredResults.length === 0 && results.length > 0) {
      return this.intelligentFallback(results, decomposedQuery);
    }

    return filteredResults;
  }

  /**
   * Filtro por intención usando similitud semántica
   */
  private async filterByIntentSemantically(
    results: SearchResult[],
    intent: string,
    originalQuery: string
  ): Promise<SearchResult[]> {
    
    try {
      // Generar embedding para la intención detectada
      const intentEmbedding = await this.generateIntentEmbedding(intent, originalQuery);
      
      // Calcular similitud con cada propiedad
      const scoredResults = await Promise.all(
        results.map(async (result) => {
          const propertyIntentScore = await this.calculateIntentSimilarity(
            result.payload,
            intentEmbedding
          );
          
          return {
            ...result,
            intentScore: propertyIntentScore
          };
        })
      );

      // Filtrar por threshold dinámico basado en distribución de scores
      const threshold = this.calculateDynamicThreshold(
        scoredResults.map(r => r.intentScore || 0)
      );

      const filtered = scoredResults
        .filter(r => (r.intentScore || 0) >= threshold)
        .sort((a, b) => (b.intentScore || 0) - (a.intentScore || 0));

      console.log(`🎯 Intent filter: ${results.length} → ${filtered.length} (threshold: ${threshold.toFixed(2)})`);
      
      return filtered;
      
    } catch (error) {
      console.error('Error in intent filtering:', error);
      return results; // Fallback a resultados originales
    }
  }

  /**
   * Filtro por amenidades usando similitud semántica
   */
  private async filterByAmenitiesSemantically(
    results: SearchResult[],
    amenities: string,
    originalQuery: string
  ): Promise<SearchResult[]> {
    
    try {
      // Generar embedding para las amenidades solicitadas
      const amenitiesEmbedding = await this.generateAmenitiesEmbedding(amenities);
      
      const scoredResults = await Promise.all(
        results.map(async (result) => {
          const amenitiesScore = await this.calculateAmenitiesSimilarity(
            result.payload.amenities || [],
            amenitiesEmbedding
          );
          
          return {
            ...result,
            amenitiesScore
          };
        })
      );

      // Threshold dinámico basado en la distribución
      const threshold = this.calculateDynamicThreshold(
        scoredResults.map(r => r.amenitiesScore || 0)
      );

      const filtered = scoredResults
        .filter(r => (r.amenitiesScore || 0) >= threshold)
        .sort((a, b) => (b.amenitiesScore || 0) - (a.amenitiesScore || 0));

      console.log(`🏠 Amenities filter: ${results.length} → ${filtered.length} (threshold: ${threshold.toFixed(2)})`);
      
      return filtered;
      
    } catch (error) {
      console.error('Error in amenities filtering:', error);
      return results; // Fallback a resultados originales
    }
  }

  /**
   * Filtro por contexto de precio usando similitud semántica
   */
  private async filterByPriceContextSemantically(
    results: SearchResult[],
    priceContext: string,
    originalQuery: string
  ): Promise<SearchResult[]> {
    
    try {
      // Generar embedding para el contexto de precio
      const priceEmbedding = await this.generatePriceContextEmbedding(priceContext, originalQuery);
      
      const scoredResults = await Promise.all(
        results.map(async (result) => {
          const priceScore = await this.calculatePriceContextSimilarity(
            result.payload,
            priceEmbedding
          );
          
          return {
            ...result,
            priceScore
          };
        })
      );

      // Threshold dinámico
      const threshold = this.calculateDynamicThreshold(
        scoredResults.map(r => r.priceScore || 0)
      );

      const filtered = scoredResults
        .filter(r => (r.priceScore || 0) >= threshold)
        .sort((a, b) => (b.priceScore || 0) - (a.priceScore || 0));

      console.log(`💰 Price context filter: ${results.length} → ${filtered.length} (threshold: ${threshold.toFixed(2)})`);
      
      return filtered;
      
    } catch (error) {
      console.error('Error in price context filtering:', error);
      return results;
    }
  }

  /**
   * Filtro por características específicas (habitaciones, baños)
   */
  private filterByCharacteristics(
    results: SearchResult[],
    characteristics: DecomposedQuery['characteristics']
  ): SearchResult[] {
    
    let filtered = [...results];
    
    // Filtro por habitaciones (solo si es específico)
    if (characteristics.bedrooms && characteristics.bedrooms > 0) {
      const beforeCount = filtered.length;
      filtered = filtered.filter(result => {
        const propBedrooms = result.payload.bedrooms;
        // Permitir ±1 habitación para flexibilidad
        return propBedrooms && Math.abs(propBedrooms - characteristics.bedrooms!) <= 1;
      });
      console.log(`🛏️ Bedrooms filter: ${beforeCount} → ${filtered.length} (${characteristics.bedrooms} ±1)`);
    }

    // Filtro por baños (solo si es específico)
    if (characteristics.bathrooms && characteristics.bathrooms > 0) {
      const beforeCount = filtered.length;
      filtered = filtered.filter(result => {
        const propBathrooms = result.payload.bathrooms;
        // Permitir ±1 baño para flexibilidad
        return propBathrooms && Math.abs(propBathrooms - characteristics.bathrooms!) <= 1;
      });
      console.log(`🚿 Bathrooms filter: ${beforeCount} → ${filtered.length} (${characteristics.bathrooms} ±1)`);
    }

    return filtered;
  }

  /**
   * Calcula threshold dinámico basado en distribución de scores
   */
  private calculateDynamicThreshold(scores: number[]): number {
    if (scores.length === 0) return 0;
    
    // Usar percentil 30 como threshold dinámico
    const sortedScores = scores.sort((a, b) => b - a);
    const percentile30Index = Math.floor(sortedScores.length * 0.3);
    
    return Math.max(
      sortedScores[percentile30Index] || 0,
      0.25 // Threshold mínimo para evitar ruido
    );
  }

  /**
   * Genera embedding específico para intención
   */
  private async generateIntentEmbedding(intent: string, originalQuery: string): Promise<number[]> {
    const intentText = `${originalQuery} ${intent === 'buy' ? 'comprar venta propiedad' : 'alquilar renta arrendar'}`;
    return await generateEmbedding(intentText);
  }

  /**
   * Genera embedding específico para amenidades
   */
  private async generateAmenitiesEmbedding(amenities: string): Promise<number[]> {
    return await generateEmbedding(`amenidades características servicios ${amenities}`);
  }

  /**
   * Genera embedding específico para contexto de precio
   */
  private async generatePriceContextEmbedding(priceContext: string, originalQuery: string): Promise<number[]> {
    const contextText = `${originalQuery} ${priceContext === 'budget' ? 'barato económico accesible' : 'lujo premium exclusivo'}`;
    return await generateEmbedding(contextText);
  }

  /**
   * Calcula similitud de intención usando embeddings
   */
  private async calculateIntentSimilarity(
    property: any,
    intentEmbedding: number[]
  ): Promise<number> {
    
    try {
      // Crear texto representativo de la intención de la propiedad
      const statusText = property.status === 'for_sale' ? 'venta comprar' : 'alquiler rentar';
      const propertyIntentText = `${statusText} ${property.type} ${property.price}`;
      const propertyEmbedding = await generateEmbedding(propertyIntentText);
      
      // Calcular similitud coseno
      return this.cosineSimilarity(intentEmbedding, propertyEmbedding);
    } catch (error) {
      console.error('Error calculating intent similarity:', error);
      return 0;
    }
  }

  /**
   * Calcula similitud de amenidades usando embeddings
   */
  private async calculateAmenitiesSimilarity(
    propertyAmenities: string[],
    requestedAmenitiesEmbedding: number[]
  ): Promise<number> {
    
    try {
      if (!propertyAmenities || propertyAmenities.length === 0) {
        return 0;
      }

      const propertyAmenitiesText = propertyAmenities.join(' ');
      const propertyAmenitiesEmbedding = await generateEmbedding(propertyAmenitiesText);
      
      return this.cosineSimilarity(requestedAmenitiesEmbedding, propertyAmenitiesEmbedding);
    } catch (error) {
      console.error('Error calculating amenities similarity:', error);
      return 0;
    }
  }

  /**
   * Calcula similitud de contexto de precio usando embeddings
   */
  private async calculatePriceContextSimilarity(
    property: any,
    priceContextEmbedding: number[]
  ): Promise<number> {
    
    try {
      const price = property.price || 0;
      const priceText = `precio ${price} ${price < 200000 ? 'económico barato' : price > 400000 ? 'lujo premium' : 'medio'}`;
      const propertyPriceEmbedding = await generateEmbedding(priceText);
      
      return this.cosineSimilarity(priceContextEmbedding, propertyPriceEmbedding);
    } catch (error) {
      console.error('Error calculating price context similarity:', error);
      return 0;
    }
  }

  /**
   * Fallback inteligente cuando los filtros son muy restrictivos
   */
  private intelligentFallback(
    originalResults: SearchResult[],
    decomposedQuery: DecomposedQuery
  ): SearchResult[] {
    
    console.log(`🔄 Applying intelligent fallback - filters too restrictive`);
    
    // Devolver top 3 resultados originales con nota explicativa
    return originalResults.slice(0, 3).map(result => ({
      ...result,
      fallbackApplied: true
    }));
  }

  /**
   * Determina si hay criterios específicos que requieren filtrado
   */
  private hasSpecificCriteria(decomposedQuery: DecomposedQuery): boolean {
    return !!(
      (decomposedQuery.intent && decomposedQuery.intent !== 'neutral') ||
      (decomposedQuery.amenities && decomposedQuery.amenities.trim().length > 0) ||
      (decomposedQuery.priceContext && decomposedQuery.priceContext !== 'neutral') ||
      decomposedQuery.characteristics?.bedrooms ||
      decomposedQuery.characteristics?.bathrooms
    );
  }

  /**
   * Calcula similitud coseno entre dos vectores
   */
  private cosineSimilarity(vecA: number[], vecB: number[]): number {
    if (vecA.length !== vecB.length) return 0;
    
    const dotProduct = vecA.reduce((sum, a, i) => sum + a * vecB[i], 0);
    const magnitudeA = Math.sqrt(vecA.reduce((sum, a) => sum + a * a, 0));
    const magnitudeB = Math.sqrt(vecB.reduce((sum, b) => sum + b * b, 0));
    
    if (magnitudeA === 0 || magnitudeB === 0) return 0;
    
    return dotProduct / (magnitudeA * magnitudeB);
  }
}
