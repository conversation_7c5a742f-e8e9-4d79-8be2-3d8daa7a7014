/**
 * Query Profile Detector - Sistema Inteligente de Detección de Perfil de Consulta
 * 
 * Detecta automáticamente el ámbito de una consulta inmobiliaria usando OpenAI
 * para clasificarla en uno de los 11 perfiles de pesos semánticos.
 * 
 * Día 4 - Optimización de Pesos Semánticos
 */

import OpenAI from 'openai';
import { SemanticWeights } from './unified-config';
import { SEMANTIC_WEIGHT_PROFILES, WeightProfile } from './weight-config';

// Tipos para detección de perfil
export interface QueryAnalysis {
  detectedCriteria: {
    location: boolean;
    propertyType: boolean;
    rooms: boolean;
    size: boolean;
    amenities: boolean;
    budget: boolean;
    status: boolean;
    age: boolean;
    features: boolean;
  };
  criteriaCount: number;
  primaryCriteria: string[];
  confidence: number;
  reasoning: string;
}

export interface ProfileDetectionResult {
  profile: WeightProfile;
  analysis: QueryAnalysis;
  fallbackUsed: boolean;
  processingTime: number;
  adaptiveWeights?: SemanticWeights;
}

/**
 * Detector inteligente de perfil de consulta usando OpenAI
 */
export class QueryProfileDetector {
  private openai: OpenAI;
  private model: string;
  private confidenceThreshold: number;

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
    this.model = process.env.AI_MODEL_QUERY_ANALYSIS || 'gpt-4o-mini';
    this.confidenceThreshold = 0.7;
  }

  /**
   * Detecta el perfil de pesos apropiado para una consulta
   */
  async detectProfile(query: string): Promise<ProfileDetectionResult> {
    const startTime = Date.now();

    try {
      // Validar entrada
      if (!query || query.trim().length === 0) {
        return this.createFallbackResult('Consulta vacía', Date.now() - startTime);
      }

      // Analizar consulta con OpenAI
      const analysis = await this.analyzeQueryWithAI(query);

      // Determinar perfil basado en análisis
      const profile = this.determineProfileFromAnalysis(analysis);

      // Crear pesos adaptativos si es necesario
      const adaptiveWeights = this.createAdaptiveWeights(analysis, profile);

      return {
        profile,
        analysis,
        fallbackUsed: false,
        processingTime: Date.now() - startTime,
        adaptiveWeights,
      };

    } catch (error) {
      console.error('Error en detección de perfil:', error);
      return this.createFallbackResult(
        `Error: ${error instanceof Error ? error.message : 'Desconocido'}`,
        Date.now() - startTime
      );
    }
  }

  /**
   * Analiza la consulta usando OpenAI para detectar criterios
   */
  private async analyzeQueryWithAI(query: string): Promise<QueryAnalysis> {
    const prompt = this.buildAnalysisPrompt(query);

    const response = await this.openai.chat.completions.create({
      model: this.model,
      messages: [
        {
          role: 'system',
          content: 'Eres un experto en análisis de consultas inmobiliarias en Guatemala. Analiza consultas para detectar criterios específicos de búsqueda.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.1, // Muy determinístico
      max_tokens: 800,
    });

    const content = response.choices[0].message.content || '{}';
    return this.parseAnalysisResponse(content, query);
  }

  /**
   * Construye el prompt para análisis de consulta
   */
  private buildAnalysisPrompt(query: string): string {
    return `
Analiza esta consulta inmobiliaria y detecta QUÉ CRITERIOS ESPECÍFICOS menciona el usuario:

CONSULTA: "${query}"

Detecta si la consulta menciona EXPLÍCITAMENTE cada criterio:

1. UBICACIÓN: ¿Menciona zona, colonia, área, ciudad específica?
2. TIPO_PROPIEDAD: ¿Menciona casa, apartamento, oficina, terreno, local?
3. HABITACIONES: ¿Menciona número de habitaciones, dormitorios, cuartos?
4. TAMAÑO: ¿Menciona metros cuadrados, área, "grande", "pequeño"?
5. AMENIDADES: ¿Menciona piscina, gimnasio, parqueo, terraza, etc?
6. PRESUPUESTO: ¿Menciona precio, rango, "económico", "barato"?
7. STATUS: ¿Menciona "comprar", "alquilar", "venta", "renta"?
8. EDAD: ¿Menciona "nuevo", "moderno", año de construcción?
9. CARACTERÍSTICAS: ¿Menciona "destacada", "tour virtual", "fotos"?

Responde SOLO en JSON:
{
  "detectedCriteria": {
    "location": boolean,
    "propertyType": boolean,
    "rooms": boolean,
    "size": boolean,
    "amenities": boolean,
    "budget": boolean,
    "status": boolean,
    "age": boolean,
    "features": boolean
  },
  "primaryCriteria": ["criterio1", "criterio2"],
  "confidence": 0.95,
  "reasoning": "Explicación breve de por qué se detectaron estos criterios"
}

IMPORTANTE: Solo marca como true los criterios que están EXPLÍCITAMENTE mencionados.
`;
  }

  /**
   * Parsea la respuesta de OpenAI
   */
  private parseAnalysisResponse(content: string, originalQuery: string): QueryAnalysis {
    try {
      // Limpiar respuesta
      const cleanContent = content.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      const parsed = JSON.parse(cleanContent);

      // Contar criterios detectados
      const criteriaCount = Object.values(parsed.detectedCriteria || {})
        .filter(Boolean).length;

      return {
        detectedCriteria: {
          location: parsed.detectedCriteria?.location || false,
          propertyType: parsed.detectedCriteria?.propertyType || false,
          rooms: parsed.detectedCriteria?.rooms || false,
          size: parsed.detectedCriteria?.size || false,
          amenities: parsed.detectedCriteria?.amenities || false,
          budget: parsed.detectedCriteria?.budget || false,
          status: parsed.detectedCriteria?.status || false,
          age: parsed.detectedCriteria?.age || false,
          features: parsed.detectedCriteria?.features || false,
        },
        criteriaCount,
        primaryCriteria: parsed.primaryCriteria || [],
        confidence: parsed.confidence || 0.5,
        reasoning: parsed.reasoning || 'Análisis automático',
      };

    } catch (error) {
      console.warn('Error parseando análisis de consulta:', error);
      return this.createFallbackAnalysis(originalQuery);
    }
  }

  /**
   * Determina el perfil basado en el análisis
   */
  private determineProfileFromAnalysis(analysis: QueryAnalysis): WeightProfile {
    const { detectedCriteria, criteriaCount } = analysis;

    // Si hay múltiples criterios (3+), usar MIXED_CRITERIA
    if (criteriaCount >= 3) {
      return SEMANTIC_WEIGHT_PROFILES.MIXED_CRITERIA;
    }

    // Priorizar por importancia para el usuario guatemalteco
    if (detectedCriteria.location) {
      return SEMANTIC_WEIGHT_PROFILES.LOCATION_FOCUSED;
    }

    if (detectedCriteria.budget) {
      return SEMANTIC_WEIGHT_PROFILES.BUDGET_FOCUSED;
    }

    if (detectedCriteria.amenities) {
      return SEMANTIC_WEIGHT_PROFILES.AMENITIES_FOCUSED;
    }

    if (detectedCriteria.rooms) {
      return SEMANTIC_WEIGHT_PROFILES.ROOMS_SPECS_FOCUSED;
    }

    if (detectedCriteria.propertyType) {
      return SEMANTIC_WEIGHT_PROFILES.PROPERTY_TYPE_FOCUSED;
    }

    if (detectedCriteria.size) {
      return SEMANTIC_WEIGHT_PROFILES.SIZE_FOCUSED;
    }

    if (detectedCriteria.status) {
      return SEMANTIC_WEIGHT_PROFILES.STATUS_FOCUSED;
    }

    if (detectedCriteria.age) {
      return SEMANTIC_WEIGHT_PROFILES.AGE_FOCUSED;
    }

    if (detectedCriteria.features) {
      return SEMANTIC_WEIGHT_PROFILES.FEATURES_FOCUSED;
    }

    // Si no hay criterios específicos, usar búsqueda general
    if (criteriaCount === 0) {
      return SEMANTIC_WEIGHT_PROFILES.GENERAL_SEARCH;
    }

    // Fallback a balanceado
    return SEMANTIC_WEIGHT_PROFILES.BALANCED;
  }

  /**
   * Crea pesos adaptativos para criterios mixtos
   */
  private createAdaptiveWeights(analysis: QueryAnalysis, profile: WeightProfile): SemanticWeights | undefined {
    // Solo crear pesos adaptativos para MIXED_CRITERIA
    if (profile.name !== 'MIXED_CRITERIA' || analysis.criteriaCount < 3) {
      return undefined;
    }

    const { detectedCriteria } = analysis;
    let locationWeight = 0.20;
    let propertyWeight = 0.20;
    let amenitiesWeight = 0.20;
    let characteristicsWeight = 0.15;
    let priceWeight = 0.05;

    // Ajustar pesos según criterios detectados
    if (detectedCriteria.location) locationWeight += 0.15;
    if (detectedCriteria.budget) priceWeight += 0.15;
    if (detectedCriteria.amenities) amenitiesWeight += 0.15;
    if (detectedCriteria.propertyType) propertyWeight += 0.10;
    if (detectedCriteria.rooms || detectedCriteria.size) {
      characteristicsWeight += 0.15;
    }

    // Normalizar para que sumen 1.0
    const total = locationWeight + propertyWeight + amenitiesWeight + characteristicsWeight + priceWeight;

    return {
      location: locationWeight / total,
      property: propertyWeight / total,
      amenities: amenitiesWeight / total,
      characteristics: characteristicsWeight / total,
      price: priceWeight / total,
    };
  }

  /**
   * Crea resultado de fallback en caso de error
   */
  private createFallbackResult(reason: string, processingTime: number): ProfileDetectionResult {
    return {
      profile: SEMANTIC_WEIGHT_PROFILES.GENERAL_SEARCH,
      analysis: this.createFallbackAnalysis(reason),
      fallbackUsed: true,
      processingTime,
    };
  }

  /**
   * Crea análisis de fallback
   */
  private createFallbackAnalysis(query: string): QueryAnalysis {
    return {
      detectedCriteria: {
        location: false,
        propertyType: false,
        rooms: false,
        size: false,
        amenities: false,
        budget: false,
        status: false,
        age: false,
        features: false,
      },
      criteriaCount: 0,
      primaryCriteria: [],
      confidence: 0.1,
      reasoning: `Fallback para: ${query}`,
    };
  }
}
