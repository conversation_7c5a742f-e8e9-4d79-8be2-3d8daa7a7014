{"parameters": {"toolDescription": "📸 MOSTRAR IMÁGENES DE PROPIEDADES\n\nMuestra las fotografías de una propiedad específica cuando el usuario las solicite.\n\nCUÁNDO USAR:\n- Cliente dice: \"quiero ver fotos\", \"muéstrame imágenes\", \"fotografías\", \"pics\", \"pictures\"\n- <PERSON><PERSON><PERSON> pregun<PERSON>: \"¿tienes fotos?\", \"¿hay imágenes?\", \"¿puedo ver cómo se ve?\"\n- Cliente solicita: \"envíame las fotos\", \"mándame imágenes\", \"quiero ver la propiedad\"\n- Cliente quiere ver: \"cómo luce\", \"aspecto visual\", \"apariencia\"\n\nPARÁMETROS:\n- propertyId: ID EXACTO de la propiedad (formato: jd7fzdrkdanjs1tgb98kgm88a57jfhav)\n\nCRÍTICO - FLUJO DE PROPERTY ID:\n1. El propertyId DEBE venir de una búsqueda previa mostrada al usuario\n2. Buscar en el historial de conversación el \"ID: [propertyId]\" mostrado\n3. Usar ese propertyId exacto\n4. SI NO ENCUENTRAS UN ID PREVIO: Primero usar buscar_propiedades\n\nRESPUESTA:\n- Devuelve URLs directas de Cloudinary de todas las imágenes\n- Incluye información sobre cantidad de fotos disponibles\n- Proporciona enlaces optimizados para WhatsApp\n\nFORMATO DE RESPUESTA OBLIGATORIO:\n```\n📸 **FOTOGRAFÍAS - [Título Propiedad]**\n\n🖼️ **GALERÍA DISPONIBLE:** [X] imágenes\n📍 **Ubicación:** [dirección]\n\n🔗 **ENLACES DIRECTOS:**\n• Imagen 1: [URL_Cloudinary_1]\n• Imagen 2: [URL_Cloudinary_2]\n• Imagen 3: [URL_Cloudinary_3]\n[...continuar con todas las imágenes]\n\n💡 **TIP:** Haz clic en los enlaces para ver las fotos en alta calidad\n\n¿Te gustaría conocer más detalles de esta propiedad o agendar una visita?\n```\n\nIMPORTANTE:\n- Usar cuando el usuario específicamente pida ver imágenes\n- Complementa (no reemplaza) buscar_informacion\n- Finalizar preguntando si desea más detalles o agendar visita\n- SIEMPRE mostrar TODAS las imágenes disponibles\n- Usar URLs directas de Cloudinary sin modificaciones", "method": "POST", "url": "https://capable-cod-213.convex.site/getPropertyImagesForAgent", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "x-api-key", "value": "dde2fae93649d4550684edd565a9515f"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"propertyId\": \"{{ $fromAI('propertyId', 'Property ID', 'string') }}\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequestTool", "typeVersion": 4.2, "position": [-11440, 3400], "id": "NEW_UUID", "name": "mostrar_imagenes_propiedad"}