/**
 * Score Combiner
 * 
 * Algoritmos para combinar y normalizar scores de múltiples componentes
 * con breakdown detallado y análisis de relevancia.
 */

import { SemanticWeights } from './unified-config';

// Tipos para combinación de scores
export interface ComponentScore {
  component: string;
  score: number;
  weight: number;
  normalized: number;
  contribution: number;
}

export interface CombinedScore {
  finalScore: number;
  normalizedScore: number;
  componentBreakdown: ComponentScore[];
  dominantComponent: string;
  confidence: number;
  method: string;
}

export interface ScoreCombinationOptions {
  method: 'weighted_sum' | 'weighted_harmonic' | 'weighted_geometric';
  normalization: 'none' | 'min_max' | 'z_score';
  boostDominant: boolean;
  penalizeEmpty: boolean;
}

/**
 * Clase para combinar scores de múltiples componentes
 */
export class ScoreCombiner {
  private defaultOptions: ScoreCombinationOptions = {
    method: 'weighted_sum',
    normalization: 'min_max',
    boostDominant: true,
    penalizeEmpty: true,
  };

  /**
   * Combina scores de componentes usando pesos semánticos
   */
  combineScores(
    componentScores: Record<string, number>,
    weights: SemanticWeights,
    options: Partial<ScoreCombinationOptions> = {}
  ): CombinedScore {
    const opts = { ...this.defaultOptions, ...options };

    // Preparar datos de componentes
    const components = this.prepareComponentData(componentScores, weights);

    // Normalizar scores si es necesario
    const normalizedComponents = this.normalizeScores(components, opts.normalization);

    // Calcular score final según el método
    const finalScore = this.calculateFinalScore(normalizedComponents, opts.method);

    // Aplicar ajustes adicionales
    const adjustedScore = this.applyAdjustments(finalScore, normalizedComponents, opts);

    // Calcular métricas adicionales
    const dominantComponent = this.findDominantComponent(normalizedComponents);
    const confidence = this.calculateConfidence(normalizedComponents);

    return {
      finalScore: adjustedScore,
      normalizedScore: Math.min(Math.max(adjustedScore, 0), 1), // Clamp entre 0 y 1
      componentBreakdown: normalizedComponents,
      dominantComponent,
      confidence,
      method: opts.method,
    };
  }

  /**
   * Prepara datos de componentes con información básica
   */
  private prepareComponentData(
    scores: Record<string, number>,
    weights: SemanticWeights
  ): ComponentScore[] {
    return [
      {
        component: 'location',
        score: scores.location || 0,
        weight: weights.location,
        normalized: scores.location || 0,
        contribution: (scores.location || 0) * weights.location,
      },
      {
        component: 'property',
        score: scores.property || 0,
        weight: weights.property,
        normalized: scores.property || 0,
        contribution: (scores.property || 0) * weights.property,
      },
      {
        component: 'amenities',
        score: scores.amenities || 0,
        weight: weights.amenities,
        normalized: scores.amenities || 0,
        contribution: (scores.amenities || 0) * weights.amenities,
      },
      {
        component: 'price',
        score: scores.price || 0,
        weight: weights.price,
        normalized: scores.price || 0,
        contribution: (scores.price || 0) * weights.price,
      },
    ];
  }

  /**
   * Normaliza scores según el método especificado
   */
  private normalizeScores(
    components: ComponentScore[],
    method: 'none' | 'min_max' | 'z_score'
  ): ComponentScore[] {
    if (method === 'none') {
      return components;
    }

    const scores = components.map(c => c.score).filter(s => s > 0);
    
    if (scores.length === 0) {
      return components;
    }

    if (method === 'min_max') {
      const min = Math.min(...scores);
      const max = Math.max(...scores);
      const range = max - min;

      if (range === 0) {
        return components;
      }

      return components.map(component => ({
        ...component,
        normalized: component.score > 0 ? (component.score - min) / range : 0,
        contribution: component.score > 0 ? ((component.score - min) / range) * component.weight : 0,
      }));
    }

    if (method === 'z_score') {
      const mean = scores.reduce((sum, s) => sum + s, 0) / scores.length;
      const variance = scores.reduce((sum, s) => sum + Math.pow(s - mean, 2), 0) / scores.length;
      const stdDev = Math.sqrt(variance);

      if (stdDev === 0) {
        return components;
      }

      return components.map(component => ({
        ...component,
        normalized: component.score > 0 ? (component.score - mean) / stdDev : 0,
        contribution: component.score > 0 ? ((component.score - mean) / stdDev) * component.weight : 0,
      }));
    }

    return components;
  }

  /**
   * Calcula el score final según el método especificado
   */
  private calculateFinalScore(
    components: ComponentScore[],
    method: 'weighted_sum' | 'weighted_harmonic' | 'weighted_geometric'
  ): number {
    const activeComponents = components.filter(c => c.normalized > 0);

    if (activeComponents.length === 0) {
      return 0;
    }

    switch (method) {
      case 'weighted_sum':
        return components.reduce((sum, c) => sum + c.contribution, 0);

      case 'weighted_harmonic':
        const harmonicSum = activeComponents.reduce((sum, c) => {
          return sum + (c.weight / c.normalized);
        }, 0);
        const totalWeight = activeComponents.reduce((sum, c) => sum + c.weight, 0);
        return harmonicSum > 0 ? totalWeight / harmonicSum : 0;

      case 'weighted_geometric':
        const product = activeComponents.reduce((prod, c) => {
          return prod * Math.pow(c.normalized, c.weight);
        }, 1);
        return product;

      default:
        return components.reduce((sum, c) => sum + c.contribution, 0);
    }
  }

  /**
   * Aplica ajustes adicionales al score final
   */
  private applyAdjustments(
    score: number,
    components: ComponentScore[],
    options: ScoreCombinationOptions
  ): number {
    let adjustedScore = score;

    // Boost para componente dominante
    if (options.boostDominant) {
      const dominant = this.findDominantComponent(components);
      const dominantComponent = components.find(c => c.component === dominant);
      
      if (dominantComponent && dominantComponent.normalized > 0.8) {
        adjustedScore *= 1.1; // 10% boost para componentes muy relevantes
      }
    }

    // Penalización por componentes vacíos
    if (options.penalizeEmpty) {
      const activeComponents = components.filter(c => c.normalized > 0).length;
      const totalComponents = components.length;
      const completeness = activeComponents / totalComponents;
      
      if (completeness < 0.5) {
        adjustedScore *= (0.8 + 0.2 * completeness); // Penalización gradual
      }
    }

    return adjustedScore;
  }

  /**
   * Encuentra el componente dominante (mayor contribución)
   */
  private findDominantComponent(components: ComponentScore[]): string {
    const maxContribution = Math.max(...components.map(c => c.contribution));
    const dominant = components.find(c => c.contribution === maxContribution);
    return dominant?.component || 'none';
  }

  /**
   * Calcula la confianza del score combinado
   */
  private calculateConfidence(components: ComponentScore[]): number {
    const activeComponents = components.filter(c => c.normalized > 0);
    
    if (activeComponents.length === 0) {
      return 0;
    }

    // Confianza basada en:
    // 1. Número de componentes activos
    // 2. Distribución de scores
    // 3. Consistencia entre componentes

    const completeness = activeComponents.length / components.length;
    const averageScore = activeComponents.reduce((sum, c) => sum + c.normalized, 0) / activeComponents.length;
    
    // Calcular varianza de scores normalizados
    const variance = activeComponents.reduce((sum, c) => {
      return sum + Math.pow(c.normalized - averageScore, 2);
    }, 0) / activeComponents.length;
    
    const consistency = 1 - Math.min(variance, 1); // Menor varianza = mayor consistencia

    // Combinar factores
    const confidence = (completeness * 0.4) + (averageScore * 0.4) + (consistency * 0.2);
    
    return Math.min(Math.max(confidence, 0), 1);
  }

  /**
   * Genera reporte detallado del score combinado
   */
  generateScoreReport(result: CombinedScore): {
    summary: string;
    details: Array<{
      component: string;
      score: number;
      weight: number;
      contribution: number;
      percentage: number;
    }>;
    insights: string[];
  } {
    const totalContribution = result.componentBreakdown.reduce((sum, c) => sum + c.contribution, 0);
    
    const details = result.componentBreakdown.map(component => ({
      component: component.component,
      score: Math.round(component.normalized * 100) / 100,
      weight: Math.round(component.weight * 100) / 100,
      contribution: Math.round(component.contribution * 100) / 100,
      percentage: totalContribution > 0 ? Math.round((component.contribution / totalContribution) * 100) : 0,
    }));

    const insights = this.generateInsights(result);

    const summary = `Score final: ${Math.round(result.finalScore * 100) / 100} ` +
                   `(confianza: ${Math.round(result.confidence * 100)}%, ` +
                   `dominante: ${result.dominantComponent})`;

    return {
      summary,
      details,
      insights,
    };
  }

  /**
   * Genera insights sobre la combinación de scores
   */
  private generateInsights(result: CombinedScore): string[] {
    const insights: string[] = [];
    const { componentBreakdown, dominantComponent, confidence } = result;

    // Insight sobre componente dominante
    const dominant = componentBreakdown.find(c => c.component === dominantComponent);
    if (dominant && dominant.contribution > 0.5) {
      insights.push(`El componente "${dominantComponent}" domina la búsqueda (${Math.round(dominant.contribution * 100)}% del score)`);
    }

    // Insight sobre confianza
    if (confidence < 0.5) {
      insights.push('Baja confianza: pocos componentes activos o scores inconsistentes');
    } else if (confidence > 0.8) {
      insights.push('Alta confianza: múltiples componentes con scores consistentes');
    }

    // Insight sobre distribución
    const activeComponents = componentBreakdown.filter(c => c.normalized > 0);
    if (activeComponents.length === 1) {
      insights.push('Búsqueda enfocada en un solo componente');
    } else if (activeComponents.length >= 3) {
      insights.push('Búsqueda multi-dimensional con varios componentes activos');
    }

    return insights;
  }
}
