/**
 * Multi-Component Embedding Generator
 * 
 * Genera embeddings especializados para cada componente semántico de una consulta
 * optimizando las llamadas paralelas a OpenAI.
 */

import { generateEmbedding } from './embeddings';
import { DecomposedQuery } from './semantic-decomposer';

// Tipos para embeddings multi-componente
export interface ComponentEmbedding {
  component: string;
  text: string;
  embedding: number[];
  isEmpty: boolean;
  processingTime: number;
}

export interface MultiComponentEmbeddingResult {
  location: ComponentEmbedding;
  property: ComponentEmbedding;
  amenities: ComponentEmbedding;
  characteristics: ComponentEmbedding;
  price: ComponentEmbedding;
  totalProcessingTime: number;
  successfulComponents: number;
}

/**
 * Clase para generar embeddings especializados por componente
 */
export class MultiComponentEmbeddingGenerator {
  private embeddingModel: string;

  constructor() {
    this.embeddingModel = process.env.AI_MODEL_EMBEDDINGS || 'text-embedding-3-small';
  }

  /**
   * Genera embeddings para todos los componentes de una consulta descompuesta
   */
  async generateMultiComponentEmbeddings(
    decomposed: DecomposedQuery
  ): Promise<MultiComponentEmbeddingResult> {
    const startTime = Date.now();

    try {
      // Preparar textos optimizados para cada componente
      const componentTexts = this.prepareComponentTexts(decomposed);

      // Generar embeddings en paralelo para componentes no vacíos
      const embeddingPromises = Object.entries(componentTexts).map(
        async ([component, text]) => {
          const componentStartTime = Date.now();
          
          if (!text || text.trim().length === 0) {
            return this.createEmptyEmbedding(component, componentStartTime);
          }

          try {
            const embedding = await generateEmbedding(text);
            return {
              component,
              text,
              embedding,
              isEmpty: false,
              processingTime: Date.now() - componentStartTime,
            };
          } catch (error) {
            console.error(`Error generating embedding for ${component}:`, error);
            return this.createEmptyEmbedding(component, componentStartTime);
          }
        }
      );

      // Esperar a que todos los embeddings se generen
      const results = await Promise.all(embeddingPromises);

      // Organizar resultados por componente
      const organizedResults = this.organizeResults(results);
      const successfulComponents = results.filter(r => !r.isEmpty).length;

      console.log(`🧠 Generated ${successfulComponents}/5 component embeddings in ${Date.now() - startTime}ms`);

      return {
        ...organizedResults,
        totalProcessingTime: Date.now() - startTime,
        successfulComponents,
      };

    } catch (error) {
      console.error('Error generating multi-component embeddings:', error);
      return this.createEmptyResult(Date.now() - startTime);
    }
  }

  /**
   * Prepara textos optimizados para cada componente
   */
  private prepareComponentTexts(decomposed: DecomposedQuery): Record<string, string> {
    return {
      location: this.optimizeLocationText(decomposed.location),
      property: this.optimizePropertyText(decomposed.property),
      amenities: this.optimizeAmenitiesText(decomposed.amenities),
      characteristics: this.optimizeCharacteristicsText(decomposed.characteristics),
      price: this.optimizePriceText(decomposed.price),
    };
  }

  /**
   * Optimiza el texto de ubicación para embedding
   */
  private optimizeLocationText(location: string): string {
    if (!location || location.trim().length === 0) {
      return '';
    }

    // NO normalización - confiar 100% en la capacidad semántica de OpenAI
    // OpenAI puede interpretar z10, zon 10, zona 10, sona 10, z-10 como "zona 10"
    return location.trim();
  }

  /**
   * Optimiza el texto de tipo de propiedad para embedding
   */
  private optimizePropertyText(property: string): string {
    if (!property || property.trim().length === 0) {
      return '';
    }

    // SOLO expandir abreviaciones básicas - NO agregar contexto adicional
    let optimized = property
      .replace(/\bapto\b/gi, 'apartamento')
      .replace(/\bdpto\b/gi, 'departamento')
      .trim();

    return optimized;
  }

  /**
   * Optimiza el texto de amenidades para embedding
   */
  private optimizeAmenitiesText(amenities: string): string {
    if (!amenities || amenities.trim().length === 0) {
      return '';
    }

    // SOLO normalizar amenidades básicas - NO agregar contexto adicional
    let optimized = amenities
      .replace(/\balberca\b/gi, 'piscina')
      .replace(/\bgaraje\b/gi, 'cochera estacionamiento')
      .replace(/\bgym\b/gi, 'gimnasio')
      .trim();

    return optimized;
  }

  /**
   * Optimiza el texto de características técnicas para embedding
   */
  private optimizeCharacteristicsText(characteristics: any): string {
    if (!characteristics || typeof characteristics !== 'object') {
      return '';
    }

    const parts: string[] = [];

    // Agregar habitaciones
    if (characteristics.bedrooms && characteristics.bedrooms > 0) {
      parts.push(`${characteristics.bedrooms} habitaciones`);
    }

    // Agregar baños
    if (characteristics.bathrooms && characteristics.bathrooms > 0) {
      parts.push(`${characteristics.bathrooms} baños`);
    }

    // Agregar área
    if (characteristics.area && characteristics.area > 0) {
      parts.push(`${characteristics.area} metros cuadrados`);
    }

    return parts.join(' ');
  }

  /**
   * Optimiza el texto de precio para embedding
   */
  private optimizePriceText(price: string): string {
    if (!price || price.trim().length === 0) {
      return '';
    }

    // SOLO normalizar formato de precios básicos - NO agregar contexto adicional
    let optimized = price
      .replace(/\bk\b/gi, 'mil')
      .replace(/\bm\b/gi, 'millones')
      .replace(/\busd\b/gi, 'dólares')
      .replace(/\bgtq\b/gi, 'quetzales')
      .trim();

    return optimized;
  }

  /**
   * Organiza los resultados por componente
   */
  private organizeResults(results: ComponentEmbedding[]): {
    location: ComponentEmbedding;
    property: ComponentEmbedding;
    amenities: ComponentEmbedding;
    characteristics: ComponentEmbedding;
    price: ComponentEmbedding;
  } {
    const organized = {
      location: results.find(r => r.component === 'location')!,
      property: results.find(r => r.component === 'property')!,
      amenities: results.find(r => r.component === 'amenities')!,
      characteristics: results.find(r => r.component === 'characteristics')!,
      price: results.find(r => r.component === 'price')!,
    };

    return organized;
  }

  /**
   * Crea un embedding vacío para componentes sin contenido
   */
  private createEmptyEmbedding(component: string, startTime: number): ComponentEmbedding {
    return {
      component,
      text: '',
      embedding: [], // Vector vacío
      isEmpty: true,
      processingTime: Date.now() - startTime,
    };
  }

  /**
   * Crea un resultado vacío para casos de error
   */
  private createEmptyResult(processingTime: number): MultiComponentEmbeddingResult {
    const emptyEmbedding = (component: string): ComponentEmbedding => ({
      component,
      text: '',
      embedding: [],
      isEmpty: true,
      processingTime: 0,
    });

    return {
      location: emptyEmbedding('location'),
      property: emptyEmbedding('property'),
      amenities: emptyEmbedding('amenities'),
      characteristics: emptyEmbedding('characteristics'),
      price: emptyEmbedding('price'),
      totalProcessingTime: processingTime,
      successfulComponents: 0,
    };
  }

  /**
   * Valida si el resultado tiene embeddings útiles
   */
  isValidResult(result: MultiComponentEmbeddingResult): boolean {
    return result.successfulComponents > 0;
  }

  /**
   * Obtiene estadísticas del resultado
   */
  getResultStats(result: MultiComponentEmbeddingResult): {
    successRate: number;
    averageProcessingTime: number;
    primaryComponent: string;
    hasLocation: boolean;
  } {
    const components = [result.location, result.property, result.amenities, result.characteristics, result.price];
    const nonEmptyComponents = components.filter(c => !c.isEmpty);
    
    const averageProcessingTime = nonEmptyComponents.length > 0
      ? nonEmptyComponents.reduce((sum, c) => sum + c.processingTime, 0) / nonEmptyComponents.length
      : 0;

    const primaryComponent = nonEmptyComponents.length > 0
      ? nonEmptyComponents.reduce((a, b) => a.text.length > b.text.length ? a : b).component
      : 'none';

    return {
      successRate: result.successfulComponents / 4,
      averageProcessingTime,
      primaryComponent,
      hasLocation: !result.location.isEmpty,
    };
  }
}
