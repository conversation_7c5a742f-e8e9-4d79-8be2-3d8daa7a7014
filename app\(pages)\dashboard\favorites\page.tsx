"use client";

import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { PropertyCard } from "@/components/marketplace/property-card";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Heart, Home, Search } from "lucide-react";
import Link from "next/link";
import { useUser } from "@clerk/nextjs";

export default function DashboardFavoritesPage() {
  const { user } = useUser();
  const [paginationOpts, setPaginationOpts] = useState({
    numItems: 12,
    cursor: undefined as string | undefined,
  });

  const favoritesResult = useQuery(
    api.properties.getUserFavorites,
    user ? { paginationOpts } : "skip"
  );

  const toggleFavorite = useMutation(api.properties.toggleFavorite);

  const handleToggleFavorite = async (propertyId: string) => {
    try {
      await toggleFavorite({ propertyId: propertyId as any });
    } catch (error) {
      console.error("Error toggling favorite:", error);
    }
  };

  const loadMore = () => {
    if (favoritesResult?.continueCursor) {
      setPaginationOpts(prev => ({
        ...prev,
        cursor: favoritesResult.continueCursor
      }));
    }
  };

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <Heart className="h-16 w-16 mx-auto text-gray-400 mb-4" />
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Inicia sesión para ver tus favoritos
        </h1>
        <p className="text-gray-600 mb-6">
          Guarda las propiedades que más te gusten para encontrarlas fácilmente después
        </p>
        <Button asChild>
          <Link href="/sign-in">Iniciar Sesión</Link>
        </Button>
      </div>
    );
  }

  if (favoritesResult === undefined) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Mis Favoritos
          </h1>
          <p className="text-gray-600">
            Propiedades que has guardado como favoritas
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_: any, i: any) => (
            <Card key={i} className="overflow-hidden">
              <Skeleton className="h-48 w-full" />
              <CardContent className="p-4">
                <Skeleton className="h-4 w-3/4 mb-2" />
                <Skeleton className="h-4 w-1/2 mb-3" />
                <Skeleton className="h-6 w-1/3" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!favoritesResult?.page || favoritesResult.page.length === 0) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <Heart className="h-16 w-16 mx-auto text-gray-400 mb-4" />
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          No tienes favoritos aún
        </h1>
        <p className="text-gray-600 mb-6">
          Explora propiedades y guarda las que más te gusten haciendo clic en el corazón
        </p>
        <div className="flex gap-4 justify-center">
          <Button asChild variant="outline">
            <Link href="/properties">
              <Search className="h-4 w-4 mr-2" />
              Explorar Propiedades
            </Link>
          </Button>
          <Button asChild>
            <Link href="/">
              <Home className="h-4 w-4 mr-2" />
              Ir al Inicio
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Mis Favoritos
        </h1>
        <p className="text-gray-600">
          {favoritesResult.page.length} propiedades guardadas como favoritas
        </p>
      </div>

      {/* Grid de propiedades */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {favoritesResult.page.map((property: any) => (
          <div key={property._id} className="relative">
            <PropertyCard
              property={property}
              isFavorite={true}
              onFavorite={() => handleToggleFavorite(property._id)}
            />
            <div className="absolute top-2 left-2 bg-white/90 backdrop-blur-sm rounded-full px-2 py-1 text-xs text-gray-600">
              Agregado {new Date(property.addedToFavoritesAt).toLocaleDateString()}
            </div>
          </div>
        ))}
      </div>

      {/* Load More Button */}
      {!favoritesResult.isDone && (
        <div className="text-center">
          <Button
            onClick={loadMore}
            variant="outline"
            className="min-w-[200px]"
          >
            Cargar más favoritos
          </Button>
        </div>
      )}

      {/* Actions */}
      <div className="mt-12 text-center">
        <div className="bg-gray-50 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            ¿Buscas algo específico?
          </h3>
          <p className="text-gray-600 mb-4">
            Usa filtros avanzados para encontrar exactamente lo que necesitas
          </p>
          <Button asChild>
            <Link href="/properties">
              <Search className="h-4 w-4 mr-2" />
              Buscar más propiedades
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
