"use client";

import React, { useState } from 'react';
import { useMutation, useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Calendar,
  Clock,
  User,
  Home,
  Check,
  X,
  MessageSquare,
  CreditCard,
  Lock,
  Eye,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { CollapsibleItem } from "@/components/ui/collapsible-card";
import { ManagedList } from "@/components/ui/list-controls";
import { formatTimeFor12h, formatDateLocal, formatTimeRange } from '@/lib/time-utils';
import Link from 'next/link';
import { toast } from 'sonner';

interface RequestsViewProps {
  requests: any[];
}

// Componente para mostrar una solicitud con nuevo sistema "pago por respuesta"
function ProtectedRequestCard({ request }: { request: any }) {
  // Si la solicitud ya fue respondida, mostrar información completa
  if (request.hasBeenResponded) {
    return <RequestCard request={request} />;
  }

  // Mostrar vista limitada hasta que se responda
  return <LimitedRequestCard request={request} />;
}

// Componente para vista limitada de la solicitud (antes de responder)
function LimitedRequestCard({ request }: { request: any }) {
  const [isResponding, setIsResponding] = useState(false);
  const [response, setResponse] = useState('');
  const [finalStartTime, setFinalStartTime] = useState('');
  const [finalEndTime, setFinalEndTime] = useState('');
  const [location, setLocation] = useState('');

  const respondToRequest = useMutation(api.appointments.respondToRequest);

  // Verificar si puede responder (créditos disponibles)
  const canRespond = useQuery(api.subscriptions.canPerformAction, {
    action: "respond_to_appointment",
    resourceId: request._id,
  });

  // Obtener suscripción actual para créditos actualizados
  const subscription = useQuery(api.subscriptions.getUserSubscription);

  const handleApprove = async () => {
    if (!finalStartTime || !finalEndTime) {
      toast.error("Por favor selecciona fecha y hora final");
      return;
    }

    try {
      await respondToRequest({
        requestId: request._id,
        approved: true,
        response: response || "Cita aprobada",
        finalStartTime,
        finalEndTime,
        location: location || undefined,
      });
      setIsResponding(false);
      toast.success("Cita aprobada. Información del solicitante desbloqueada.");
    } catch (error) {
      console.error('Error approving request:', error);
      toast.error("Error al aprobar la cita");
    }
  };

  const handleReject = async () => {
    if (!response.trim()) {
      toast.error("Por favor proporciona una razón para el rechazo");
      return;
    }

    try {
      await respondToRequest({
        requestId: request._id,
        approved: false,
        response,
      });
      setIsResponding(false);
      toast.success("Cita rechazada. Información del solicitante desbloqueada.");
    } catch (error) {
      console.error('Error rejecting request:', error);
      toast.error("Error al rechazar la cita");
    }
  };

  // Determinar tipo de cita
  const getAppointmentType = (type: string) => {
    switch (type) {
      case 'property_viewing': return 'Visita a Propiedad';
      case 'consultation': return 'Consulta General';
      case 'negotiation': return 'Negociación';
      case 'document_signing': return 'Firma de Documentos';
      default: return 'Cita';
    }
  };

  const getMeetingType = (meetingType: string) => {
    switch (meetingType) {
      case 'in_person': return 'Presencial';
      case 'video_call': return 'Videollamada';
      case 'phone_call': return 'Llamada telefónica';
      default: return 'Presencial';
    }
  };

  // Si no tiene créditos suficientes, mostrar mensaje de créditos insuficientes
  if (canRespond && !canRespond.canPerform) {
    return (
      <Card className="border-l-4 border-l-red-400">
        <CardContent className="p-6">
          <div className="text-center space-y-4">
            <div className="flex justify-center">
              <div className="bg-red-100 p-3 rounded-full">
                <Lock className="w-8 h-8 text-red-600" />
              </div>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Solicitud Bloqueada - Créditos Insuficientes
              </h3>
              <p className="text-gray-600 mb-4">
                Necesitas <strong>{canRespond.requiredCredits} créditos</strong> para ver esta solicitud de cita.
              </p>
              <div className="bg-gray-50 rounded-lg p-3 mb-4">
                <p className="text-sm text-gray-700">
                  <strong>Tienes:</strong> {canRespond.availableCredits} créditos disponibles<br/>
                  <strong>Necesitas:</strong> {canRespond.requiredCredits} créditos para ver la solicitud
                </p>
              </div>
              <Link href="/dashboard/finance">
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <CreditCard className="w-4 h-4 mr-2" />
                  Comprar Créditos
                </Button>
              </Link>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-l-4 border-l-yellow-400">
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* Vista previa limitada - Solo información básica */}
          <div className="flex items-start justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {getAppointmentType(request.type)}
              </h3>
              <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                <span className="flex items-center gap-1">
                  <Calendar className="w-3 h-3" />
                  {formatDateLocal(request.requestedStartTime)}
                </span>
                <span className="flex items-center gap-1">
                  <Clock className="w-3 h-3" />
                  {formatTimeRange(request.requestedStartTime, request.requestedEndTime)}
                </span>
                <span className="flex items-center gap-1">
                  <User className="w-3 h-3" />
                  {getMeetingType(request.meetingType)}
                </span>
              </div>
            </div>
            <Badge className="bg-yellow-100 text-yellow-800">
              Pendiente
            </Badge>
          </div>

          {/* Propiedad relacionada */}
          {request.property && (
            <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg">
              <Home className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-900">
                Propiedad: {request.property.title}
              </span>
            </div>
          )}

          {/* Información oculta */}
          <div className="bg-gray-100 rounded-lg p-4">
            <div className="flex items-center gap-2 text-gray-500 mb-2">
              <Lock className="w-4 h-4" />
              <span className="text-sm font-medium">Información oculta hasta responder</span>
            </div>
            <div className="space-y-1 text-sm text-gray-400">
              <p>• Nombre completo del solicitante</p>
              <p>• Email de contacto</p>
              <p>• Teléfono (si proporcionado)</p>
              <p>• Mensaje personalizado</p>
            </div>
          </div>

          {/* Formulario de respuesta */}
          {!isResponding ? (
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                {canRespond?.isPremiumAccess ? (
                  <span>Responde <strong>gratis</strong> con tu plan premium</span>
                ) : (
                  <span>Responder cuesta <strong>{canRespond?.requiredCredits || 3} créditos</strong></span>
                )}
              </div>
              <Button
                onClick={() => setIsResponding(true)}
                className="bg-blue-600 hover:bg-blue-700"
                disabled={canRespond && !canRespond.canPerform}
              >
                <MessageSquare className="w-4 h-4 mr-2" />
                Responder
              </Button>
            </div>
          ) : (
            <div className="space-y-4 border-t pt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-700">Fecha y Hora Final</Label>
                  <div className="space-y-2">
                    <Input
                      type="datetime-local"
                      value={finalStartTime}
                      onChange={(e) => setFinalStartTime(e.target.value)}
                      placeholder="Hora de inicio"
                    />
                    <Input
                      type="datetime-local"
                      value={finalEndTime}
                      onChange={(e) => setFinalEndTime(e.target.value)}
                      placeholder="Hora de fin"
                    />
                  </div>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">Ubicación (opcional)</Label>
                  <Input
                    value={location}
                    onChange={(e) => setLocation(e.target.value)}
                    placeholder="Dirección o lugar de encuentro"
                  />
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-700">Mensaje de respuesta</Label>
                <Textarea
                  value={response}
                  onChange={(e) => setResponse(e.target.value)}
                  placeholder="Mensaje para el solicitante..."
                  className="min-h-[80px]"
                />
              </div>

              <div className="flex gap-2">
                <Button
                  onClick={handleApprove}
                  className="bg-green-600 hover:bg-green-700"
                  disabled={!finalStartTime || !finalEndTime}
                >
                  <Check className="w-4 h-4 mr-2" />
                  Aprobar ({canRespond?.requiredCredits || 3} créditos)
                </Button>
                <Button
                  onClick={handleReject}
                  variant="destructive"
                  disabled={!response.trim()}
                >
                  <X className="w-4 h-4 mr-2" />
                  Rechazar ({canRespond?.requiredCredits || 3} créditos)
                </Button>
                <Button
                  onClick={() => setIsResponding(false)}
                  variant="outline"
                >
                  Cancelar
                </Button>
              </div>
            </div>
          )}

          {/* Mensaje de créditos insuficientes */}
          {canRespond && !canRespond.canPerform && (
            <Alert>
              <CreditCard className="h-4 w-4" />
              <AlertDescription>
                <div className="flex items-center justify-between">
                  <span>
                    Necesitas <strong>{canRespond.requiredCredits || 3} créditos</strong> para responder.
                    Tienes <strong>{subscription ? (subscription.credits - subscription.creditsUsed) : (canRespond.availableCredits || 0)}</strong> disponibles.
                  </span>
                  <Link href="/dashboard/finance">
                    <Button size="sm" className="ml-4">
                      Comprar Créditos
                    </Button>
                  </Link>
                </div>
              </AlertDescription>
            </Alert>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Componente para mostrar la solicitud completa
function RequestCard({ request }: { request: any }) {
  const [respondingTo, setRespondingTo] = useState<string | null>(null);
  const [response, setResponse] = useState('');
  const [finalStartTime, setFinalStartTime] = useState('');
  const [finalEndTime, setFinalEndTime] = useState('');
  const [location, setLocation] = useState('');
  const [meetingUrl, setMeetingUrl] = useState('');

  const respondToRequest = useMutation(api.appointments.respondToRequest);

  const handleApprove = async (requestId: string) => {
    try {
      await respondToRequest({
        requestId: requestId as Id<"appointmentRequests">,
        approved: true,
        response,
        finalStartTime: finalStartTime || undefined,
        finalEndTime: finalEndTime || undefined,
        location: location || undefined,
        meetingUrl: meetingUrl || undefined,
      });
      resetForm();
    } catch (error) {
      console.error('Error approving request:', error);
    }
  };

  const handleReject = async (requestId: string) => {
    try {
      await respondToRequest({
        requestId: requestId as Id<"appointmentRequests">,
        approved: false,
        response,
      });
      resetForm();
    } catch (error) {
      console.error('Error rejecting request:', error);
    }
  };

  const resetForm = () => {
    setRespondingTo(null);
    setResponse('');
    setFinalStartTime('');
    setFinalEndTime('');
    setLocation('');
    setMeetingUrl('');
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'property_viewing': return 'Visita de Propiedad';
      case 'consultation': return 'Consulta';
      case 'negotiation': return 'Negociación';
      case 'document_signing': return 'Firma de Documentos';
      case 'other': return 'Otro';
      default: return type;
    }
  };

  const getMeetingTypeText = (meetingType: string) => {
    switch (meetingType) {
      case 'video_call': return 'Videollamada';
      case 'phone_call': return 'Llamada telefónica';
      case 'in_person': return 'Presencial';
      default: return meetingType;
    }
  };

  return (
    <Card className="border-l-4 border-l-green-400">
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* Header */}
          <div className="flex items-start justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {getTypeText(request.type)}
              </h3>
              <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                <span className="flex items-center gap-1">
                  <User className="w-3 h-3" />
                  {request.guestName}
                </span>
                <span className="flex items-center gap-1">
                  <Calendar className="w-3 h-3" />
                  {getMeetingTypeText(request.meetingType)}
                </span>
              </div>
            </div>
            <Badge className="bg-green-100 text-green-800">
              Desbloqueada
            </Badge>
          </div>

          {/* Detalles de la solicitud */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 bg-gray-50 rounded-lg p-4">
            <div>
              <Label className="text-sm font-medium text-gray-700">Fecha y Hora Solicitada</Label>
              <div className="mt-1 space-y-1">
                <p className="text-sm text-gray-900">
                  {formatDateLocal(request.requestedStartTime)}
                </p>
                <p className="text-sm text-gray-600">
                  {formatTimeRange(request.requestedStartTime, request.requestedEndTime)}
                </p>
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium text-gray-700">Información de Contacto</Label>
              <div className="mt-1 space-y-1">
                <p className="text-sm text-gray-900">{request.guestEmail}</p>
                {request.guestPhone && (
                  <p className="text-sm text-gray-600">{request.guestPhone}</p>
                )}
              </div>
            </div>
          </div>

          {/* Propiedad relacionada */}
          {request.property && (
            <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg">
              <Home className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-900">
                Propiedad: {request.property.title}
              </span>
            </div>
          )}

          {/* Mensaje del cliente */}
          {request.message && (
            <div className="bg-white border rounded-lg p-3">
              <Label className="text-sm font-medium text-gray-700">Mensaje del Cliente</Label>
              <p className="text-sm text-gray-900 mt-1">{request.message}</p>
            </div>
          )}

          {/* Formulario de respuesta */}
          {respondingTo === request._id ? (
            <div className="border-t pt-4 space-y-4">
              <div>
                <Label className="text-sm font-medium">Respuesta (opcional)</Label>
                <Textarea
                  placeholder="Escribe tu respuesta al cliente..."
                  value={response}
                  onChange={(e) => setResponse(e.target.value)}
                  className="mt-1"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Hora de inicio</Label>
                  <Input
                    type="datetime-local"
                    value={finalStartTime}
                    onChange={(e) => setFinalStartTime(e.target.value)}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label className="text-sm font-medium">Hora de fin</Label>
                  <Input
                    type="datetime-local"
                    value={finalEndTime}
                    onChange={(e) => setFinalEndTime(e.target.value)}
                    className="mt-1"
                  />
                </div>
              </div>

              {request.meetingType === 'in_person' && (
                <div>
                  <Label className="text-sm font-medium">Ubicación</Label>
                  <Input
                    placeholder="Dirección del encuentro"
                    value={location}
                    onChange={(e) => setLocation(e.target.value)}
                    className="mt-1"
                  />
                </div>
              )}

              {request.meetingType === 'video_call' && (
                <div>
                  <Label className="text-sm font-medium">URL de videollamada</Label>
                  <Input
                    placeholder="https://meet.google.com/..."
                    value={meetingUrl}
                    onChange={(e) => setMeetingUrl(e.target.value)}
                    className="mt-1"
                  />
                </div>
              )}

              <div className="flex gap-2">
                <Button
                  onClick={() => handleApprove(request._id)}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <Check className="w-4 h-4 mr-2" />
                  Aprobar
                </Button>
                <Button
                  onClick={() => handleReject(request._id)}
                  variant="destructive"
                >
                  <X className="w-4 h-4 mr-2" />
                  Rechazar
                </Button>
                <Button
                  onClick={resetForm}
                  variant="outline"
                >
                  Cancelar
                </Button>
              </div>
            </div>
          ) : (
            <div className="border-t pt-4">
              <Button
                onClick={() => setRespondingTo(request._id)}
                className="w-full"
              >
                Responder
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export function RequestsView({ requests }: RequestsViewProps) {
  if (requests.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No hay solicitudes pendientes
          </h3>
          <p className="text-gray-600">
            Las solicitudes de cita aparecerán aquí cuando los clientes las envíen
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <ManagedList
      items={requests}
      getItemId={(request) => request._id}
      title="Solicitudes Pendientes"
      icon={MessageSquare}
      defaultExpanded={false}
      showControls={true}
      controlsVariant="compact"
      renderItem={(request, isExpanded, onToggle) => (
        <CollapsibleItem
          key={request._id}
          id={request._id}
          data={request}
          type="request"
          defaultExpanded={isExpanded}
          onToggle={onToggle}
        >
          <ProtectedRequestCard request={request} />
        </CollapsibleItem>
      )}
    />
  );
}