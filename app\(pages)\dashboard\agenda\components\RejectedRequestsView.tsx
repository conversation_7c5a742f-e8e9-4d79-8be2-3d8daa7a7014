"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Calendar,
  User,
  Home,
  MessageSquare,
  X,
  Clock,
  Mail,
  Phone,
  RotateCcw
} from 'lucide-react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { CollapsibleItem } from "@/components/ui/collapsible-card";
import { ManagedList } from "@/components/ui/list-controls";
import { formatTimeFor12h, formatDateLocal, formatTimeRange } from '@/lib/time-utils';

interface RejectedRequestsViewProps {
  requests: any[];
}

// Componente para mostrar una solicitud rechazada
function RejectedRequestCard({ request }: { request: any }) {
  const getTypeText = (type: string) => {
    switch (type) {
      case 'property_viewing': return 'Visita de Propiedad';
      case 'consultation': return 'Consulta';
      case 'negotiation': return 'Negociación';
      case 'document_signing': return 'Firma de Documentos';
      case 'other': return 'Otro';
      default: return type;
    }
  };

  const getMeetingTypeText = (meetingType: string) => {
    switch (meetingType) {
      case 'video_call': return 'Videollamada';
      case 'phone_call': return 'Llamada telefónica';
      case 'in_person': return 'Presencial';
      default: return meetingType;
    }
  };

  const formatRejectionDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "dd 'de' MMMM 'de' yyyy 'a las' HH:mm", { locale: es });
    } catch {
      return dateString;
    }
  };

  return (
    <Card className="border-l-4 border-l-red-400">
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* Header */}
          <div className="flex items-start justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {getTypeText(request.type)}
              </h3>
              <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                <span className="flex items-center gap-1">
                  <User className="w-3 h-3" />
                  {request.guestName}
                </span>
                <span className="flex items-center gap-1">
                  <Calendar className="w-3 h-3" />
                  {getMeetingTypeText(request.meetingType)}
                </span>
              </div>
            </div>
            <div className="flex flex-col items-end gap-2">
              <Badge className="bg-red-100 text-red-800">
                <X className="w-3 h-3 mr-1" />
                Rechazada
              </Badge>
              {request.respondedAt && (
                <span className="text-xs text-gray-500">
                  {formatRejectionDate(request.respondedAt)}
                </span>
              )}
            </div>
          </div>

          {/* Detalles de la solicitud original */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 bg-gray-50 rounded-lg p-4">
            <div>
              <Label className="text-sm font-medium text-gray-700">Fecha y Hora Solicitada</Label>
              <div className="mt-1 space-y-1">
                <p className="text-sm text-gray-900">
                  {formatDateLocal(request.requestedStartTime)}
                </p>
                <p className="text-sm text-gray-600">
                  {formatTimeRange(request.requestedStartTime, request.requestedEndTime)}
                </p>
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium text-gray-700">Información de Contacto</Label>
              <div className="mt-1 space-y-1">
                <div className="flex items-center gap-2">
                  <Mail className="w-3 h-3 text-gray-400" />
                  <p className="text-sm text-gray-900">{request.guestEmail}</p>
                </div>
                {request.guestPhone && (
                  <div className="flex items-center gap-2">
                    <Phone className="w-3 h-3 text-gray-400" />
                    <p className="text-sm text-gray-600">{request.guestPhone}</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Propiedad relacionada */}
          {request.property && (
            <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg">
              <Home className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-900">
                Propiedad: {request.property.title}
              </span>
            </div>
          )}

          {/* Mensaje original del cliente */}
          {request.message && (
            <div className="bg-white border rounded-lg p-3">
              <Label className="text-sm font-medium text-gray-700">Mensaje Original del Cliente</Label>
              <p className="text-sm text-gray-900 mt-1">{request.message}</p>
            </div>
          )}

          {/* Razón del rechazo */}
          {request.response && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <Label className="text-sm font-medium text-red-700">Razón del Rechazo</Label>
              <p className="text-sm text-red-900 mt-1">{request.response}</p>
            </div>
          )}

          {/* Información adicional */}
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <Label className="text-xs font-medium text-gray-600">Solicitud Creada</Label>
                <p className="text-gray-900">
                  {format(new Date(request.createdAt), 'dd/MM/yyyy HH:mm', { locale: es })}
                </p>
              </div>
              <div>
                <Label className="text-xs font-medium text-gray-600">Rechazada</Label>
                <p className="text-gray-900">
                  {request.respondedAt ? 
                    format(new Date(request.respondedAt), 'dd/MM/yyyy HH:mm', { locale: es }) : 
                    'N/A'
                  }
                </p>
              </div>
              <div>
                <Label className="text-xs font-medium text-gray-600">Tiempo de Respuesta</Label>
                <p className="text-gray-900">
                  {request.respondedAt ? 
                    Math.round((new Date(request.respondedAt).getTime() - new Date(request.createdAt).getTime()) / (1000 * 60 * 60)) + ' horas' :
                    'N/A'
                  }
                </p>
              </div>
            </div>
          </div>

          {/* Acciones opcionales */}
          <div className="border-t pt-4">
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                className="text-blue-600 border-blue-200 hover:bg-blue-50"
              >
                <Mail className="w-3 h-3 mr-1" />
                Contactar Cliente
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="text-green-600 border-green-200 hover:bg-green-50"
              >
                <RotateCcw className="w-3 h-3 mr-1" />
                Proponer Nueva Fecha
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function RejectedRequestsView({ requests }: RejectedRequestsViewProps) {
  if (requests.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <div className="flex justify-center mb-4">
            <div className="bg-green-100 p-3 rounded-full">
              <X className="w-8 h-8 text-green-600" />
            </div>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No hay solicitudes rechazadas
          </h3>
          <p className="text-gray-600">
            ¡Excelente! No has rechazado ninguna solicitud de cita recientemente.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header con estadísticas */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <X className="w-5 h-5 text-red-600" />
            Solicitudes Rechazadas ({requests.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="text-center p-3 bg-red-50 rounded-lg">
              <p className="text-2xl font-bold text-red-600">{requests.length}</p>
              <p className="text-red-700">Total Rechazadas</p>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <p className="text-2xl font-bold text-gray-600">
                {requests.filter(r => r.respondedAt && 
                  (new Date().getTime() - new Date(r.respondedAt).getTime()) < 7 * 24 * 60 * 60 * 1000
                ).length}
              </p>
              <p className="text-gray-700">Esta Semana</p>
            </div>
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <p className="text-2xl font-bold text-blue-600">
                {Math.round(
                  requests.reduce((acc, r) => {
                    if (r.respondedAt) {
                      return acc + (new Date(r.respondedAt).getTime() - new Date(r.createdAt).getTime());
                    }
                    return acc;
                  }, 0) / (requests.length * 1000 * 60 * 60)
                )}h
              </p>
              <p className="text-blue-700">Tiempo Promedio de Respuesta</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Lista de solicitudes rechazadas */}
      <ManagedList
        items={requests}
        getItemId={(request) => request._id}
        title="Historial de Rechazos"
        icon={X}
        defaultExpanded={false}
        showControls={true}
        controlsVariant="compact"
        renderItem={(request, isExpanded, onToggle) => (
          <CollapsibleItem
            key={request._id}
            id={request._id}
            data={request}
            type="request"
            defaultExpanded={isExpanded}
            onToggle={onToggle}
          >
            <RejectedRequestCard request={request} />
          </CollapsibleItem>
        )}
      />
    </div>
  );
}
