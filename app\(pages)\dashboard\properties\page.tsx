"use client";

import { useState, useEffect } from "react";
import { useUser } from "@clerk/clerk-react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { CreditsDisplay } from "@/components/ui/credits-display";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Building2,
  Plus,
  Edit,
  Trash2,
  MoreH<PERSON>zontal,
  Eye,
  MapPin,
  DollarSign,
  Star,
  Crown,
  Clock,
  AlertTriangle,
  Search,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { toast } from "sonner";
import { useAlert } from "@/hooks/use-alert";
import { useDebounce } from "use-debounce";

export default function MyPropertiesPage() {
  const { user } = useUser();
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize] = useState<number>(10);
  const { showConfirm } = useAlert();
  
  const [debouncedSearchTerm] = useDebounce(searchTerm, 300);
  
  const userPropertiesData = useQuery(api.properties.getUserPropertiesPaginated, {
    userId: user?.id || "",
    page: currentPage,
    limit: pageSize,
    search: debouncedSearchTerm,
    status: selectedStatus,
  });

  useEffect(() => {
    setCurrentPage(1);
  }, [selectedStatus, debouncedSearchTerm]);

  const updateProperty = useMutation(api.properties.updateProperty);
  const deleteProperty = useMutation(api.properties.deleteProperty);

  // Mutations para destacadas
  const featureProperty = useMutation(api.featuredProperties.featureProperty);
  const unfeatureProperty = useMutation(api.featuredProperties.unfeatureProperty);
  const promoteToHomePremium = useMutation(api.featuredProperties.promoteToHomePremium);
  const removeFromHomePremium = useMutation(api.featuredProperties.removeFromHomePremium);

  // Query para estado de destacadas
  const featuredStatus = useQuery(api.featuredProperties.getUserFeaturedStatus);

  const subscription = useQuery(api.subscriptions.getUserSubscription);

  const userProperties = userPropertiesData?.properties || [];
  const pagination = userPropertiesData?.pagination;

  const getStatusBadge = (status: string) => {
    const statusMap = {
      for_sale: { label: 'En Venta', variant: 'default' as const, color: 'bg-blue-100 text-blue-800' },
      for_rent: { label: 'En Alquiler', variant: 'secondary' as const, color: 'bg-blue-100 text-blue-800' },
      sold: { label: 'Vendido', variant: 'destructive' as const, color: 'bg-red-100 text-red-800' },
      rented: { label: 'Alquilado', variant: 'outline' as const, color: 'bg-purple-100 text-purple-800' },
      draft: { label: 'Borrador', variant: 'outline' as const, color: 'bg-gray-100 text-gray-800' },
    };
    
    return statusMap[status as keyof typeof statusMap] || { label: status, variant: 'default' as const, color: 'bg-gray-100 text-gray-800' };
  };

  const getTypeLabel = (type: string) => {
    const typeMap = {
      house: 'Casa',
      apartment: 'Apartamento',
      office: 'Oficina',
      land: 'Terreno',
      commercial: 'Comercial',
    };
    
    return typeMap[type as keyof typeof typeMap] || type;
  };

  const handleDeleteProperty = async (propertyId: Id<"properties">) => {
    showConfirm(
      "¿Estás seguro de que quieres eliminar esta propiedad? Esta acción no se puede deshacer.",
      async () => {
        try {
          await deleteProperty({ id: propertyId });
          toast.success("Propiedad eliminada exitosamente");
        } catch (error) {
          console.error("Error deleting property:", error);
          toast.error("Error al eliminar la propiedad");
        }
      },
      {
        title: "Confirmar eliminación",
        confirmText: "Eliminar",
        cancelText: "Cancelar"
      }
    );
  };

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('es-ES', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
    }).format(price);
  };

  // Funciones para destacadas
  const handleFeatureProperty = async (propertyId: Id<"properties">) => {
    try {
      const availableCredits = subscription ? subscription.credits - subscription.creditsUsed : 0;
      const requiredCredits = 10;

      if (availableCredits < requiredCredits) {
        toast.error(
          `❌ Créditos insuficientes: Necesitas ${requiredCredits} créditos, tienes ${availableCredits}. ` +
          `Ve a Finanzas para obtener más créditos.`,
          { duration: 6000 }
        );
        return;
      }

      const result = await featureProperty({ propertyId });
      toast.success(`✅ Propiedad destacada exitosamente! Costo: ${result.creditsCost} créditos`);
    } catch (error: any) {
      toast.error(`❌ Error: ${error.message}`);
    }
  };

  const handleUnfeatureProperty = async (propertyId: Id<"properties">) => {
    try {
      await unfeatureProperty({ propertyId });
      toast.success("✅ Destacado removido exitosamente");
    } catch (error: any) {
      toast.error(`❌ Error: ${error.message}`);
    }
  };

  const handlePromoteToPremium = async (propertyId: Id<"properties">) => {
    try {
      const availableCredits = subscription ? subscription.credits - subscription.creditsUsed : 0;
      const requiredCredits = 25;

      if (availableCredits < requiredCredits) {
        toast.error(
          `❌ Créditos insuficientes: Necesitas ${requiredCredits} créditos, tienes ${availableCredits}. ` +
          `Ve a Finanzas para obtener más créditos.`,
          { duration: 6000 }
        );
        return;
      }

      const result = await promoteToHomePremium({ propertyId });
      toast.success(`✅ Propiedad promovida a Premium Home! Costo: ${result.creditsCost} créditos`);
    } catch (error: any) {
      toast.error(`❌ Error: ${error.message}`);
    }
  };

  const handleRemoveFromPremium = async (propertyId: Id<"properties">) => {
    try {
      await removeFromHomePremium({ propertyId });
      toast.success("✅ Removido de Premium Home exitosamente");
    } catch (error: any) {
      toast.error(`❌ Error: ${error.message}`);
    }
  };

  // Helper para obtener estado de una propiedad
  const getPropertyStatus = (propertyId: string) => {
    return featuredStatus?.properties.find((p: any) => p.id === propertyId) || {
      isFeatured: false,
      isPremium: false,
      canFeature: true,
      canPremium: true,
    };
  };

  // Helper para verificar créditos disponibles
  const hasCreditsFor = (action: 'feature' | 'premium') => {
    if (!subscription) return false;
    const availableCredits = subscription.credits - subscription.creditsUsed;
    const requiredCredits = action === 'feature' ? 10 : 25;
    return availableCredits >= requiredCredits;
  };

  return (
    <div className="flex flex-col gap-6 p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-semibold tracking-tight">Mis Propiedades</h1>
          <p className="text-muted-foreground">
            Gestiona tus propiedades y promociones
          </p>
        </div>
        
        <div className="flex items-center gap-4">
          <CreditsDisplay variant="compact" />
          <Link href="/dashboard/properties/new">
            <Button className="gap-2">
              <Plus className="h-4 w-4" />
              Nueva Propiedad
            </Button>
          </Link>
        </div>
      </div>

      {subscription && (subscription.credits - subscription.creditsUsed) < 10 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex items-center gap-3">
            <AlertTriangle className="h-5 w-5 text-red-600" />
            <div className="flex-1">
              <h3 className="font-medium text-red-800">Créditos Críticos</h3>
              <p className="text-sm text-red-700">
                Solo tienes {subscription.credits - subscription.creditsUsed} créditos. 
                La mayoría de funcionalidades premium están deshabilitadas.
              </p>
            </div>
            <Link href="/dashboard/finance">
              <Button size="sm" className="bg-red-600 hover:bg-red-700">
                Recargar Ahora
              </Button>
            </Link>
          </div>
        </div>
      )}

      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Buscar por título de propiedad..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            {pagination && (
              <div className="text-sm text-gray-600">
                {pagination.totalCount > 0 ? (
                  `${pagination.totalCount} propiedad${pagination.totalCount !== 1 ? 'es' : ''} encontrada${pagination.totalCount !== 1 ? 's' : ''}`
                ) : (
                  "No se encontraron propiedades"
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-2 flex-wrap">
        <Button
          variant={selectedStatus === "all" ? "default" : "outline"}
          size="sm"
          onClick={() => setSelectedStatus("all")}
          className={selectedStatus === "all" ? "bg-blue-600 hover:bg-blue-700" : ""}
        >
          Todas
        </Button>
        <Button
          variant={selectedStatus === "for_sale" ? "default" : "outline"}
          size="sm"
          onClick={() => setSelectedStatus("for_sale")}
          className={selectedStatus === "for_sale" ? "bg-blue-600 hover:bg-blue-700" : ""}
        >
          En Venta
        </Button>
        <Button
          variant={selectedStatus === "for_rent" ? "default" : "outline"}
          size="sm"
          onClick={() => setSelectedStatus("for_rent")}
          className={selectedStatus === "for_rent" ? "bg-blue-600 hover:bg-blue-700" : ""}
        >
          En Alquiler
        </Button>
        <Button
          variant={selectedStatus === "draft" ? "default" : "outline"}
          size="sm"
          onClick={() => setSelectedStatus("draft")}
          className={selectedStatus === "draft" ? "bg-blue-600 hover:bg-blue-700" : ""}
        >
          Borradores
        </Button>
        <Button
          variant={selectedStatus === "sold" ? "default" : "outline"}
          size="sm"
          onClick={() => setSelectedStatus("sold")}
          className={selectedStatus === "sold" ? "bg-blue-600 hover:bg-blue-700" : ""}
        >
          Vendidas
        </Button>
        <Button
          variant={selectedStatus === "rented" ? "default" : "outline"}
          size="sm"
          onClick={() => setSelectedStatus("rented")}
          className={selectedStatus === "rented" ? "bg-blue-600 hover:bg-blue-700" : ""}
        >
          Alquiladas
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Propiedades ({pagination?.totalCount || 0})</CardTitle>
          <CardDescription>
            Administra el estado y la información de tus propiedades
          </CardDescription>
        </CardHeader>
        <CardContent>
          {userProperties.length > 0 ? (
            <div>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Propiedad</TableHead>
                    <TableHead>Tipo</TableHead>
                    <TableHead>Ubicación</TableHead>
                    <TableHead>Precio</TableHead>
                    <TableHead>Estado</TableHead>
                    <TableHead className="text-right">Acciones</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {userProperties.map((property: any) => {
                    const statusInfo = getStatusBadge(property.status);
                    return (
                      <TableRow key={property._id}>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <div className="relative w-12 h-12 rounded-lg overflow-hidden bg-gray-100">
                              {property.images && property.images.length > 0 ? (
                                <Image
                                  src={property.images[0]}
                                  alt={property.title}
                                  fill
                                  className="object-cover"
                                />
                              ) : (
                                <div className="w-full h-full flex items-center justify-center">
                                  <Building2 className="h-6 w-6 text-gray-400" />
                                </div>
                              )}
                            </div>
                            <div>
                              <div>
                                <p className="font-medium line-clamp-1">{property.title}</p>
                                <p className="text-sm text-muted-foreground">
                                  {property.area} m² • {property.bedrooms || 0} hab • {property.bathrooms || 0} baños
                                </p>
                              </div>
                              {/* Badges de destacadas */}
                              {(() => {
                                const status = getPropertyStatus(property._id);
                                return (
                                  <div className="flex gap-1 mt-1">
                                    {status.isFeatured && (
                                      <Badge variant="secondary" className="text-xs bg-yellow-100 text-yellow-800">
                                        <Star className="h-3 w-3 mr-1" />
                                        Destacada
                                      </Badge>
                                    )}
                                    {status.isPremium && (
                                      <Badge variant="secondary" className="text-xs bg-purple-100 text-purple-800">
                                        <Crown className="h-3 w-3 mr-1" />
                                        Premium
                                      </Badge>
                                    )}
                                  </div>
                                );
                              })()}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {getTypeLabel(property.type)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <MapPin className="h-4 w-4 text-gray-400" />
                            <span className="text-sm">
                              {property.location?.level1?.name || 'Sin ubicación'}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <DollarSign className="h-4 w-4 text-gray-400" />
                            <span className="font-medium">
                              {formatPrice(property.price, property.currency)}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge 
                            variant={statusInfo.variant}
                            className={statusInfo.color}
                          >
                            {statusInfo.label}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Abrir menú</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Acciones</DropdownMenuLabel>
                              <DropdownMenuItem asChild>
                                <Link href={`/properties/${property._id}`}>
                                  <Eye className="h-4 w-4 mr-2" />
                                  Ver Propiedad
                                </Link>
                              </DropdownMenuItem>
                              <DropdownMenuItem asChild>
                                <Link href={`/dashboard/properties/edit/${property._id}`}>
                                  <Edit className="h-4 w-4 mr-2" />
                                  Editar
                                </Link>
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />

                              {/* Opciones de Destacadas */}
                              {(() => {
                                const status = getPropertyStatus(property._id);
                                const canAffordFeature = hasCreditsFor('feature');
                                const canAffordPremium = hasCreditsFor('premium');

                                return (
                                  <>
                                    {!status.isFeatured ? (
                                      <DropdownMenuItem
                                        onClick={() => handleFeatureProperty(property._id)}
                                        disabled={!canAffordFeature}
                                        className={!canAffordFeature ? "opacity-50" : ""}
                                      >
                                        <Star className="h-4 w-4 mr-2" />
                                        Destacar (10 créditos)
                                      </DropdownMenuItem>
                                    ) : (
                                      <DropdownMenuItem
                                        onClick={() => handleUnfeatureProperty(property._id)}
                                      >
                                        <Star className="h-4 w-4 mr-2 fill-current" />
                                        Quitar Destacado
                                      </DropdownMenuItem>
                                    )}

                                    {!status.isPremium ? (
                                      <DropdownMenuItem
                                        onClick={() => handlePromoteToPremium(property._id)}
                                        disabled={!canAffordPremium}
                                        className={!canAffordPremium ? "opacity-50" : ""}
                                      >
                                        <Crown className="h-4 w-4 mr-2" />
                                        Premium Home (25 créditos)
                                      </DropdownMenuItem>
                                    ) : (
                                      <DropdownMenuItem
                                        onClick={() => handleRemoveFromPremium(property._id)}
                                      >
                                        <Crown className="h-4 w-4 mr-2 fill-current" />
                                        Quitar Premium
                                      </DropdownMenuItem>
                                    )}
                                    <DropdownMenuSeparator />
                                  </>
                                );
                              })()}

                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() => handleDeleteProperty(property._id)}
                                className="text-red-600"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Eliminar
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
              
              {pagination && pagination.totalCount > 0 && (
                <div className="flex items-center justify-between mt-6 pt-6 border-t">
                  <div className="text-sm text-gray-600">
                    Página {pagination.page} de {pagination.totalPages} • 
                    Mostrando {((pagination.page - 1) * pagination.limit) + 1}-{Math.min(pagination.page * pagination.limit, pagination.totalCount)} de {pagination.totalCount} propiedades
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(1)}
                      disabled={pagination.page === 1}
                      title="Ir al inicio"
                    >
                      <ChevronsLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(pagination.page - 1)}
                      disabled={!pagination.hasPrevPage}
                    >
                      <ChevronLeft className="h-4 w-4" />
                      Anterior
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(pagination.page + 1)}
                      disabled={!pagination.hasNextPage}
                    >
                      Siguiente
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(pagination.totalPages)}
                      disabled={pagination.page === pagination.totalPages}
                      title="Ir al final"
                    >
                      <ChevronsRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-12">
              <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {selectedStatus === "all" ? "No tienes propiedades aún" : `No hay propiedades ${getStatusBadge(selectedStatus).label.toLowerCase()}`}
              </h3>
              <p className="text-gray-500 mb-6">
                {selectedStatus === "all" 
                  ? "Comienza publicando tu primera propiedad en el marketplace."
                  : `Cambia los filtros para ver propiedades con otros estados.`
                }
              </p>
              {selectedStatus === "all" && (
                <Link href="/dashboard/properties/new">
                  <Button className="bg-blue-600 hover:bg-blue-700">
                    <Plus className="h-4 w-4 mr-2" />
                    Publicar Primera Propiedad
                  </Button>
                </Link>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
