import { v } from "convex/values";
import { action, internalAction, internalMutation, internalQuery, query } from "./_generated/server";
import { internal } from "./_generated/api";
import { PROPERTY_TYPES, PROPERTY_STATUS } from "./schema";

/**
 * Acción para indexar una propiedad en Qdrant
 */
export const indexProperty = internalAction({
  args: {
    propertyId: v.id("properties"),
  },
  handler: async (ctx, { propertyId }) => {
    try {
      // Obtener la propiedad de Convex
      const property = await ctx.runQuery(internal.properties.getPropertyForIndexing, {
        propertyId,
      });

      if (!property) {
        throw new Error(`Property ${propertyId} not found`);
      }

      // Importar dinámicamente para evitar problemas de SSR
      const { PropertyIndexer } = await import("../lib/qdrant/indexer");
      const indexer = new PropertyIndexer();

      // Indexar la propiedad
      await indexer.indexProperty(property);

      console.log(`Property ${propertyId} indexed successfully in Qdrant`);
      return { success: true, propertyId };
    } catch (error) {
      console.error(`Error indexing property ${propertyId}:`, error);
      throw error;
    }
  },
});

/**
 * Acción para eliminar una propiedad del índice
 */
export const removePropertyFromIndex = internalAction({
  args: {
    propertyId: v.string(),
  },
  handler: async (ctx, { propertyId }) => {
    try {
      const { PropertyIndexer } = await import("../lib/qdrant/indexer");
      const indexer = new PropertyIndexer();

      await indexer.removeProperty(propertyId);

      console.log(`Property ${propertyId} removed from index successfully`);
      return { success: true, propertyId };
    } catch (error) {
      console.error(`Error removing property ${propertyId} from index:`, error);
      throw error;
    }
  },
});

/**
 * Acción pública para indexar una propiedad específica
 */
export const indexSingleProperty = action({
  args: {
    propertyId: v.id("properties"),
  },
  handler: async (ctx, { propertyId }) => {
    try {
      console.log(`Indexing single property: ${propertyId}`);

      const property = await ctx.runQuery(internal.properties.getPropertyForIndexing, { propertyId });

      if (!property) {
        throw new Error(`Property ${propertyId} not found`);
      }

      const { PropertyIndexer } = await import("../lib/qdrant/indexer");
      const indexer = new PropertyIndexer();

      await indexer.indexProperty(property);

      return {
        success: true,
        message: `Property ${propertyId} indexed successfully`
      };
    } catch (error) {
      console.error(`Error indexing property ${propertyId}:`, error);
      return {
        success: false,
        message: `Failed to index property: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  },
});

/**
 * Acción para re-indexar todas las propiedades
 */
export const reindexAllProperties = action({
  args: {
    force: v.optional(v.boolean()),
    batchSize: v.optional(v.number()),
  },
  handler: async (ctx, { force = false, batchSize = 20 }): Promise<{
    success: boolean;
    message: string;
    count: number;
  }> => {
    try {
      // Obtener todas las propiedades activas
      const properties: any[] = await ctx.runQuery(internal.properties.getAllPropertiesForIndexing);

      const { PropertyIndexer } = await import("../lib/qdrant/indexer");
      const indexer = new PropertyIndexer();

      if (force) {
        await indexer.reindexAll(properties, true);
      } else {
        await indexer.indexProperties(properties, batchSize);
      }

      return {
        success: true,
        message: `Successfully ${force ? 're-indexed' : 'indexed'} ${properties.length} properties`,
        count: properties.length,
      };
    } catch (error) {
      console.error("Error during reindexing:", error);
      throw error;
    }
  },
});

/**
 * Acción para validar sincronización entre Convex y Qdrant
 */
export const validateSync = action({
  args: {},
  handler: async (ctx) => {
    try {
      const properties = await ctx.runQuery(internal.properties.getAllPropertiesForIndexing);

      const { PropertyIndexer } = await import("../lib/qdrant/indexer");
      const indexer = new PropertyIndexer();

      const syncResult = await indexer.validateSync(properties);
      const stats = await indexer.getIndexStats();

      return {
        sync: syncResult,
        stats,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      throw error;
    }
  },
});

/**
 * Acción para limpiar vectores huérfanos
 */
export const cleanupOrphanedVectors = action({
  args: {},
  handler: async (ctx) => {
    try {
      const properties = await ctx.runQuery(internal.properties.getAllPropertiesForIndexing);
      const validIds = properties.map((p: any) => p._id);

      const { PropertyIndexer } = await import("../lib/qdrant/indexer");
      const indexer = new PropertyIndexer();

      const deletedCount = await indexer.cleanupOrphanedVectors(validIds);

      return {
        success: true,
        deletedCount,
        message: `Cleaned up ${deletedCount} orphaned vectors`,
      };
    } catch (error) {
      console.error("Error cleaning up orphaned vectors:", error);
      throw error;
    }
  },
});

/**
 * Acción para obtener estadísticas del índice
 */
export const getIndexStats = action({
  args: {},
  handler: async () => {
    try {
      const { PropertyIndexer } = await import("../lib/qdrant/indexer");
      const indexer = new PropertyIndexer();

      const stats = await indexer.getIndexStats();

      return {
        ...stats,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error("Error getting index stats:", error);
      throw error;
    }
  },
});

/**
 * Acción para probar la conexión con Qdrant
 */
export const testQdrantConnection = action({
  args: {},
  handler: async () => {
    try {
      const { testEmbeddingService } = await import("../lib/qdrant/embeddings");
      const { testQdrantConnection: testConnection } = await import("../lib/qdrant/config");

      const qdrantOk = await testConnection();
      const embeddingOk = await testEmbeddingService();

      return {
        qdrant: qdrantOk,
        embeddings: embeddingOk,
        overall: qdrantOk && embeddingOk,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error("Error testing connections:", error);
      return {
        qdrant: false,
        embeddings: false,
        overall: false,
        error: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      };
    }
  },
});

/**
 * Query para obtener configuración RAG
 */
export const getRagConfig = query({
  args: {},
  handler: async () => {
    return {
      collectionName: "properties",
      vectorSize: 1536,
      model: "text-embedding-3-small",
      maxResults: 50,
      scoreThreshold: 0.7,
    };
  },
});

/**
 * Acción para obtener todas las propiedades para diagnóstico
 */
export const getAllPropertiesForDiagnosis = action({
  args: {},
  handler: async (ctx): Promise<any[]> => {
    try {
      // Obtener todas las propiedades usando la función interna
      const properties: any[] = await ctx.runQuery(internal.properties.getAllPropertiesForIndexing, {});
      return properties;
    } catch (error) {
      console.error('Error getting properties for diagnosis:', error);
      throw new Error(`Failed to get properties: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

/**
 * Función para disparar indexación automática cuando se crea/actualiza una propiedad
 */
export const triggerPropertyIndexing = internalMutation({
  args: {
    propertyId: v.id("properties"),
    action: v.union(v.literal("create"), v.literal("update"), v.literal("delete")),
  },
  handler: async (ctx, { propertyId, action }) => {
    try {
      if (action === "delete") {
        // Programar eliminación del índice
        await ctx.scheduler.runAfter(0, internal.rag.removePropertyFromIndex, {
          propertyId: propertyId as string,
        });
      } else {
        // Programar indexación/actualización
        await ctx.scheduler.runAfter(0, internal.rag.indexProperty, {
          propertyId,
        });
      }

      console.log(`Scheduled ${action} indexing for property ${propertyId}`);
    } catch (error) {
      console.error(`Error scheduling ${action} indexing for property ${propertyId}:`, error);
      // No lanzar error para no afectar la operación principal
    }
  },
});

/**
 * Acción administrativa para eliminar todas las propiedades
 * ⚠️ PELIGROSO: Solo para administradores
 */
export const deleteAllProperties = action({
  args: {
    confirmationCode: v.string(),
  },
  handler: async (ctx, { confirmationCode }) => {
    // Validación de seguridad
    if (confirmationCode !== "DELETE_ALL_PROPERTIES_CONFIRM") {
      throw new Error("Código de confirmación incorrecto");
    }

    try {
      // Obtener todas las propiedades
      const properties = await ctx.runQuery(internal.properties.getAllPropertiesForIndexing);

      console.log(`🚨 ADMIN ACTION: Deleting ${properties.length} properties`);

      // Eliminar de Qdrant primero
      const { PropertyIndexer } = await import("../lib/qdrant/indexer");
      const indexer = new PropertyIndexer();

      // Limpiar toda la colección de Qdrant usando reindexAll con force
      await indexer.reindexAll([], true);

      // Eliminar todas las propiedades de Convex
      let deletedCount = 0;
      for (const property of properties) {
        await ctx.runMutation(internal.properties.deletePropertyInternal, {
          propertyId: property._id,
        });
        deletedCount++;
      }

      return {
        success: true,
        message: `Successfully deleted ${deletedCount} properties`,
        deletedCount,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error("Error deleting all properties:", error);
      throw error;
    }
  },
});

/**
 * Acción administrativa para cargar propiedades de demostración
 */
export const loadDemoProperties = action({
  args: {
    count: v.optional(v.number()),
  },
  handler: async (ctx, { count = 10 }) => {
    try {
      console.log(`🚀 ADMIN ACTION: Loading ${count} demo properties`);

      const demoProperties = [
        {
          title: "Casa moderna en Zona 10",
          description: "Hermosa casa de 3 niveles con acabados de lujo, ubicada en el corazón de la Zona 10. Cuenta con 4 habitaciones, 3.5 baños, sala, comedor, cocina equipada, área de servicio, garaje para 2 vehículos y jardín.",
          price: 850000,
          type: "casa",
          status: "venta",
          bedrooms: 4,
          bathrooms: 3.5,
          area: 280,
          location: {
            country: { code: "GT", name: "Guatemala" },
            level1: { name: "Guatemala", code: "GT", type: "departamento" },
            level2: { name: "Guatemala", code: "GT-GU", type: "municipio" },
            level3: { name: "Guatemala", code: "GT-GU-GU", type: "ciudad" },
            level4: { name: "Zona 10", code: "GT-GU-GU-Z10", type: "zona" }
          },
          amenities: ["piscina", "gimnasio", "seguridad_24h", "jardin"],
          keywords: ["moderna", "lujo", "zona10", "casa"]
        },
        {
          title: "Apartamento en renta Zona 14",
          description: "Moderno apartamento amueblado en torre exclusiva de Zona 14. 2 habitaciones, 2 baños, sala-comedor, cocina equipada, balcón con vista panorámica. Incluye parqueo y amenidades del edificio.",
          price: 1200,
          type: "apartment" as const,
          status: "for_rent" as const,
          bedrooms: 2,
          bathrooms: 2,
          area: 85,
          location: {
            country: { code: "GT", name: "Guatemala" },
            level1: { name: "Guatemala", code: "GT", type: "departamento" },
            level2: { name: "Guatemala", code: "GT-GU", type: "municipio" },
            level3: { name: "Guatemala", code: "GT-GU-GU", type: "ciudad" },
            level4: { name: "Zona 14", code: "GT-GU-GU-Z14", type: "zona" }
          },
          amenities: ["piscina", "gimnasio", "seguridad_24h", "parqueo"],
          keywords: ["apartamento", "amueblado", "zona14", "torre"]
        },
        {
          title: "Casa en venta Carretera a El Salvador",
          description: "Amplia casa familiar en condominio cerrado sobre Carretera a El Salvador. 3 habitaciones, 2.5 baños, sala, comedor, cocina, área de servicio, garaje doble y jardín privado.",
          price: 425000,
          type: "house" as const,
          status: "for_sale" as const,
          bedrooms: 3,
          bathrooms: 2.5,
          area: 180,
          location: {
            country: { code: "GT", name: "Guatemala" },
            level1: { name: "Guatemala", code: "GT", type: "departamento" },
            level2: { name: "Guatemala", code: "GT-GU", type: "municipio" },
            level3: { name: "Santa Catarina Pinula", code: "GT-GU-SCP", type: "municipio" },
            level4: { name: "Carretera a El Salvador", code: "GT-GU-SCP-CES", type: "zona" }
          },
          amenities: ["seguridad_24h", "jardin", "parqueo", "area_bbq"],
          keywords: ["casa", "condominio", "carretera", "salvador"]
        },
        {
          title: "Oficina en renta Zona 9",
          description: "Moderna oficina en edificio corporativo de Zona 9. Espacio abierto de 120 m², 2 oficinas privadas, sala de juntas, recepción y área de archivo. Incluye parqueo.",
          price: 2500,
          type: "office" as const,
          status: "for_rent" as const,
          bedrooms: 0,
          bathrooms: 2,
          area: 120,
          location: {
            country: { code: "GT", name: "Guatemala" },
            level1: { name: "Guatemala", code: "GT", type: "departamento" },
            level2: { name: "Guatemala", code: "GT-GU", type: "municipio" },
            level3: { name: "Guatemala", code: "GT-GU-GU", type: "ciudad" },
            level4: { name: "Zona 9", code: "GT-GU-GU-Z09", type: "zona" }
          },
          amenities: ["seguridad_24h", "parqueo", "elevador", "aire_acondicionado"],
          keywords: ["oficina", "corporativo", "zona9", "moderna"]
        },
        {
          title: "Apartamento en venta Zona 15",
          description: "Hermoso apartamento en venta en exclusivo proyecto de Zona 15. 3 habitaciones, 2 baños, sala-comedor, cocina equipada, lavandería y balcón. Excelente ubicación.",
          price: 320000,
          type: "apartment" as const,
          status: "for_sale" as const,
          bedrooms: 3,
          bathrooms: 2,
          area: 95,
          location: {
            country: { code: "GT", name: "Guatemala" },
            level1: { name: "Guatemala", code: "GT", type: "departamento" },
            level2: { name: "Guatemala", code: "GT-GU", type: "municipio" },
            level3: { name: "Guatemala", code: "GT-GU-GU", type: "ciudad" },
            level4: { name: "Zona 15", code: "GT-GU-GU-Z15", type: "zona" }
          },
          amenities: ["piscina", "gimnasio", "seguridad_24h", "parqueo"],
          keywords: ["apartamento", "zona15", "proyecto", "exclusivo"]
        }
      ];

      // Repetir las propiedades demo hasta alcanzar el count deseado
      const propertiesToCreate = [];
      for (let i = 0; i < count; i++) {
        const baseProperty = demoProperties[i % demoProperties.length];
        const property = {
          ...baseProperty,
          title: `${baseProperty.title} #${i + 1}`,
          price: baseProperty.price + (Math.random() * 50000 - 25000), // Variación de precio
        };
        propertiesToCreate.push(property);
      }

      // Crear las propiedades
      let createdCount = 0;
      for (const propertyData of propertiesToCreate) {
        await ctx.runMutation(internal.properties.createDemoProperty, {
          propertyData: propertyData as any,
        });
        createdCount++;
      }

      return {
        success: true,
        message: `Successfully created ${createdCount} demo properties`,
        createdCount,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error("Error loading demo properties:", error);
      throw error;
    }
  },
});

/**
 * Acción administrativa para limpiar todas las conversaciones
 * ⚠️ PELIGROSO: Solo para administradores
 */
export const clearAllConversations = action({
  args: {
    confirmationCode: v.string(),
  },
  handler: async (ctx, { confirmationCode }) => {
    // Validación de seguridad
    if (confirmationCode !== "CLEAR_ALL_CONVERSATIONS_CONFIRM") {
      throw new Error("Código de confirmación incorrecto");
    }

    try {
      console.log(`🚨 ADMIN ACTION: Clearing all conversations`);

      // Obtener todas las conversaciones
      const conversations = await ctx.runQuery(internal.conversations.getAllConversationsInternal);

      // Obtener todos los leads
      const leads = await ctx.runQuery(internal.conversations.getAllLeadsInternal);

      let deletedConversations = 0;
      let deletedLeads = 0;

      // Eliminar todas las conversaciones
      for (const conversation of conversations) {
        await ctx.runMutation(internal.conversations.deleteConversationInternal, {
          conversationId: conversation._id,
        });
        deletedConversations++;
      }

      // Eliminar todos los leads
      for (const lead of leads) {
        await ctx.runMutation(internal.conversations.deleteLeadInternal, {
          leadId: lead._id,
        });
        deletedLeads++;
      }

      return {
        success: true,
        message: `Successfully cleared all conversations and leads`,
        deletedConversations,
        deletedLeads,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error("Error clearing conversations:", error);
      throw error;
    }
  },
});
