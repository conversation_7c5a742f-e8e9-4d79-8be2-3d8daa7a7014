import { AccordionComponent } from "@/components/homepage/accordion-component";
import Pricing from "@/components/homepage/pricing";
import PageWrapper from "@/components/wrapper/page-wrapper";
import { STRIPE_CONFIG } from "@/lib/stripe-config";
import { Check, DollarSign } from "lucide-react";

// Productos usando configuración de Stripe
const PRODUCTS_DATA = {
  items: [
    {
      id: "premium-plan",
      name: STRIPE_CONFIG.products.premium.name,
      description: "Para equipos y agencias",
      prices: [
        {
          id: "premium-monthly",
          priceAmount: STRIPE_CONFIG.products.premium.price * 100, // Convertir a centavos
          priceCurrency: STRIPE_CONFIG.products.premium.currency.toLowerCase(),
          recurringInterval: STRIPE_CONFIG.products.premium.interval as "month",
        }
      ],
      benefits: STRIPE_CONFIG.products.premium.features.map((feature: string) => ({ description: feature }))
    }
  ],
  pagination: {
    totalCount: 1,
    maxPage: 1
  }
};

export default async function PricingPage() {
  const features = [
    "Autenticación y autorización",
    "Procesamiento de pagos",
    "Optimización SEO", 
    "Soporte TypeScript",
    "Integración de base de datos",
    "Modo oscuro",
    "Diseño responsivo",
    "Integración de APIs",
  ];

  return (
    <PageWrapper>
      <div className="container mx-auto px-4">
        <section className="relative flex flex-col items-center justify-center py-20">
          {/* Background gradient */}
          <div className="absolute inset-0 -z-10 h-full w-full bg-white dark:bg-black bg-[linear-gradient(to_right,#8080800a_1px,transparent_1px),linear-gradient(to_bottom,#8080800a_1px,transparent_1px)] bg-[size:14px_24px]">
            <div className="absolute left-0 right-0 top-0 -z-10 m-auto h-[310px] w-[310px] rounded-full bg-blue-400 dark:bg-blue-500 opacity-20 blur-[100px]"></div>
          </div>

          <div className="space-y-6 text-center">
            {/* Pill badge */}
            <div className="mx-auto w-fit rounded-full border border-blue-200 dark:border-blue-900 bg-blue-50 dark:bg-blue-900/30 px-4 py-1 mb-6">
              <div className="flex items-center gap-2 text-sm font-medium text-blue-900 dark:text-blue-200">
                <DollarSign className="h-4 w-4" />
                <span>Precios simples y transparentes</span>
              </div>
            </div>

            {/* Main heading */}
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-gray-900 via-blue-800 to-gray-900 dark:from-white dark:via-blue-300 dark:to-white animate-gradient-x pb-2">
              Elige tu Plan Perfecto
            </h1>

            {/* Subtitle */}
            <p className="text-lg md:text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Comienza con nuestra plataforma inmobiliaria y haz crecer tu negocio
              más rápido que nunca
            </p>
          </div>
        </section>

        <section className="py-12">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center mb-16">
            <div className="space-y-4">
              <h2 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-gray-900 via-blue-800 to-gray-900 dark:from-white dark:via-blue-300 dark:to-white">
                Todo lo que Necesitas
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Nuestra plataforma viene equipada con todas las características
                esenciales que necesitas para gestionar tu negocio inmobiliario.
              </p>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {features.map((feature: any, index: any) => (
                <div
                  key={feature}
                  className="flex items-center gap-2 text-gray-600 dark:text-gray-400"
                >
                  <Check className="h-5 w-5 flex-shrink-0 text-blue-500" />
                  <span>{feature}</span>
                </div>
              ))}
            </div>
          </div>

          <div className="py-8">
            <Pricing result={PRODUCTS_DATA as any} />
          </div>
        </section>

        <section className="pb-20">
          <AccordionComponent />
        </section>
      </div>
    </PageWrapper>
  );
}
