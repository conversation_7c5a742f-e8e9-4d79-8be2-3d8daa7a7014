import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Obtener configuración de un país
export const getCountryConfig = query({
  args: { countryCode: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("locationConfig")
      .withIndex("by_country", (q) => q.eq("countryCode", args.countryCode))
      .filter((q: any) => q.eq(q.field("isActive"), true))
      .first();
  },
});

// Obtener todas las configuraciones activas
export const getAllActiveConfigs = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db
      .query("locationConfig")
      .filter((q: any) => q.eq(q.field("isActive"), true))
      .collect();
  },
});

// Crear configuración completamente personalizada para un país
export const createCustomCountryConfig = mutation({
  args: {
    countryCode: v.string(),
    countryName: v.string(),
    levels: v.array(v.object({
      name: v.string(),
      required: v.boolean(),
      hasCode: v.boolean(),
    })),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Debes estar autenticado");
    }

    // Verificar permisos de admin
    const user = await ctx.db
      .query("users")
      .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.subject))
      .unique();

    if (!user || user.role !== 'admin') {
      throw new Error("Solo los administradores pueden crear configuraciones personalizadas");
    }

    // Verificar si ya existe configuración para este país
    const existing = await ctx.db
      .query("locationConfig")
      .withIndex("by_country", (q) => q.eq("countryCode", args.countryCode))
      .first();

    if (existing) {
      throw new Error(`Ya existe configuración para el país ${args.countryCode}`);
    }

    // Validar que no exceda 6 niveles
    if (args.levels.length > 6) {
      throw new Error("El sistema soporta máximo 6 niveles de jerarquía");
    }

    // Construir jerarquía dinámica
    const hierarchy: any = {};
    args.levels.forEach((level, index) => {
      const levelKey = `level${index + 1}`;
      hierarchy[levelKey] = {
        name: level.name,
        required: level.required,
        hasCode: level.hasCode,
      };
    });

    const now = new Date().toISOString();

    const newConfig = await ctx.db.insert("locationConfig", {
      countryCode: args.countryCode,
      countryName: args.countryName,
      hierarchy,
      isActive: true,
      createdAt: now,
      updatedAt: now,
    });

    return {
      message: `Configuración personalizada para ${args.countryName} creada exitosamente`,
      config: newConfig,
      type: "custom"
    };
  },
});

// Crear configuración para un país
export const createCountryConfig = mutation({
  args: {
    countryCode: v.string(),
    countryName: v.string(),
    hierarchy: v.object({
      level1: v.object({
        name: v.string(),
        required: v.boolean(),
        hasCode: v.boolean(),
      }),
      level2: v.object({
        name: v.string(),
        required: v.boolean(),
        hasCode: v.boolean(),
      }),
      level3: v.optional(v.object({
        name: v.string(),
        required: v.boolean(),
        hasCode: v.boolean(),
      })),
      level4: v.optional(v.object({
        name: v.string(),
        required: v.boolean(),
        hasCode: v.boolean(),
      })),
    }),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Debes estar autenticado");
    }

    // Verificar si ya existe configuración para este país
    const existing = await ctx.db
      .query("locationConfig")
      .withIndex("by_country", (q) => q.eq("countryCode", args.countryCode))
      .first();

    if (existing) {
      throw new Error(`Ya existe configuración para el país ${args.countryCode}`);
    }

    const now = new Date().toISOString();
    
    return await ctx.db.insert("locationConfig", {
      countryCode: args.countryCode,
      countryName: args.countryName,
      hierarchy: args.hierarchy,
      isActive: true,
      createdAt: now,
      updatedAt: now,
    });
  },
});

// Configuraciones predefinidas para diferentes países
const COUNTRY_CONFIGS = {
  GT: {
    countryCode: "GT",
    countryName: "Guatemala",
    hierarchy: {
      level1: { name: "Departamento", required: true, hasCode: true },
      level2: { name: "Municipio", required: true, hasCode: false },
      level3: { name: "Zona", required: false, hasCode: false },
      level4: { name: "Colonia", required: false, hasCode: false },
    }
  },
  MX: {
    countryCode: "MX",
    countryName: "México",
    hierarchy: {
      level1: { name: "Estado", required: true, hasCode: true },
      level2: { name: "Delegación/Municipio", required: true, hasCode: false },
      level3: { name: "Colonia", required: true, hasCode: false },
      level4: { name: "Sector", required: false, hasCode: false },
      level5: { name: "Manzana", required: false, hasCode: false },
    }
  },
  US: {
    countryCode: "US",
    countryName: "United States",
    hierarchy: {
      level1: { name: "State", required: true, hasCode: true },
      level2: { name: "City", required: true, hasCode: false },
      level3: { name: "Neighborhood", required: false, hasCode: false },
    }
  },
  ES: {
    countryCode: "ES",
    countryName: "España",
    hierarchy: {
      level1: { name: "Comunidad Autónoma", required: true, hasCode: true },
      level2: { name: "Provincia", required: true, hasCode: false },
      level3: { name: "Municipio", required: true, hasCode: false },
      level4: { name: "Distrito", required: false, hasCode: false },
      level5: { name: "Barrio", required: false, hasCode: false },
      level6: { name: "Calle", required: false, hasCode: false },
    }
  }
};

// Configuración genérica por defecto para países no predefinidos
const DEFAULT_COUNTRY_CONFIG = {
  hierarchy: {
    level1: { name: "Región/Estado", required: true, hasCode: false },
    level2: { name: "Ciudad/Municipio", required: true, hasCode: false },
    level3: { name: "Distrito/Zona", required: false, hasCode: false },
    level4: { name: "Barrio/Colonia", required: false, hasCode: false },
  }
};

// Lista de países disponibles (constante)
const AVAILABLE_COUNTRIES = [
  { code: "GT", name: "Guatemala", predefined: true },
  { code: "MX", name: "México", predefined: true },
  { code: "US", name: "Estados Unidos", predefined: true },
  { code: "ES", name: "España", predefined: true },
  { code: "CR", name: "Costa Rica", predefined: false },
  { code: "HN", name: "Honduras", predefined: false },
  { code: "SV", name: "El Salvador", predefined: false },
  { code: "NI", name: "Nicaragua", predefined: false },
  { code: "PA", name: "Panamá", predefined: false },
  { code: "CO", name: "Colombia", predefined: false },
  { code: "PE", name: "Perú", predefined: false },
  { code: "EC", name: "Ecuador", predefined: false },
  { code: "AR", name: "Argentina", predefined: false },
  { code: "CL", name: "Chile", predefined: false },
  { code: "BR", name: "Brasil", predefined: false },
  // Agregar más países según necesidad
];

// Obtener lista de países disponibles (ISO 3166-1)
export const getAvailableCountries = query({
  args: {},
  handler: async () => {
    return AVAILABLE_COUNTRIES;
  },
});

// Inicializar configuración para cualquier país (predefinido o genérico)
export const initializeCountryConfig = mutation({
  args: {
    countryCode: v.string(),
    countryName: v.optional(v.string()),
    useGeneric: v.optional(v.boolean())
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Debes estar autenticado");
    }

    // Verificar si ya existe
    const existing = await ctx.db
      .query("locationConfig")
      .withIndex("by_country", (q) => q.eq("countryCode", args.countryCode))
      .first();

    if (existing) {
      return { message: `Configuración para ${existing.countryName} ya existe`, config: existing };
    }

    let config;

    // Intentar usar configuración predefinida
    const predefinedConfig = COUNTRY_CONFIGS[args.countryCode as keyof typeof COUNTRY_CONFIGS];

    if (predefinedConfig && !args.useGeneric) {
      config = predefinedConfig;
    } else {
      // Usar configuración genérica
      const countryInfo = AVAILABLE_COUNTRIES.find((c: any) => c.code === args.countryCode);

      config = {
        countryCode: args.countryCode,
        countryName: args.countryName || countryInfo?.name || args.countryCode,
        ...DEFAULT_COUNTRY_CONFIG
      };
    }

    const now = new Date().toISOString();

    const newConfig = await ctx.db.insert("locationConfig", {
      ...config,
      isActive: true,
      createdAt: now,
      updatedAt: now,
    });

    return {
      message: `Configuración para ${config.countryName} creada exitosamente`,
      config: newConfig,
      type: predefinedConfig ? "predefined" : "generic"
    };
  },
});

// Inicializar configuración por defecto para Guatemala
export const initializeGuatemalaConfig = mutation({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Debes estar autenticado");
    }

    // Verificar si ya existe
    const existing = await ctx.db
      .query("locationConfig")
      .withIndex("by_country", (q) => q.eq("countryCode", "GT"))
      .first();

    if (existing) {
      return { message: "Configuración para Guatemala ya existe", config: existing };
    }

    const now = new Date().toISOString();
    
    const guatemalaConfig = await ctx.db.insert("locationConfig", {
      countryCode: "GT",
      countryName: "Guatemala",
      hierarchy: {
        level1: {
          name: "Departamento",
          required: true,
          hasCode: true,
        },
        level2: {
          name: "Municipio",
          required: true,
          hasCode: false,
        },
        level3: {
          name: "Zona",
          required: false,
          hasCode: false,
        },
        level4: {
          name: "Colonia",
          required: false,
          hasCode: false,
        },
      },
      isActive: true,
      createdAt: now,
      updatedAt: now,
    });

    return { 
      message: "Configuración para Guatemala creada exitosamente", 
      config: guatemalaConfig 
    };
  },
});

// Función para generar keywords de búsqueda basados en la ubicación
export const generateLocationKeywords = (location: any): string[] => {
  const keywords: string[] = [];
  
  if (!location) return keywords;
  
  // Agregar todos los niveles de la jerarquía
  if (location.country) {
    keywords.push(location.country.name.toLowerCase());
    if (location.country.code) {
      keywords.push(location.country.code.toLowerCase());
    }
  }
  
  if (location.level1) {
    keywords.push(location.level1.name.toLowerCase());
    if (location.level1.code) {
      keywords.push(location.level1.code.toLowerCase());
    }
  }
  
  if (location.level2) {
    keywords.push(location.level2.name.toLowerCase());
    if (location.level2.code) {
      keywords.push(location.level2.code.toLowerCase());
    }
  }
  
  if (location.level3) {
    keywords.push(location.level3.name.toLowerCase());
    if (location.level3.code) {
      keywords.push(location.level3.code.toLowerCase());
    }
  }
  
  if (location.level4) {
    keywords.push(location.level4.name.toLowerCase());
    if (location.level4.code) {
      keywords.push(location.level4.code.toLowerCase());
    }
  }
  
  // Agregar combinaciones comunes
  if (location.level3 && location.level2) {
    keywords.push(`${location.level3.name} ${location.level2.name}`.toLowerCase());
  }
  
  if (location.level4 && location.level3) {
    keywords.push(`${location.level4.name} ${location.level3.name}`.toLowerCase());
  }
  
  // Remover duplicados
  return [...new Set(keywords)];
};

// Búsqueda optimizada por ubicación
export const searchPropertiesByLocation = query({
  args: {
    locationQuery: v.string(),
    countryCode: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const query = args.locationQuery.toLowerCase();
    
    let properties = await ctx.db.query("properties")
      .filter((q: any) => q.neq(q.field("status"), "draft"))
      .collect();
    
    // Filtrar por país si se especifica
    if (args.countryCode) {
      properties = properties.filter(p =>
        p.location?.country?.code === args.countryCode
      );
    }
    
    // Búsqueda semántica por ubicación
    const filtered = properties.filter(property => {
      // Buscar en nueva estructura
      if (property.location) {
        const locationKeywords = generateLocationKeywords(property.location);
        if (locationKeywords.some(keyword => 
          keyword.includes(query) || query.includes(keyword)
        )) {
          return true;
        }
      }
      
      // Buscar en metadatos
      if (property.locationMetadata?.searchKeywords) {
        const metadataKeywords = property.locationMetadata.searchKeywords.map(k => k.toLowerCase());
        if (metadataKeywords.some(keyword => 
          keyword.includes(query) || query.includes(keyword)
        )) {
          return true;
        }
      }
      
      // Buscar en dirección
      if (property.address.toLowerCase().includes(query)) {
        return true;
      }

      return false;
    });
    
    return filtered.slice(0, args.limit || 20);
  },
});

// Migrar datos existentes a nueva estructura
export const migrateExistingProperties = mutation({
  args: {
    dryRun: v.optional(v.boolean()), // Para probar sin hacer cambios
    limit: v.optional(v.number()) // Limitar número de propiedades a migrar
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Debes estar autenticado");
    }

    // Obtener propiedades que no tienen la nueva estructura
    const properties = await ctx.db.query("properties")
      .filter((q: any) => q.eq(q.field("location"), undefined))
      .collect();

    const limit = args.limit || properties.length;
    const propertiesToMigrate = properties.slice(0, limit);

    let migratedCount = 0;
    let errors: string[] = [];

    for (const property of propertiesToMigrate) {
      try {
        // Esta función ya no es necesaria porque todas las propiedades
        // ya tienen la nueva estructura de ubicación
        migratedCount++;
      } catch (error) {
        errors.push(`Error migrando propiedad ${property._id}: ${error}`);
      }
    }

    return {
      totalProperties: properties.length,
      migratedCount,
      errors,
      dryRun: args.dryRun || false,
      message: args.dryRun
        ? `Simulación: ${migratedCount} propiedades serían migradas`
        : `${migratedCount} propiedades migradas exitosamente`
    };
  },
});

// Función para limpiar datos después de migración exitosa
export const cleanupOldLocationFields = mutation({
  args: {
    confirmCleanup: v.boolean(), // Requiere confirmación explícita
    removeFromSchema: v.optional(v.boolean()) // Para eliminar también del schema
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Debes estar autenticado");
    }

    if (!args.confirmCleanup) {
      throw new Error("Debes confirmar la limpieza de campos antiguos");
    }

    // Verificar que todas las propiedades tienen la nueva estructura
    const propertiesWithoutNewStructure = await ctx.db.query("properties")
      .filter((q: any) => q.eq(q.field("location"), undefined))
      .collect();

    if (propertiesWithoutNewStructure.length > 0) {
      throw new Error(`${propertiesWithoutNewStructure.length} propiedades aún no tienen la nueva estructura. Ejecuta la migración primero.`);
    }

    // Obtener propiedades que tienen tanto estructura nueva como antigua
    const properties = await ctx.db.query("properties")
      .filter((q: any) => q.neq(q.field("location"), undefined))
      .collect();

    let cleanedCount = 0;

    // Los campos legacy ya fueron eliminados del schema
    // Esta función ya no es necesaria
    console.log("Limpieza ya completada - campos legacy eliminados del schema");

    return {
      cleanedCount,
      message: `${cleanedCount} propiedades limpiadas de campos antiguos`,
      nextStep: args.removeFromSchema
        ? "Ahora puedes eliminar los campos del schema.ts manualmente"
        : "Ejecuta con removeFromSchema: true para el paso final"
    };
  },
});

// Verificar estado de migración
export const getMigrationStatus = query({
  args: {},
  handler: async (ctx) => {
    const totalProperties = await ctx.db.query("properties").collect();
    const propertiesWithNewStructure = totalProperties.filter(p => p.location);
    const propertiesWithOldFields: any[] = []; // Los campos legacy ya fueron eliminados

    return {
      total: totalProperties.length,
      withNewStructure: propertiesWithNewStructure.length,
      withOldFields: propertiesWithOldFields.length,
      migrationComplete: propertiesWithNewStructure.length === totalProperties.length,
      cleanupReady: propertiesWithNewStructure.length === totalProperties.length && propertiesWithOldFields.length > 0,
      status: propertiesWithNewStructure.length === totalProperties.length
        ? (propertiesWithOldFields.length > 0 ? "ready_for_cleanup" : "fully_migrated")
        : "migration_pending"
    };
  },
});


