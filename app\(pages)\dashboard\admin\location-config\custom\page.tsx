"use client";

import React, { useState } from 'react';
import { useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { useUser } from "@clerk/nextjs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "sonner";
import { 
  Plus, 
  Trash2, 
  Save, 
  ArrowLeft,
  Globe,
  Loader2,
  Info
} from "lucide-react";
import { useRouter } from "next/navigation";

interface Level {
  name: string;
  required: boolean;
  hasCode: boolean;
}

export default function CustomLocationConfigPage() {
  const { user } = useUser();
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  // Estado del formulario
  const [countryCode, setCountryCode] = useState("");
  const [countryName, setCountryName] = useState("");
  const [levels, setLevels] = useState<Level[]>([
    { name: "", required: true, hasCode: false },
    { name: "", required: true, hasCode: false },
  ]);

  // Mutation
  const createCustomConfig = useMutation(api.locationConfig.createCustomCountryConfig);

  const addLevel = () => {
    if (levels.length < 6) {
      setLevels([...levels, { name: "", required: false, hasCode: false }]);
    }
  };

  const removeLevel = (index: number) => {
    if (levels.length > 2) { // Mínimo 2 niveles
      setLevels(levels.filter((_, i) => i !== index));
    }
  };

  const updateLevel = (index: number, field: keyof Level, value: string | boolean) => {
    const newLevels = [...levels];
    newLevels[index] = { ...newLevels[index], [field]: value };
    setLevels(newLevels);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validaciones
    if (!countryCode.trim() || !countryName.trim()) {
      toast.error("Código y nombre del país son requeridos");
      return;
    }

    if (countryCode.length !== 2) {
      toast.error("El código del país debe tener exactamente 2 caracteres (ISO 3166-1)");
      return;
    }

    const validLevels = levels.filter(level => level.name.trim() !== "");
    if (validLevels.length < 2) {
      toast.error("Se requieren al menos 2 niveles de jerarquía");
      return;
    }

    setLoading(true);
    try {
      const result = await createCustomConfig({
        countryCode: countryCode.toUpperCase(),
        countryName: countryName.trim(),
        levels: validLevels,
      });

      toast.success(result.message);
      router.push("/dashboard/admin/location-config");
    } catch (error: any) {
      console.error("Error:", error);
      toast.error(error.message || "Error al crear configuración personalizada");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex flex-col gap-6 p-4 md:p-6 max-w-4xl mx-auto">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Volver
        </Button>
        <div>
          <h1 className="text-2xl md:text-3xl font-semibold tracking-tight flex items-center gap-2">
            <Globe className="h-6 w-6 md:h-8 md:w-8" />
            Configuración Personalizada de País
          </h1>
          <p className="text-muted-foreground mt-2 text-sm md:text-base">
            Crea una configuración geográfica específica para un país
          </p>
        </div>
      </div>

      {/* Información */}
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <Info className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-blue-800">
              <p className="font-medium mb-2">Instrucciones:</p>
              <ul className="space-y-1">
                <li>• Usa códigos ISO 3166-1 de 2 letras (GT, MX, BR, etc.)</li>
                <li>• Mínimo 2 niveles, máximo 6 niveles de jerarquía</li>
                <li>• Los primeros 2 niveles son siempre requeridos</li>
                <li>• &quot;Tiene código&quot; indica si el nivel usa códigos oficiales</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Formulario */}
      <form onSubmit={handleSubmit}>
        <Card>
          <CardHeader>
            <CardTitle>Información del País</CardTitle>
            <CardDescription>
              Datos básicos del país a configurar
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="countryCode">Código del País *</Label>
                <Input
                  id="countryCode"
                  placeholder="Ej: AR"
                  value={countryCode}
                  onChange={(e) => setCountryCode(e.target.value.toUpperCase())}
                  maxLength={2}
                  className="mt-1"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Código ISO 3166-1 de 2 letras
                </p>
              </div>
              
              <div>
                <Label htmlFor="countryName">Nombre del País *</Label>
                <Input
                  id="countryName"
                  placeholder="Ej: Argentina"
                  value={countryName}
                  onChange={(e) => setCountryName(e.target.value)}
                  className="mt-1"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Jerarquía Geográfica
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addLevel}
                disabled={levels.length >= 6}
              >
                <Plus className="h-4 w-4 mr-2" />
                Agregar Nivel
              </Button>
            </CardTitle>
            <CardDescription>
              Define los niveles de división geográfica (mínimo 2, máximo 6)
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {levels.map((level: any, index: any) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium">Nivel {index + 1}</h4>
                  {levels.length > 2 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeLevel(index)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="md:col-span-2">
                    <Label htmlFor={`level-${index}-name`}>
                      Nombre del Nivel *
                    </Label>
                    <Input
                      id={`level-${index}-name`}
                      placeholder={
                        index === 0 ? "Ej: Provincia" :
                        index === 1 ? "Ej: Partido" :
                        index === 2 ? "Ej: Localidad" :
                        "Ej: Barrio"
                      }
                      value={level.name}
                      onChange={(e) => updateLevel(index, "name", e.target.value)}
                      className="mt-1"
                    />
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id={`level-${index}-required`}
                        checked={level.required}
                        onCheckedChange={(checked) => 
                          updateLevel(index, "required", checked as boolean)
                        }
                        disabled={index < 2} // Primeros 2 niveles siempre requeridos
                      />
                      <Label htmlFor={`level-${index}-required`} className="text-sm">
                        Requerido
                      </Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id={`level-${index}-hasCode`}
                        checked={level.hasCode}
                        onCheckedChange={(checked) => 
                          updateLevel(index, "hasCode", checked as boolean)
                        }
                      />
                      <Label htmlFor={`level-${index}-hasCode`} className="text-sm">
                        Tiene código
                      </Label>
                    </div>
                  </div>
                </div>
              </div>
            ))}

            <div className="text-sm text-gray-500 bg-gray-50 p-3 rounded-lg">
              <p className="font-medium mb-1">Vista previa de la jerarquía:</p>
              <div className="space-y-1">
                {levels.map((level: any, index: any) => (
                  level.name && (
                    <div key={index} className="flex items-center gap-2">
                      <span className="text-gray-400">•</span>
                      <span>{level.name}</span>
                      {level.required && <span className="text-red-500 text-xs">(requerido)</span>}
                      {level.hasCode && <span className="text-blue-500 text-xs">(con código)</span>}
                    </div>
                  )
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end gap-3 mt-6">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
          >
            Cancelar
          </Button>
          <Button
            type="submit"
            disabled={loading}
          >
            {loading ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Crear Configuración
          </Button>
        </div>
      </form>
    </div>
  );
}
