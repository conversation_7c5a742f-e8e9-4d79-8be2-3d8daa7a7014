import { v } from "convex/values";
import { mutation, action } from "./_generated/server";
import { api } from "./_generated/api";

// 🔄 INICIALIZAR SISTEMA - Elimina todos los datos excepto configuraciones críticas
export const initializeSystem: any = action({
  args: {
    confirmAction: v.string(), // Debe ser exactamente "CONFIRMAR_INICIALIZACION"
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Debes estar autenticado");
    }

    // Verificar que el usuario es admin
    const currentUser = await ctx.runMutation(api.systemAdmin.getCurrentUserForAdmin);

    if (!currentUser || currentUser.role !== 'admin') {
      throw new Error("Solo los administradores pueden inicializar el sistema");
    }

    // Verificación de seguridad
    if (args.confirmAction !== "CONFIRMAR_INICIALIZACION") {
      throw new Error("Confirmación requerida: debe escribir exactamente 'CONFIRMAR_INICIALIZACION'");
    }

    console.log("🔄 Iniciando inicialización del sistema...");

    // Tablas a PRESERVAR (no eliminar)
    const preservedTables = [
      "adminSettings",
      "creditActions",
      "knowledgeBase",
      "locationConfig",
      "locationData",
      "subscriptions", // Solo preservar admin
      "users" // Solo preservar admin
    ];

    // Tablas a ELIMINAR (todas las demás)
    const tablesToClear = [
      "properties",
      "favorites", 
      "orders",
      "messages",
      "transactions",
      "conversations",
      "leads",
      "appointments",
      "appointmentRequests"
    ];

    let deletedCounts: Record<string, number> = {};

    try {
      // 1. Ejecutar la eliminación de datos usando una mutation
      const result = await ctx.runMutation(api.systemAdmin.clearAllDataExceptPreserved);

      // 2. Eliminar usuarios de Clerk
      console.log("🗑️ Eliminando usuarios de Clerk...");
      let clerkDeletionErrors: string[] = [];

      for (const user of result.nonAdminUsers) {
        try {
          await ctx.runAction(api.systemAdmin.deleteUserFromClerk, {
            userId: user.tokenIdentifier
          });
        } catch (error) {
          console.error(`Error eliminando usuario ${user.email} de Clerk:`, error);
          clerkDeletionErrors.push(`${user.email}: ${error}`);
        }
      }

      console.log("✅ Inicialización del sistema completada");

      return {
        success: true,
        message: "Sistema inicializado exitosamente",
        deletedCounts: result.deletedCounts,
        preservedTables: result.preservedTables,
        totalDeleted: result.totalDeleted,
        clerkDeletionErrors: clerkDeletionErrors.length > 0 ? clerkDeletionErrors : undefined,
        timestamp: new Date().toISOString()
      };



    } catch (error) {
      console.error("❌ Error durante la inicialización:", error);
      throw new Error(`Error durante la inicialización: ${error}`);
    }
  },
});

// 🗑️ LIMPIAR TODOS LOS DATOS EXCEPTO PRESERVADOS - Mutation interna
export const clearAllDataExceptPreserved = mutation({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Debes estar autenticado");
    }

    // Verificar que el usuario es admin
    const currentUser = await ctx.db
      .query("users")
      .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.subject))
      .first();

    if (!currentUser || currentUser.role !== 'admin') {
      throw new Error("Solo los administradores pueden limpiar datos");
    }

    let deletedCounts: Record<string, number> = {};
    let clerkDeletionErrors: string[] = [];

    // 1. Eliminar todas las propiedades
    console.log("🏠 Eliminando propiedades...");
    const properties = await ctx.db.query("properties").collect();
    for (const property of properties) {
      await ctx.db.delete(property._id);
    }
    deletedCounts.properties = properties.length;

    // 2. Eliminar favoritos
    console.log("❤️ Eliminando favoritos...");
    const favorites = await ctx.db.query("favorites").collect();
    for (const favorite of favorites) {
      await ctx.db.delete(favorite._id);
    }
    deletedCounts.favorites = favorites.length;

    // 3. Eliminar órdenes
    console.log("💳 Eliminando órdenes...");
    const orders = await ctx.db.query("orders").collect();
    for (const order of orders) {
      await ctx.db.delete(order._id);
    }
    deletedCounts.orders = orders.length;

    // 4. Eliminar mensajes
    console.log("💬 Eliminando mensajes...");
    const messages = await ctx.db.query("messages").collect();
    for (const message of messages) {
      await ctx.db.delete(message._id);
    }
    deletedCounts.messages = messages.length;

    // 5. Eliminar transacciones
    console.log("📊 Eliminando transacciones...");
    const transactions = await ctx.db.query("transactions").collect();
    for (const transaction of transactions) {
      await ctx.db.delete(transaction._id);
    }
    deletedCounts.transactions = transactions.length;

    // 6. Eliminar conversaciones
    console.log("🗨️ Eliminando conversaciones...");
    const conversations = await ctx.db.query("conversations").collect();
    for (const conversation of conversations) {
      await ctx.db.delete(conversation._id);
    }
    deletedCounts.conversations = conversations.length;

    // 7. Eliminar leads
    console.log("🎯 Eliminando leads...");
    const leads = await ctx.db.query("leads").collect();
    for (const lead of leads) {
      await ctx.db.delete(lead._id);
    }
    deletedCounts.leads = leads.length;

    // 8. Eliminar citas
    console.log("📅 Eliminando citas...");
    const appointments = await ctx.db.query("appointments").collect();
    for (const appointment of appointments) {
      await ctx.db.delete(appointment._id);
    }
    deletedCounts.appointments = appointments.length;

    // 9. Eliminar solicitudes de citas
    console.log("📋 Eliminando solicitudes de citas...");
    const appointmentRequests = await ctx.db.query("appointmentRequests").collect();
    for (const appointmentRequest of appointmentRequests) {
      await ctx.db.delete(appointmentRequest._id);
    }
    deletedCounts.appointmentRequests = appointmentRequests.length;

    // 10. Limpiar suscripciones (mantener solo admin)
    console.log("💼 Limpiando suscripciones (manteniendo admin)...");
    const allSubscriptions = await ctx.db.query("subscriptions").collect();
    let deletedSubscriptions = 0;
    for (const subscription of allSubscriptions) {
      // Obtener usuario de la suscripción
      const user = await ctx.db
        .query("users")
        .withIndex("by_token", (q) => q.eq("tokenIdentifier", subscription.userId))
        .first();

      // Solo eliminar si no es admin
      if (!user || user.role !== 'admin') {
        await ctx.db.delete(subscription._id);
        deletedSubscriptions++;
      }
    }
    deletedCounts.subscriptions = deletedSubscriptions;

    // 11. Obtener usuarios no-admin para eliminar de Clerk
    console.log("👥 Obteniendo usuarios no-admin...");
    const allUsers = await ctx.db.query("users").collect();
    const nonAdminUsers = allUsers.filter(user => user.role !== 'admin');

    // Eliminar usuarios de Convex (Clerk se eliminará después)
    let deletedUsers = 0;
    for (const user of nonAdminUsers) {
      await ctx.db.delete(user._id);
      deletedUsers++;
    }
    deletedCounts.users = deletedUsers;

    console.log("✅ Limpieza de base de datos completada");

    return {
      deletedCounts,
      nonAdminUsers: nonAdminUsers.map(u => ({
        tokenIdentifier: u.tokenIdentifier,
        email: u.email
      })),
      preservedTables: [
        "adminSettings",
        "creditActions",
        "knowledgeBase",
        "locationConfig",
        "locationData",
        "subscriptions (solo admin)",
        "users (solo admin)"
      ],
      totalDeleted: Object.values(deletedCounts).reduce((sum, count) => sum + count, 0)
    };
  },
});

// 🧹 LIMPIAR QDRANT - Elimina todos los vectores de Qdrant
export const clearQdrantVectors = mutation({
  args: {
    confirmAction: v.string(), // Debe ser exactamente "CONFIRMAR_LIMPIAR_QDRANT"
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Debes estar autenticado");
    }

    // Verificar que el usuario es admin
    const currentUser = await ctx.db
      .query("users")
      .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.subject))
      .first();

    if (!currentUser || currentUser.role !== 'admin') {
      throw new Error("Solo los administradores pueden limpiar Qdrant");
    }

    // Verificación de seguridad
    if (args.confirmAction !== "CONFIRMAR_LIMPIAR_QDRANT") {
      throw new Error("Confirmación requerida: debe escribir exactamente 'CONFIRMAR_LIMPIAR_QDRANT'");
    }

    try {
      // Aquí se integraría con la API de Qdrant para eliminar todos los vectores
      // Por ahora, solo retornamos un mensaje de éxito
      console.log("🧹 Limpiando vectores de Qdrant...");
      
      // TODO: Implementar llamada a Qdrant API para eliminar colección
      // await fetch(`${process.env.QDRANT_URL}/collections/properties`, {
      //   method: 'DELETE',
      //   headers: { 'api-key': process.env.QDRANT_API_KEY }
      // });

      return {
        success: true,
        message: "Vectores de Qdrant eliminados exitosamente",
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error("❌ Error limpiando Qdrant:", error);
      throw new Error(`Error limpiando Qdrant: ${error}`);
    }
  },
});

// 📊 OBTENER ESTADÍSTICAS DEL SISTEMA
export const getSystemStats = mutation({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Debes estar autenticado");
    }

    // Verificar que el usuario es admin
    const currentUser = await ctx.db
      .query("users")
      .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.subject))
      .first();

    if (!currentUser || currentUser.role !== 'admin') {
      throw new Error("Solo los administradores pueden ver estadísticas del sistema");
    }

    try {
      // Contar registros en cada tabla
      const stats = {
        users: (await ctx.db.query("users").collect()).length,
        properties: (await ctx.db.query("properties").collect()).length,
        favorites: (await ctx.db.query("favorites").collect()).length,
        subscriptions: (await ctx.db.query("subscriptions").collect()).length,
        orders: (await ctx.db.query("orders").collect()).length,
        messages: (await ctx.db.query("messages").collect()).length,
        transactions: (await ctx.db.query("transactions").collect()).length,
        conversations: (await ctx.db.query("conversations").collect()).length,
        leads: (await ctx.db.query("leads").collect()).length,
        appointments: (await ctx.db.query("appointments").collect()).length,
        appointmentRequests: (await ctx.db.query("appointmentRequests").collect()).length,
        locationConfig: (await ctx.db.query("locationConfig").collect()).length,
        locationData: (await ctx.db.query("locationData").collect()).length,
        creditActions: (await ctx.db.query("creditActions").collect()).length,
        knowledgeBase: (await ctx.db.query("knowledgeBase").collect()).length,
      };

      return {
        success: true,
        stats,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error("❌ Error obteniendo estadísticas:", error);
      throw new Error(`Error obteniendo estadísticas: ${error}`);
    }
  },
});

// 🗑️ ELIMINAR USUARIO DE CLERK - Action para llamar API externa
export const deleteUserFromClerk = action({
  args: {
    userId: v.string(), // tokenIdentifier del usuario
  },
  handler: async (ctx, args) => {
    const clerkSecretKey = process.env.CLERK_SECRET_KEY;

    if (!clerkSecretKey) {
      throw new Error("CLERK_SECRET_KEY no está configurada");
    }

    try {
      // Extraer el ID real de Clerk del tokenIdentifier
      // El tokenIdentifier tiene formato: "https://domain.clerk.accounts.dev#user_xxxxx"
      const clerkUserId = args.userId.split('#')[1];

      if (!clerkUserId || !clerkUserId.startsWith('user_')) {
        throw new Error(`Token identifier inválido: ${args.userId}`);
      }

      console.log(`🗑️ Eliminando usuario de Clerk: ${clerkUserId}`);

      const response = await fetch(`https://api.clerk.com/v1/users/${clerkUserId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${clerkSecretKey}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Error de Clerk API (${response.status}): ${errorText}`);
      }

      console.log(`✅ Usuario eliminado de Clerk: ${clerkUserId}`);

      return {
        success: true,
        clerkUserId,
        message: `Usuario ${clerkUserId} eliminado de Clerk exitosamente`
      };

    } catch (error) {
      console.error(`❌ Error eliminando usuario de Clerk:`, error);
      throw error;
    }
  },
});

// 👤 OBTENER USUARIO ACTUAL PARA ADMIN - Helper function
export const getCurrentUserForAdmin = mutation({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.subject))
      .first();

    return user;
  },
});
