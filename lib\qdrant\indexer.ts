import { getQdrantClient, QDRANT_CONFIG, PropertyVector, PropertyPayload } from './config';
import { generateEmbedding, createSimplePropertyEmbeddingText, createWeightedPropertyEmbeddingText, validateEmbeddingText } from './embeddings';

/**
 * Clase para manejar la indexación de propiedades en Qdrant
 */
export class PropertyIndexer {
  private client = getQdrantClient();
  private collectionName = QDRANT_CONFIG.collectionName;

  /**
   * Convierte un ID de Convex a un UUID válido para Qdrant
   */
  private convexIdToUuid(convexId: string): string {
    // Crear múltiples hashes para generar suficientes caracteres hexadecimales
    let hash1 = 0;
    let hash2 = 0;

    for (let i = 0; i < convexId.length; i++) {
      const char = convexId.charCodeAt(i);
      hash1 = ((hash1 << 5) - hash1) + char;
      hash1 = hash1 & hash1; // Convertir a 32bit integer

      hash2 = ((hash2 << 3) - hash2) + char * (i + 1);
      hash2 = hash2 & hash2; // Convertir a 32bit integer
    }

    // Convertir a hexadecimal
    const hex1 = Math.abs(hash1).toString(16).padStart(8, '0');
    const hex2 = Math.abs(hash2).toString(16).padStart(8, '0');

    // Crear UUID válido en formato 8-4-4-4-12
    const uuid = `${hex1.slice(0, 8)}-${hex1.slice(0, 4)}-4${hex1.slice(1, 4)}-8${hex1.slice(4, 7)}-${hex2.slice(0, 8)}${hex1.slice(4, 8)}`;

    return uuid;
  }

  /**
   * Convierte un UUID de Qdrant de vuelta a ID de Convex
   */
  private uuidToConvexId(uuid: string, convexProperties: any[]): string | null {
    // Buscar en las propiedades de Convex cuál corresponde a este UUID
    for (const property of convexProperties) {
      if (this.convexIdToUuid(property._id) === uuid) {
        return property._id;
      }
    }
    return null;
  }

  /**
   * Indexa una propiedad individual
   */
  async indexProperty(property: any): Promise<void> {
    try {
      console.log(`Indexing property: ${property._id}`);
      
      // Crear texto para embedding (versión jerárquica con pesos semánticos)
      const embeddingText = createWeightedPropertyEmbeddingText(property);
      
      // Validar calidad del texto
      const validation = validateEmbeddingText(embeddingText);
      if (!validation.isValid) {
        console.warn(`Property ${property._id} has embedding quality issues:`, validation.issues);
        // Continuar con la indexación pero registrar advertencias
      }

      // Generar embedding
      const vector = await generateEmbedding(embeddingText);

      // Preparar payload con TODOS los campos necesarios
      const payload: PropertyPayload = {
        propertyId: property._id,
        title: property.title || '',
        description: property.description || '',
        type: property.type || 'unknown',
        status: property.status || 'unknown',
        price: property.price || 0,
        currency: property.currency || 'GTQ',
        bedrooms: property.bedrooms || 0,
        bathrooms: property.bathrooms || 0,
        area: property.area || 0,
        address: property.address || '',

        // Ubicación jerárquica completa (4 niveles)
        country: property.location?.country?.name || '',
        level1: property.location?.level1?.name || '',
        level2: property.location?.level2?.name || '',
        level3: property.location?.level3?.name || '',
        level4: property.location?.level4?.name || '',

        // Ubicación legacy para compatibilidad
        neighborhood: property.locationMetadata?.neighborhood || '',

        coordinates: property.coordinates ? {
          lat: property.coordinates.lat,
          lon: property.coordinates.lng,
        } : undefined,

        amenities: property.amenities || [],
        searchKeywords: property.locationMetadata?.searchKeywords || [],

        // Metadatos adicionales
        ownerId: property.ownerId || '',
        agentId: property.agentId || '',

        createdAt: property._creationTime ? new Date(property._creationTime).toISOString() : new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        isActive: property.isActive !== false,
        isFeatured: property.featured || false,
        isPremium: property.premiumHomeUntil ? (property.premiumHomeUntil > Date.now()) : false,
        embeddingText,
      };

      // Crear vector para Qdrant
      // Usar ID de Convex directamente como UUID
      const uuid = this.convexIdToUuid(property._id);

      const propertyVector: PropertyVector = {
        id: uuid,
        vector,
        payload,
      };

      // Upsert en Qdrant (crear o actualizar)
      await this.client.upsert(this.collectionName, {
        wait: true,
        points: [propertyVector as any],
      });

      console.log(`Property ${property._id} indexed successfully (score: ${validation.score}/100)`);
    } catch (error) {
      console.error(`Error indexing property ${property._id}:`, error);
      throw error;
    }
  }

  /**
   * Indexa múltiples propiedades en lotes
   */
  async indexProperties(properties: any[], batchSize: number = 10): Promise<void> {
    console.log(`Starting batch indexing of ${properties.length} properties`);
    
    for (let i = 0; i < properties.length; i += batchSize) {
      const batch = properties.slice(i, i + batchSize);
      console.log(`Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(properties.length / batchSize)}`);
      
      try {
        await Promise.all(batch.map(property => this.indexProperty(property)));
        console.log(`Batch completed: ${batch.length} properties indexed`);
      } catch (error) {
        console.error(`Error in batch ${Math.floor(i / batchSize) + 1}:`, error);
        // Continuar con el siguiente lote
      }
      
      // Pequeña pausa entre lotes para no sobrecargar la API
      if (i + batchSize < properties.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    console.log(`Batch indexing completed for ${properties.length} properties`);
  }

  /**
   * Elimina una propiedad del índice
   */
  async removeProperty(propertyId: string): Promise<void> {
    try {
      console.log(`Removing property from index: ${propertyId}`);

      const uuid = this.convexIdToUuid(propertyId);

      await this.client.delete(this.collectionName, {
        wait: true,
        points: [uuid],
      });

      console.log(`Property ${propertyId} removed from index successfully`);
    } catch (error) {
      console.error(`Error removing property ${propertyId} from index:`, error);
      throw error;
    }
  }

  /**
   * Verifica si una propiedad está indexada
   */
  async isPropertyIndexed(propertyId: string): Promise<boolean> {
    try {
      const uuid = this.convexIdToUuid(propertyId);

      const result = await this.client.retrieve(this.collectionName, {
        ids: [uuid],
      });

      return result.length > 0;
    } catch (error) {
      console.error(`Error checking if property ${propertyId} is indexed:`, error);
      return false;
    }
  }

  /**
   * Obtiene estadísticas del índice
   */
  async getIndexStats(): Promise<{
    totalVectors: number;
    indexedProperties: number;
    collectionInfo: any;
  }> {
    try {
      const collectionInfo = await this.client.getCollection(this.collectionName);
      
      return {
        totalVectors: collectionInfo.vectors_count || 0,
        indexedProperties: collectionInfo.points_count || 0,
        collectionInfo,
      };
    } catch (error) {
      console.error('Error getting index stats:', error);
      throw error;
    }
  }

  /**
   * Re-indexa todas las propiedades
   */
  async reindexAll(properties: any[], force: boolean = false): Promise<void> {
    try {
      console.log(`Starting full reindex of ${properties.length} properties (force: ${force})`);
      
      if (force) {
        // Eliminar toda la colección y recrearla
        console.log('Force reindex: recreating collection');
        await this.client.deleteCollection(this.collectionName);
        
        // Recrear colección (esto debería manejarse en la configuración)
        const { ensurePropertyCollection } = await import('./config');
        await ensurePropertyCollection();
      }

      // Indexar todas las propiedades
      await this.indexProperties(properties, 20); // Lotes más grandes para reindexación
      
      console.log('Full reindex completed successfully');
    } catch (error) {
      console.error('Error during full reindex:', error);
      throw error;
    }
  }

  /**
   * Valida la sincronización entre Convex y Qdrant
   */
  async validateSync(convexProperties: any[]): Promise<{
    inSync: boolean;
    missing: string[];
    extra: string[];
    total: number;
  }> {
    try {
      console.log('Validating sync between Convex and Qdrant');
      
      // Obtener todos los IDs de Qdrant
      const qdrantResult = await this.client.scroll(this.collectionName, {
        limit: 10000, // Ajustar según necesidades
        with_payload: false,
        with_vector: false,
      });

      // Convertir IDs de Qdrant a IDs de Convex para comparación
      const qdrantUuids = new Set(qdrantResult.points.map(point => point.id as string));
      const convexUuids = new Set(convexProperties.map(prop => this.convexIdToUuid(prop._id)));

      // Encontrar diferencias
      const missing = convexProperties
        .filter(prop => !qdrantUuids.has(this.convexIdToUuid(prop._id)))
        .map(prop => prop._id);

      const extra = Array.from(qdrantUuids)
        .filter(uuid => !convexUuids.has(uuid))
        .map(uuid => this.uuidToConvexId(uuid, convexProperties))
        .filter(id => id !== null) as string[];
      
      const inSync = missing.length === 0 && extra.length === 0;
      
      console.log(`Sync validation: ${inSync ? 'IN SYNC' : 'OUT OF SYNC'}`);
      console.log(`Missing in Qdrant: ${missing.length}, Extra in Qdrant: ${extra.length}`);
      
      return {
        inSync,
        missing,
        extra,
        total: convexProperties.length,
      };
    } catch (error) {
      console.error('Error validating sync:', error);
      throw error;
    }
  }

  /**
   * Limpia vectores huérfanos (que no existen en Convex)
   */
  async cleanupOrphanedVectors(validPropertyIds: string[]): Promise<number> {
    try {
      console.log('Cleaning up orphaned vectors');
      
      const validUuids = new Set(validPropertyIds.map(id => this.convexIdToUuid(id)));
      let deletedCount = 0;
      let offset: string | undefined;

      do {
        const result = await this.client.scroll(this.collectionName, {
          limit: 100,
          offset,
          with_payload: false,
          with_vector: false,
        });

        const orphanedIds = result.points
          .filter(point => !validUuids.has(point.id as string))
          .map(point => point.id as string);

        if (orphanedIds.length > 0) {
          await this.client.delete(this.collectionName, {
            wait: true,
            points: orphanedIds,
          });
          deletedCount += orphanedIds.length;
          console.log(`Deleted ${orphanedIds.length} orphaned vectors`);
        }

        offset = typeof result.next_page_offset === 'string' ? result.next_page_offset : undefined;
      } while (offset);
      
      console.log(`Cleanup completed: ${deletedCount} orphaned vectors removed`);
      return deletedCount;
    } catch (error) {
      console.error('Error cleaning up orphaned vectors:', error);
      throw error;
    }
  }
}
