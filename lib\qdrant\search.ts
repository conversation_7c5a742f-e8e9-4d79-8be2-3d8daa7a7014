import { getQdrantClient, QDRANT_CONFIG, SEARCH_CONFIG, PropertyPayload } from './config';
import { generateEmbedding, optimizeSearchQuery } from './embeddings';
import { detectTransactionIntent, adjustScoresByIntent, initializeIntentEmbeddings, areIntentEmbeddingsInitialized, IntentDetectionResult } from './intent-detector';
import { SemanticQueryDecomposer, DecompositionResult } from './semantic-decomposer';
import { MultiComponentEmbeddingGenerator, MultiComponentEmbeddingResult } from './multi-embedding';
import { WeightedSemanticSearchEngine, WeightedSearchResult } from './weighted-search';
import { ScoreCombiner, CombinedScore } from './score-combiner';
import { SemanticWeights } from './unified-config';
import { detectWeightProfile, detectWeightProfileIntelligent, getConfiguredWeights } from './weight-config';
import { AdaptiveWeightsEngine, AdaptiveWeightsResult } from './adaptive-weights-engine';
import { IntelligentCache, CachedQueryAnalysis } from './intelligent-cache';
import { SemanticNormalizer, NormalizationResult } from './semantic-normalizer';
import { PredictiveCache } from './predictive-cache';
import { AdvancedContextEngine, AdvancedContextResult } from './advanced-context-engine';
import { AdaptiveSemanticFilter } from './adaptive-filter';
import { getScoreThreshold } from './unified-config';
import { SemanticRelevanceAnalyzer, RelevanceAnalysisResult } from './semantic-relevance-analyzer';
import { AdvancedRankingOptimizer, OptimizedRankingResult } from './advanced-ranking-optimizer';
import { IntelligentPrecisionFilter, FilteringStats } from './intelligent-precision-filter';

// Interfaces para diagnóstico
interface DiagnosticStep {
  step: string;
  timestamp: number;
  duration: number;
  input: any;
  output: any;
  metadata?: any;
}

interface SearchDiagnostics {
  query: string;
  totalDuration: number;
  steps: DiagnosticStep[];
  summary: {
    decomposition: any;
    vectorResults: number;
    relevanceAnalyses: number;
    rankingOptimizations: number;
    finalResults: number;
    issues: string[];
    recommendations: string[];
  };
}

/**
 * Clase para manejar búsquedas semánticas de propiedades
 */
export class PropertyRAGEngine {
  private client = getQdrantClient();
  private collectionName = QDRANT_CONFIG.collectionName;
  private intentEmbeddingsInitialized = false;

  // Componentes del sistema de pesos semánticos
  private semanticDecomposer = new SemanticQueryDecomposer();
  private multiEmbeddingGenerator = new MultiComponentEmbeddingGenerator();
  private weightedSearchEngine = new WeightedSemanticSearchEngine();
  private scoreCombiner = new ScoreCombiner();
  private semanticNormalizer = new SemanticNormalizer();

  // 🎯 NUEVO: Motor de pesos adaptativos (Día 4)
  private adaptiveWeightsEngine = new AdaptiveWeightsEngine();

  // ⚡ NUEVO: Sistema de caché inteligente (Día 5)
  private intelligentCache = new IntelligentCache();

  // 🔮 NUEVO: Sistema de caché predictivo (Día 5)
  private predictiveCache: PredictiveCache;

  // 🧠 NUEVO: Motor de contexto avanzado (Día 6)
  private advancedContextEngine = new AdvancedContextEngine();

  // 🎯 NUEVO: Sistema de precisión semántica avanzada (Día 7)
  private semanticRelevanceAnalyzer = new SemanticRelevanceAnalyzer();
  private advancedRankingOptimizer = new AdvancedRankingOptimizer();
  private intelligentPrecisionFilter = new IntelligentPrecisionFilter();

  constructor() {
    // Inicializar caché predictivo
    this.predictiveCache = new PredictiveCache(this.intelligentCache, this);
  }

  /**
   * Inicializar embeddings de intención si no están inicializados
   */
  private async ensureIntentEmbeddingsInitialized(): Promise<void> {
    if (!this.intentEmbeddingsInitialized && !areIntentEmbeddingsInitialized()) {
      console.log('🧠 Inicializando embeddings de intención...');
      await initializeIntentEmbeddings();
      this.intentEmbeddingsInitialized = true;
    }
  }

  /**
   * 🎯 NUEVO: Búsqueda con precisión semántica avanzada (Día 7)
   * Sistema completo que integra análisis de relevancia, ranking optimizado y filtrado inteligente
   */
  async searchPropertiesWithAdvancedPrecision(options: {
    query: string;
    limit?: number;
    scoreThreshold?: number;
    filters?: SearchFilters;
    includePayload?: boolean;
    useSemanticWeights?: boolean;
    customWeights?: SemanticWeights;
    includeBreakdown?: boolean;
    enableAdvancedPrecision?: boolean;
    precisionOptions?: {
      strictMode?: boolean;
      adaptiveThresholds?: boolean;
      semanticValidation?: boolean;
      businessLogicFilters?: boolean;
      qualityFilters?: boolean;
      diversityPreservation?: boolean;
    };
  }): Promise<SemanticWeightedSearchResult | AdvancedPrecisionSearchResult> {
    const startTime = Date.now();
    const { query, enableAdvancedPrecision = true } = options;

    try {
      console.log(`🎯 Advanced precision search: "${query}" (precision: ${enableAdvancedPrecision})`);

      if (!enableAdvancedPrecision) {
        // Fallback al sistema anterior
        const fallbackResult = await this.searchPropertiesWithOptimizedCache(options);
        // Convertir a AdvancedPrecisionSearchResult si es necesario
        if (Array.isArray(fallbackResult)) {
          return this.createEmptyAdvancedResult(query, Date.now() - startTime);
        }
        return fallbackResult as SemanticWeightedSearchResult;
      }

      // 1. Realizar búsqueda base con sistema simple optimizado
      const baseResult = await this.searchPropertiesSimple(options);
      const baseResults = Array.isArray(baseResult) ? baseResult : baseResult;

      if (baseResults.length === 0) {
        return this.createEmptyAdvancedResult(query, Date.now() - startTime);
      }

      // 2. Obtener query descompuesta para análisis
      const decompositionResult = await this.semanticDecomposer.decompose(query);

      // 3. Analizar relevancia semántica de cada resultado
      const relevanceAnalyses = await Promise.all(
        baseResults.map(result =>
          this.semanticRelevanceAnalyzer.analyzeRelevance({
            query,
            decomposedQuery: decompositionResult,
            property: result.payload,
            originalScore: result.score,
          })
        )
      );

      // 4. Optimizar ranking con factores avanzados
      const rankingResults = await this.advancedRankingOptimizer.optimizeRanking(
        baseResults,
        query,
        decompositionResult,
        this.getDefaultWeights(), // Sistema simple no usa pesos multi-componente
        {
          emphasizeLocation: decompositionResult.decomposed.location ? true : false,
          emphasizePrice: decompositionResult.decomposed.price ? true : false,
          emphasizeFeatures: decompositionResult.decomposed.amenities ? true : false,
          guatemalanContext: true,
          diversityBoost: options.precisionOptions?.diversityPreservation ?? true,
          userContext: options.precisionOptions ? {
            preferences: [], // Se puede expandir en el futuro
          } : undefined,
        }
      );

      // 5. Aplicar filtrado inteligente
      const { filteredResults, stats } = await this.intelligentPrecisionFilter.filterResults(
        rankingResults,
        query,
        decompositionResult,
        relevanceAnalyses,
        {
          strictMode: options.precisionOptions?.strictMode ?? false,
          adaptiveThresholds: options.precisionOptions?.adaptiveThresholds ?? true,
          semanticValidation: options.precisionOptions?.semanticValidation ?? true,
          businessLogicFilters: options.precisionOptions?.businessLogicFilters ?? true,
          qualityFilters: options.precisionOptions?.qualityFilters ?? true,
          diversityPreservation: options.precisionOptions?.diversityPreservation ?? true,
        }
      );

      // 6. Convertir resultados optimizados de vuelta al formato estándar
      const finalResults = filteredResults.map(optimizedResult => ({
        id: optimizedResult.propertyId,
        score: optimizedResult.optimizedScore,
        payload: baseResults.find(r => r.id === optimizedResult.propertyId)?.payload!,
      })).slice(0, options.limit || 10);

      // 7. Crear resultado avanzado con métricas detalladas
      return this.createAdvancedPrecisionResult(
        finalResults,
        baseResult,
        relevanceAnalyses,
        filteredResults,
        stats,
        Date.now() - startTime
      );

    } catch (error) {
      console.error('Error en búsqueda con precisión avanzada:', error);

      // Fallback al sistema anterior en caso de error
      const fallbackResult = await this.searchPropertiesWithOptimizedCache(options);
      if (Array.isArray(fallbackResult)) {
        return this.createEmptyAdvancedResult(query, Date.now() - startTime);
      }
      return fallbackResult as SemanticWeightedSearchResult;
    }
  }

  /**
   * ⚡ NUEVO: Búsqueda optimizada con caché inteligente (Día 5)
   * Usa el sistema anterior pero con caché para mejorar rendimiento
   */
  async searchPropertiesWithOptimizedCache(options: {
    query: string;
    limit?: number;
    scoreThreshold?: number;
    filters?: SearchFilters;
    includePayload?: boolean;
    useSemanticWeights?: boolean;
    customWeights?: SemanticWeights;
    includeBreakdown?: boolean;
  }): Promise<SearchResult[] | SemanticWeightedSearchResult> {
    const startTime = Date.now();
    const { query } = options;

    try {
      console.log(`⚡ Optimized search: "${query}" (cache enabled)`);

      // 1. ⚡ Intentar obtener análisis del caché
      const cachedAnalysis = await this.intelligentCache.getCachedAnalysis(query);
      const analysisFromCache = !!cachedAnalysis;

      // 2. Usar sistema simple optimizado con información de caché
      const result = await this.searchPropertiesSimple(options);

      // 3. Agregar información de caché si se solicita breakdown
      if (options.includeBreakdown && typeof result === 'object' && 'results' in result) {
        (result as any).cacheInfo = {
          fromCache: analysisFromCache,
          cacheMetrics: this.intelligentCache.getMetrics(),
          optimizedSearch: true,
          processingTime: Date.now() - startTime,
        };
      }

      return result;

    } catch (error) {
      console.error('Error in optimized search:', error);
      // Fallback al sistema simple
      return this.searchPropertiesSimple(options);
    }
  }

  /**
   * ❌ ELIMINADO: Función del sistema multi-componente removida
   * ✅ USAR: searchPropertiesSimple() - Sistema optimizado
   */
  async searchPropertiesWithSemanticWeights(options: {
    query: string;
    limit?: number;
    scoreThreshold?: number;
    filters?: SearchFilters;
    includePayload?: boolean;
    useSemanticWeights?: boolean;
    customWeights?: SemanticWeights;
    includeBreakdown?: boolean;
  }): Promise<SearchResult[]> {
    console.log('⚠️  LEGACY: searchPropertiesWithSemanticWeights llamado, redirigiendo a sistema simple');
    return this.searchPropertiesSimple({
      query: options.query,
      limit: options.limit,
      scoreThreshold: options.scoreThreshold,
      filters: options.filters,
      adaptiveThreshold: true,
      intentDetection: true,
    });
  }





  /**
   * 🚀 NUEVO: Búsqueda semántica simple con embeddings estructurados
   * Usa búsqueda directa sin sistema multi-componente para máxima compatibilidad
   * con embeddings simples indexados en Qdrant
   */
  async searchPropertiesSimple(options: {
    query: string;
    limit?: number;
    scoreThreshold?: number;
    filters?: SearchFilters;
    includePayload?: boolean;
    adaptiveThreshold?: boolean;
    intentDetection?: boolean;
  }): Promise<SearchResult[]> {
    try {
      const {
        query,
        limit = SEARCH_CONFIG.defaultLimit,
        scoreThreshold = SEARCH_CONFIG.scoreThreshold,
        filters,
        includePayload = true,
        adaptiveThreshold = false,
        intentDetection = false,
      } = options;

      console.log(`🚀 Simple semantic search: "${query}" (embedding-based)`);

      // 1. Detectar intención de transacción si está habilitado
      let intentResult = null;
      if (intentDetection) {
        await this.ensureIntentEmbeddingsInitialized();
        intentResult = await detectTransactionIntent(query);
        console.log(`🎯 Intent detected: ${intentResult.intent} (confidence: ${intentResult.confidence.toFixed(3)})`);
      }

      // 2. Threshold adaptativo basado en el tipo de consulta
      let finalThreshold = scoreThreshold;
      if (adaptiveThreshold) {
        finalThreshold = this.calculateAdaptiveThreshold(query, scoreThreshold);
      }

      // 3. Normalizar consulta semánticamente (sin regex, solo IA)
      const normalizedQuery = await this.semanticNormalizer.normalize(query);
      console.log(`🧠 Normalized query: "${normalizedQuery.normalized}"`);

      // 4. Generar embedding para la consulta normalizada
      const queryVector = await generateEmbedding(normalizedQuery.normalized);

      // 5. Construir filtros de Qdrant
      const qdrantFilter = this.buildQdrantFilter(filters);

      // 6. Realizar búsqueda vectorial directa
      const searchResult = await this.client.search(this.collectionName, {
        vector: queryVector,
        limit: Math.min(limit, SEARCH_CONFIG.maxLimit),
        score_threshold: finalThreshold,
        filter: qdrantFilter,
        with_payload: includePayload,
        with_vector: false,
      });

      // 7. Procesar resultados
      let results: SearchResult[] = searchResult.map(point => ({
        id: point.id as string,
        score: point.score,
        payload: point.payload as unknown as PropertyPayload,
      }));

      // 8. Aplicar boost por intención si se detectó
      if (intentResult && intentResult.confidence > 0.6) {
        const adjustedResults = adjustScoresByIntent(results, intentResult);

        // Mapear resultados ajustados de vuelta al formato SearchResult
        results = adjustedResults.map((adjustedResult: any, index: number) => ({
          id: results[index].id,
          score: adjustedResult.score,
          payload: results[index].payload,
        }));

        console.log(`🎯 Applied intent boost for ${intentResult.intent}`);
      }

      // 9. Filtrar por threshold final y ordenar
      results = results
        .filter(result => result.score >= finalThreshold)
        .sort((a, b) => b.score - a.score);

      console.log(`✅ Simple search completed: ${results.length} results (threshold: ${finalThreshold.toFixed(3)})`);
      return results;

    } catch (error) {
      console.error('Error in simple semantic search:', error);
      throw new Error(`Simple search failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Búsqueda semántica principal (SISTEMA TRADICIONAL)
   */
  async searchProperties(options: {
    query: string;
    limit?: number;
    scoreThreshold?: number;
    filters?: SearchFilters;
    includePayload?: boolean;
    adaptiveThreshold?: boolean; // Nuevo: permite threshold adaptativo
    intentDetection?: boolean; // Nuevo: permite detección de intención
    useSemanticWeights?: boolean; // NUEVO: usar sistema de pesos semánticos
  }): Promise<SearchResult[]> {
    try {
      const {
        query,
        limit = SEARCH_CONFIG.defaultLimit,
        scoreThreshold = SEARCH_CONFIG.scoreThreshold,
        filters,
        includePayload = true,
        adaptiveThreshold = false,
        intentDetection = false,
        useSemanticWeights = false, // Por defecto usar sistema tradicional
      } = options;

      // ❌ LEGACY: Sistema multi-componente descartado tras validación empírica
      // ✅ OPTIMIZADO: Usar siempre sistema simple (3.2x más rápido, 49% mejor precisión)
      if (useSemanticWeights) {
        console.log('⚠️  LEGACY: useSemanticWeights=true detectado, redirigiendo a sistema simple optimizado');
        // Redirigir al sistema simple en lugar del multi-componente
        return this.searchPropertiesSimple({
          query,
          limit,
          scoreThreshold,
          filters,
          adaptiveThreshold: true,
          intentDetection: true,
        });
      }

      console.log(`Searching properties for: "${query}"`);

      // 1. Detectar intención de transacción si está habilitado
      let intentResult = null;
      if (intentDetection) {
        await this.ensureIntentEmbeddingsInitialized();
        intentResult = await detectTransactionIntent(query);
        console.log(`🎯 Intención detectada: ${intentResult.intent} (confianza: ${intentResult.confidence.toFixed(3)})`);
      }

      // 2. Threshold adaptativo basado en el tipo de consulta
      let finalThreshold = scoreThreshold;
      if (adaptiveThreshold) {
        finalThreshold = this.calculateAdaptiveThreshold(query, scoreThreshold);
      }

      // 3. Optimizar consulta y generar embedding
      const optimizedQuery = optimizeSearchQuery(query);
      const queryVector = await generateEmbedding(optimizedQuery);

      // 4. Construir filtros de Qdrant
      const qdrantFilter = this.buildQdrantFilter(filters);

      // 5. Realizar búsqueda vectorial
      const searchResult = await this.client.search(this.collectionName, {
        vector: queryVector,
        limit: Math.min(limit, SEARCH_CONFIG.maxLimit),
        score_threshold: finalThreshold,
        filter: qdrantFilter,
        with_payload: includePayload,
        with_vector: false,
      });

      // 6. Procesar resultados
      let results: SearchResult[] = searchResult.map(point => ({
        id: point.id as string,
        score: point.score,
        payload: point.payload as unknown as PropertyPayload,
      }));

      // 7. Ajustar scores por intención si está habilitado
      if (intentDetection && intentResult && intentResult.intent !== 'ambiguous') {
        console.log(`🔧 Ajustando scores basado en intención: ${intentResult.intent}`);
        const adjustedResults = adjustScoresByIntent(results, intentResult);

        // Mapear resultados ajustados de vuelta al formato SearchResult
        results = adjustedResults.map((adjustedResult: any, index: any) => ({
          id: results[index].id,
          score: adjustedResult.score,
          payload: results[index].payload,
        }));

        // Re-ordenar por nueva puntuación
        results.sort((a, b) => b.score - a.score);
        console.log(`✅ Scores ajustados y resultados reordenados`);
      }

      console.log(`Found ${results.length} properties with scores >= ${finalThreshold}`);
      if (intentDetection && intentResult) {
        console.log(`🎯 Intent detection final: ${intentResult.intent} (${Math.round(intentResult.confidence * 100)}%)`);
      }
      
      return results;
    } catch (error) {
      console.error('Error in semantic search:', error);
      throw error;
    }
  }

  /**
   * Búsqueda semántica con información de intención
   */
  async searchPropertiesWithIntent(options: {
    query: string;
    limit?: number;
    scoreThreshold?: number;
    filters?: SearchFilters;
    includePayload?: boolean;
    adaptiveThreshold?: boolean;
    intentDetection?: boolean;
  }): Promise<SearchWithIntentResult> {
    try {
      const {
        query,
        limit = SEARCH_CONFIG.defaultLimit,
        scoreThreshold = SEARCH_CONFIG.scoreThreshold,
        filters,
        includePayload = true,
        adaptiveThreshold = false,
        intentDetection = false,
      } = options;

      console.log(`Searching properties for: "${query}"`);

      // 1. Detectar intención de transacción si está habilitado
      let intentResult = null;
      if (intentDetection) {
        await this.ensureIntentEmbeddingsInitialized();
        intentResult = await detectTransactionIntent(query);
        console.log(`🎯 Intención detectada: ${intentResult.intent} (confianza: ${intentResult.confidence.toFixed(3)})`);
      }

      // 2. Threshold adaptativo basado en el tipo de consulta
      let finalThreshold = scoreThreshold;
      if (adaptiveThreshold) {
        finalThreshold = this.calculateAdaptiveThreshold(query, scoreThreshold);
      }

      // 3. Optimizar consulta y generar embedding
      const optimizedQuery = optimizeSearchQuery(query);
      const queryVector = await generateEmbedding(optimizedQuery);

      // 4. Construir filtros de Qdrant
      const qdrantFilter = this.buildQdrantFilter(filters);

      // 5. Realizar búsqueda vectorial
      const searchResult = await this.client.search(this.collectionName, {
        vector: queryVector,
        limit: Math.min(limit, SEARCH_CONFIG.maxLimit),
        score_threshold: finalThreshold,
        filter: qdrantFilter,
        with_payload: includePayload,
        with_vector: false,
      });

      // 6. Procesar resultados
      let results: SearchResult[] = searchResult.map(point => ({
        id: point.id as string,
        score: point.score,
        payload: point.payload as unknown as PropertyPayload,
      }));

      // 7. Ajustar scores por intención si está habilitado
      if (intentDetection && intentResult && intentResult.intent !== 'ambiguous') {
        console.log(`🔧 Ajustando scores basado en intención: ${intentResult.intent}`);
        const adjustedResults = adjustScoresByIntent(results, intentResult);

        // Mapear resultados ajustados de vuelta al formato SearchResult
        results = adjustedResults.map((adjustedResult: any, index: any) => ({
          id: results[index].id,
          score: adjustedResult.score,
          payload: results[index].payload,
        }));

        // Re-ordenar por nueva puntuación
        results.sort((a, b) => b.score - a.score);
        console.log(`✅ Scores ajustados y resultados reordenados`);
      }

      console.log(`Found ${results.length} properties with scores >= ${finalThreshold}`);
      if (intentDetection && intentResult) {
        console.log(`🎯 Intent detection final: ${intentResult.intent} (${Math.round(intentResult.confidence * 100)}%)`);
      }

      return {
        results,
        intentResult: intentResult || undefined,
      };
    } catch (error) {
      console.error('Error in semantic search:', error);
      throw error;
    }
  }

  /**
   * Búsqueda de propiedades similares
   */
  async findSimilarProperties(options: {
    propertyId: string;
    limit?: number;
    scoreThreshold?: number;
    excludeOriginal?: boolean;
  }): Promise<SearchResult[]> {
    try {
      const {
        propertyId,
        limit = 5,
        scoreThreshold = 0.8,
        excludeOriginal = true,
      } = options;

      console.log(`Finding similar properties to: ${propertyId}`);

      // Obtener el vector de la propiedad original
      const originalProperty = await this.client.retrieve(this.collectionName, {
        ids: [propertyId],
        with_vector: true,
      });

      if (originalProperty.length === 0) {
        throw new Error(`Property ${propertyId} not found in index`);
      }

      const originalVector = originalProperty[0].vector as number[];

      // Buscar propiedades similares
      const searchResult = await this.client.search(this.collectionName, {
        vector: originalVector,
        limit: excludeOriginal ? limit + 1 : limit,
        score_threshold: scoreThreshold,
        with_payload: true,
        with_vector: false,
      });

      // Filtrar la propiedad original si es necesario
      let results = searchResult.map(point => ({
        id: point.id as string,
        score: point.score,
        payload: point.payload as unknown as PropertyPayload,
      }));

      if (excludeOriginal) {
        results = results.filter(result => result.id !== propertyId);
        results = results.slice(0, limit);
      }

      console.log(`Found ${results.length} similar properties`);
      return results;
    } catch (error) {
      console.error('Error finding similar properties:', error);
      throw error;
    }
  }

  /**
   * Búsqueda híbrida (vectorial + filtros tradicionales)
   */
  async hybridSearch(options: {
    query?: string;
    filters?: SearchFilters;
    limit?: number;
    scoreThreshold?: number;
    vectorWeight?: number;
  }): Promise<SearchResult[]> {
    try {
      const {
        query,
        filters,
        limit = SEARCH_CONFIG.defaultLimit,
        scoreThreshold = getScoreThreshold() * 0.9, // ✅ UNIFICADO: Basado en configuración centralizada, ligeramente más bajo para híbrida
        vectorWeight = SEARCH_CONFIG.hybridAlpha,
      } = options;

      let results: SearchResult[] = [];

      if (query && query.trim()) {
        // Búsqueda vectorial
        const vectorResults = await this.searchProperties({
          query,
          limit: limit * 2, // Obtener más resultados para combinar
          scoreThreshold: scoreThreshold * 0.7, // ✅ Umbral más bajo para búsqueda vectorial en híbrida
          filters,
          includePayload: true,
        });

        results = vectorResults;
      } else if (filters) {
        // Solo filtros tradicionales
        const filterResults = await this.filterOnlySearch(filters, limit);
        results = filterResults;
      }

      // Aplicar peso vectorial si hay consulta de texto
      if (query && vectorWeight < 1.0) {
        results = results.map(result => ({
          ...result,
          score: result.score * vectorWeight + (1 - vectorWeight) * this.calculateFilterScore(result.payload, filters),
        }));

        // Re-ordenar por nueva puntuación
        results.sort((a, b) => b.score - a.score);
      }

      return results.slice(0, limit);
    } catch (error) {
      console.error('Error in hybrid search:', error);
      throw error;
    }
  }

  /**
   * Búsqueda solo por filtros (sin vectores)
   */
  private async filterOnlySearch(filters: SearchFilters, limit: number): Promise<SearchResult[]> {
    const qdrantFilter = this.buildQdrantFilter(filters);

    const scrollResult = await this.client.scroll(this.collectionName, {
      filter: qdrantFilter,
      limit,
      with_payload: true,
      with_vector: false,
    });

    return scrollResult.points.map(point => ({
      id: point.id as string,
      score: 1.0, // Puntuación neutral para filtros
      payload: point.payload as unknown as PropertyPayload,
    }));
  }

  /**
   * Calcular threshold adaptativo basado en la especificidad de la consulta
   */
  private calculateAdaptiveThreshold(query: string, baseThreshold: number): number {
    const queryWords = query.trim().split(/\s+/);
    const queryLength = query.length;
    const lowercaseQuery = query.toLowerCase();
    
    // Detectar consultas muy genéricas (necesitan threshold más bajo)
    const genericTerms = ['apartamento', 'casa', 'propiedad', 'inmueble', 'comprar', 'vender', 'alquilar', 'rentar'];
    const hasGenericTerms = genericTerms.some(term => lowercaseQuery.includes(term));
    
    // Detectar consultas con ubicaciones genéricas
    const genericLocations = ['zona', 'ciudad', 'área', 'sector'];
    const hasGenericLocation = genericLocations.some(term => lowercaseQuery.includes(term));
    
    // CASO 1: Consultas muy cortas o genéricas (threshold muy bajo)
    if (queryWords.length <= 2 || queryLength <= 15 || (hasGenericTerms && queryWords.length <= 3)) {
      console.log(`Threshold adaptativo: Query genérica/corta "${query}" -> threshold bajo`);
      return Math.max(baseThreshold * 0.5, 0.25); // 50% del base, mínimo 0.25
    }
    
    // CASO 2: Consultas con ubicaciones genéricas (threshold bajo-medio)
    if (hasGenericLocation && queryWords.length <= 4) {
      console.log(`Threshold adaptativo: Query con ubicación genérica "${query}" -> threshold bajo-medio`);
      return Math.max(baseThreshold * 0.65, 0.3); // 65% del base, mínimo 0.3
    }
    
    // CASO 3: Consultas específicas con muchos detalles (threshold normal)
    if (queryWords.length >= 6 || queryLength >= 50) {
      console.log(`Threshold adaptativo: Query específica "${query}" -> threshold normal`);
      return baseThreshold; // Mantener threshold base
    }
    
    // CASO 4: Consultas medias (threshold intermedio)
    console.log(`Threshold adaptativo: Query media "${query}" -> threshold intermedio`);
    return baseThreshold * 0.75; // 75% del base
  }







  /**
   * Construir filtros de Qdrant a partir de filtros de búsqueda
   */
  private buildQdrantFilter(filters?: SearchFilters): any {
    if (!filters) return undefined;

    const conditions: any[] = [];

    // Filtros básicos
    if (filters.type) {
      conditions.push({ key: 'type', match: { value: filters.type } });
    }
    if (filters.status) {
      conditions.push({ key: 'status', match: { value: filters.status } });
    }
    if (filters.currency) {
      conditions.push({ key: 'currency', match: { value: filters.currency } });
    }

    // Filtros de rango
    if (filters.priceRange) {
      const priceCondition: any = { key: 'price', range: {} };
      if (filters.priceRange.min !== undefined) {
        priceCondition.range.gte = filters.priceRange.min;
      }
      if (filters.priceRange.max !== undefined) {
        priceCondition.range.lte = filters.priceRange.max;
      }
      conditions.push(priceCondition);
    }

    if (filters.areaRange) {
      const areaCondition: any = { key: 'area', range: {} };
      if (filters.areaRange.min !== undefined) {
        areaCondition.range.gte = filters.areaRange.min;
      }
      if (filters.areaRange.max !== undefined) {
        areaCondition.range.lte = filters.areaRange.max;
      }
      conditions.push(areaCondition);
    }

    // Filtros de habitaciones/baños
    if (filters.minBedrooms !== undefined) {
      conditions.push({ key: 'bedrooms', range: { gte: filters.minBedrooms } });
    }
    if (filters.minBathrooms !== undefined) {
      conditions.push({ key: 'bathrooms', range: { gte: filters.minBathrooms } });
    }

    // Filtros de ubicación
    if (filters.location) {
      if (filters.location.country) {
        conditions.push({ key: 'country', match: { value: filters.location.country } });
      }
      if (filters.location.level1) {
        conditions.push({ key: 'level1', match: { value: filters.location.level1 } });
      }
      if (filters.location.level2) {
        conditions.push({ key: 'level2', match: { value: filters.location.level2 } });
      }
      if (filters.location.neighborhood) {
        conditions.push({ key: 'neighborhood', match: { value: filters.location.neighborhood } });
      }
    }

    // Filtros de amenidades
    if (filters.amenities && filters.amenities.length > 0) {
      const amenityConditions = filters.amenities.map(amenity => ({
        key: 'amenities',
        match: { value: amenity }
      }));
      conditions.push({ should: amenityConditions });
    }

    // Filtros booleanos
    if (filters.isActive !== undefined) {
      conditions.push({ key: 'isActive', match: { value: filters.isActive } });
    }
    if (filters.isFeatured !== undefined) {
      conditions.push({ key: 'isFeatured', match: { value: filters.isFeatured } });
    }
    if (filters.isPremium !== undefined) {
      conditions.push({ key: 'isPremium', match: { value: filters.isPremium } });
    }

    // Filtro geográfico (radio)
    if (filters.geoRadius) {
      conditions.push({
        key: 'coordinates',
        geo_radius: {
          center: {
            lat: filters.geoRadius.center.lat,
            lon: filters.geoRadius.center.lon,
          },
          radius: filters.geoRadius.radius,
        },
      });
    }

    return conditions.length > 0 ? { must: conditions } : undefined;
  }

  /**
   * Calcular puntuación basada en filtros tradicionales
   */
  private calculateFilterScore(payload: PropertyPayload, filters?: SearchFilters): number {
    if (!filters) return 0.5;

    let score = 0.5;
    let factors = 0;

    // Bonificaciones por características destacadas
    if (payload.isFeatured) {
      score += 0.2;
      factors++;
    }
    if (payload.isPremium) {
      score += 0.3;
      factors++;
    }

    // Penalización por antigüedad (propiedades más nuevas tienen mejor score)
    const daysSinceCreated = (Date.now() - new Date(payload.createdAt).getTime()) / (1000 * 60 * 60 * 24);
    if (daysSinceCreated < 30) {
      score += 0.1;
      factors++;
    }

    return Math.min(1.0, score);
  }

  /**
   * ❌ LEGACY: Función de conveniencia redirigida al sistema simple
   * ✅ OPTIMIZADO: Usa sistema simple en lugar del multi-componente descartado
   */
  async searchWithAdaptiveWeights(options: {
    query: string;
    limit?: number;
    scoreThreshold?: number;
    filters?: SearchFilters;
    includeBreakdown?: boolean;
  }): Promise<SearchResult[]> {
    console.log('⚠️  LEGACY: searchWithAdaptiveWeights llamado, redirigiendo a sistema simple');
    return this.searchPropertiesSimple({
      ...options,
      adaptiveThreshold: true,
      intentDetection: true,
    });
  }

  /**
   * 🧠 NUEVO: Búsqueda con contexto avanzado (Día 6)
   * Sistema completo con análisis de contexto, desambiguación y memoria conversacional
   */
  async searchWithAdvancedContext(options: {
    query: string;
    sessionId?: string;
    userId?: string;
    limit?: number;
    scoreThreshold?: number;
    filters?: SearchFilters;
    includeBreakdown?: boolean;
    enableContextAnalysis?: boolean;
    enableDisambiguation?: boolean;
    enableConversationalMemory?: boolean;
    enableEdgeCaseHandling?: boolean;
  }): Promise<SearchResult[] | SemanticWeightedSearchResult> {
    const startTime = Date.now();

    try {
      console.log(`🧠 Búsqueda con contexto avanzado: "${options.query}"`);

      // 1. Procesar consulta con contexto avanzado
      const contextResult = await this.advancedContextEngine.processQuery(options.query, {
        enableContextAnalysis: options.enableContextAnalysis ?? true,
        enableDisambiguation: options.enableDisambiguation ?? true,
        enableConversationalMemory: options.enableConversationalMemory ?? true,
        enableEdgeCaseHandling: options.enableEdgeCaseHandling ?? true,
        sessionId: options.sessionId,
        userId: options.userId,
      });

      // 2. Usar consulta procesada para búsqueda
      const searchOptions = {
        ...options,
        query: contextResult.finalQuery,
        useSemanticWeights: true,
        includeBreakdown: options.includeBreakdown || false,
      };

      const searchResult = await this.searchPropertiesWithOptimizedCache(searchOptions);

      // 3. Registrar feedback automático basado en resultados
      if (options.sessionId) {
        const resultsCount = Array.isArray(searchResult) ? searchResult.length : searchResult.results.length;
        // Feedback automático: positivo si hay resultados, neutral si no
        const autoFeedback = resultsCount > 0 ? 'positive' : 'neutral';
        this.advancedContextEngine.recordFeedback(options.sessionId, autoFeedback, resultsCount);
      }

      // 4. Agregar información de contexto avanzado al resultado
      if (options.includeBreakdown && typeof searchResult === 'object' && 'results' in searchResult) {
        (searchResult as any).advancedContext = contextResult;
        (searchResult as any).contextProcessingTime = Date.now() - startTime;
      }

      console.log(`✅ Búsqueda con contexto avanzado completada en ${Date.now() - startTime}ms`);
      return searchResult;

    } catch (error) {
      console.error('Error en búsqueda con contexto avanzado:', error);
      // Fallback a búsqueda normal
      return this.searchPropertiesWithOptimizedCache(options);
    }
  }

  /**
   * 🎯 NUEVO: Obtener estadísticas del motor adaptativo (Día 4)
   */
  getAdaptiveEngineStats(): {
    totalRules: number;
    ruleNames: string[];
    profilesAvailable: number;
  } {
    return this.adaptiveWeightsEngine.getEngineStats();
  }

  /**
   * ⚡ NUEVO: Pre-calentar caché para consulta (Día 5)
   */
  private async preWarmCache(query: string): Promise<void> {
    try {
      console.log(`⚡ Pre-warming cache for: "${query}"`);

      // Simplemente ejecutar una búsqueda simple para que se cachee el análisis
      await this.searchPropertiesSimple({
        query,
        limit: 1,
        adaptiveThreshold: true,
        intentDetection: true,
      });

      console.log(`✅ Cache pre-warmed for: "${query}"`);

    } catch (error) {
      console.warn(`Error pre-warming cache for "${query}":`, error);
    }
  }


  /**
   * ⚡ NUEVO: Obtener métricas del caché (Día 5)
   */
  getCacheMetrics() {
    return this.intelligentCache.getMetrics();
  }

  /**
   * ⚡ NUEVO: Limpiar caché (Día 5)
   */
  clearCache(): void {
    this.intelligentCache.clearCache();
  }

  /**
   * 🔮 NUEVO: Obtener estadísticas del caché predictivo (Día 5)
   */
  getPredictiveCacheStats() {
    return this.predictiveCache.getStats();
  }

  /**
   * 🔮 NUEVO: Sugerir consultas relacionadas (Día 5)
   */
  suggestRelatedQueries(query: string): string[] {
    return this.predictiveCache.suggestRelatedQueries(query);
  }

  /**
   * 🔮 NUEVO: Actualizar caché predictivo manualmente (Día 5)
   */
  async updatePredictiveCache(): Promise<void> {
    return this.predictiveCache.updatePredictiveCache();
  }

  /**
   * ⚡ NUEVO: Generar embeddings básicos para pre-cálculo (Día 5)
   */
  private async generateBasicEmbeddings(query: string): Promise<number[]> {
    try {
      return await generateEmbedding(query);
    } catch (error) {
      console.warn('Error generando embedding básico:', error);
      return [];
    }
  }

  /**
   * ⚡ NUEVO: Crear descomposición de fallback (Día 5)
   */
  private async createFallbackDecomposition(query: string): Promise<DecompositionResult> {
    return {
      decomposed: {
        location: '',
        property: query,
        amenities: '',
        price: '',
        intent: 'neutral',
        priceContext: 'neutral',
        characteristics: { bedrooms: undefined, bathrooms: undefined, area: undefined },
        original: query,
      },
      confidence: 0.5,
      detectedProfile: 'GENERAL_SEARCH',
      processingTime: 0,
    };
  }

  /**
   * ⚡ NUEVO: Generar embeddings optimizados con paralelización (Día 5)
   */
  private async generateOptimizedEmbeddings(decomposed: any): Promise<MultiComponentEmbeddingResult> {
    const startTime = Date.now();
    const maxParallel = parseInt(process.env.PARALLEL_EMBEDDING_LIMIT || '4');

    try {
      // Preparar componentes para embeddings paralelos
      const components = [
        { name: 'location', text: decomposed.location },
        { name: 'property', text: decomposed.property },
        { name: 'amenities', text: decomposed.amenities },
        { name: 'price', text: decomposed.price },
      ].filter(comp => comp.text && comp.text.trim().length > 0);

      // Generar embeddings en lotes paralelos
      const embeddingPromises = components.map(async (comp) => {
        try {
          const embedding = await generateEmbedding(comp.text);
          return {
            component: comp.name,
            text: comp.text,
            embedding,
            isEmpty: false,
            processingTime: Date.now() - startTime,
          };
        } catch (error) {
          console.warn(`Error generando embedding para ${comp.name}:`, error);
          return {
            component: comp.name,
            text: comp.text,
            embedding: [],
            isEmpty: true,
            processingTime: Date.now() - startTime,
          };
        }
      });

      // Ejecutar en lotes para no sobrecargar OpenAI
      const results = await this.executeBatched(embeddingPromises, maxParallel);

      // Construir resultado final
      const embeddingResult: MultiComponentEmbeddingResult = {
        location: results.find(r => r.component === 'location') || this.createEmptyEmbedding('location'),
        property: results.find(r => r.component === 'property') || this.createEmptyEmbedding('property'),
        amenities: results.find(r => r.component === 'amenities') || this.createEmptyEmbedding('amenities'),
        characteristics: results.find(r => r.component === 'characteristics') || this.createEmptyEmbedding('characteristics'),
        price: results.find(r => r.component === 'price') || this.createEmptyEmbedding('price'),
        totalProcessingTime: Date.now() - startTime,
        successfulComponents: results.filter(r => !r.isEmpty).length,
      };

      console.log(`⚡ Optimized embeddings generated: ${embeddingResult.successfulComponents}/5 components in ${embeddingResult.totalProcessingTime}ms`);
      return embeddingResult;

    } catch (error) {
      console.error('Error en embeddings optimizados:', error);
      // Fallback al generador original
      return await this.multiEmbeddingGenerator.generateMultiComponentEmbeddings(decomposed);
    }
  }

  /**
   * ⚡ NUEVO: Ejecutar promesas en lotes paralelos (Día 5)
   */
  private async executeBatched<T>(promises: Promise<T>[], batchSize: number): Promise<T[]> {
    const results: T[] = [];

    for (let i = 0; i < promises.length; i += batchSize) {
      const batch = promises.slice(i, i + batchSize);
      const batchResults = await Promise.allSettled(batch);

      batchResults.forEach(result => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        }
      });
    }

    return results;
  }

  /**
   * ⚡ NUEVO: Crear embedding vacío (Día 5)
   */
  private createEmptyEmbedding(component: string) {
    return {
      component,
      text: '',
      embedding: [],
      isEmpty: true,
      processingTime: 0,
    };
  }

  /**
   * 🧠 NUEVO: Obtener estadísticas de sesión conversacional (Día 6)
   */
  getSessionStats(sessionId: string) {
    return this.advancedContextEngine.getSessionStats(sessionId);
  }

  /**
   * 🧠 NUEVO: Obtener información de zona guatemalteca (Día 6)
   */
  getZoneInfo(zone: string) {
    return this.advancedContextEngine.getZoneInfo(zone);
  }

  /**
   * 🧠 NUEVO: Registrar feedback manual del usuario (Día 6)
   */
  recordUserFeedback(sessionId: string, feedback: 'positive' | 'negative' | 'neutral', resultsCount: number): void {
    this.advancedContextEngine.recordFeedback(sessionId, feedback, resultsCount);
  }

  /**
   * 🎯 NUEVO: Obtiene pesos por defecto (Día 7)
   */
  private getDefaultWeights(): SemanticWeights {
    return {
      location: 0.4,
      property: 0.25,
      amenities: 0.2,
      characteristics: 0.05,
      price: 0.1,
    };
  }

  /**
   * 🎯 NUEVO: Crea resultado vacío para precisión avanzada (Día 7)
   */
  private createEmptyAdvancedResult(query: string, processingTime: number): AdvancedPrecisionSearchResult {
    return {
      results: [],
      decomposition: {
        decomposed: {
          location: '',
          property: '',
          amenities: '',
          price: '',
          intent: '',
          priceContext: '',
          characteristics: {},
          original: '',
        },
        confidence: 0,
        detectedProfile: 'BALANCED',
        processingTime: 0,
      },
      weightProfile: 'BALANCED',
      weights: this.getDefaultWeights(),
      embeddingStats: {
        successRate: 0,
        averageProcessingTime: 0,
        primaryComponent: 'none',
        hasLocation: false,
      },
      searchStats: {
        totalResults: 0,
        uniqueProperties: 0,
        averageScore: 0,
        topScore: 0,
      },
      componentResults: [],
      processingTime,
      precisionMetrics: {
        originalResultsCount: 0,
        filteredResultsCount: 0,
        averageRelevanceImprovement: 0,
        filteringStats: {
          totalResults: 0,
          filteredResults: 0,
          passedResults: 0,
          filterReasons: {},
          averageRelevanceImprovement: 0,
          processingTime: 0,
        },
        topResultConfidence: 0,
        precisionScore: 0,
      },
      relevanceAnalyses: [],
      rankingOptimizations: [],
      advancedProcessingTime: processingTime,
    };
  }

  /**
   * 🎯 NUEVO: Crea resultado avanzado con métricas de precisión (Día 7)
   */
  private createAdvancedPrecisionResult(
    finalResults: SearchResult[],
    baseResult: SearchResult[] | SemanticWeightedSearchResult,
    relevanceAnalyses: RelevanceAnalysisResult[],
    rankingOptimizations: OptimizedRankingResult[],
    filteringStats: FilteringStats,
    processingTime: number
  ): AdvancedPrecisionSearchResult {
    // Extraer información base
    const baseInfo = Array.isArray(baseResult) ? {
      decomposition: {
        decomposed: {
          location: '',
          property: '',
          amenities: '',
          price: '',
          intent: '',
          priceContext: '',
          characteristics: {},
          original: '',
        },
        confidence: 0,
        detectedProfile: 'BALANCED',
        processingTime: 0,
      },
      weightProfile: 'BALANCED',
      weights: this.getDefaultWeights(),
      embeddingStats: {
        successRate: 1,
        averageProcessingTime: processingTime,
        primaryComponent: 'location',
        hasLocation: false,
      },
      searchStats: {
        totalResults: baseResult.length,
        uniqueProperties: baseResult.length,
        averageScore: baseResult.reduce((sum, r) => sum + r.score, 0) / (baseResult.length || 1),
        topScore: baseResult.length > 0 ? baseResult[0].score : 0,
      },
      componentResults: [],
      processingTime: 0,
    } : baseResult;

    // Calcular métricas de precisión
    const topResultConfidence = relevanceAnalyses.length > 0 ? relevanceAnalyses[0].confidence : 0;
    const precisionScore = this.calculatePrecisionScore(relevanceAnalyses, filteringStats);

    return {
      results: finalResults,
      decomposition: baseInfo.decomposition,
      weightProfile: baseInfo.weightProfile,
      weights: baseInfo.weights,
      embeddingStats: baseInfo.embeddingStats,
      searchStats: {
        totalResults: finalResults.length,
        uniqueProperties: finalResults.length,
        averageScore: finalResults.reduce((sum, r) => sum + r.score, 0) / (finalResults.length || 1),
        topScore: finalResults.length > 0 ? finalResults[0].score : 0,
      },
      componentResults: baseInfo.componentResults,
      processingTime: baseInfo.processingTime,
      precisionMetrics: {
        originalResultsCount: filteringStats.totalResults,
        filteredResultsCount: filteringStats.passedResults,
        averageRelevanceImprovement: filteringStats.averageRelevanceImprovement,
        filteringStats,
        topResultConfidence,
        precisionScore,
      },
      relevanceAnalyses,
      rankingOptimizations,
      advancedProcessingTime: processingTime,
    };
  }

  /**
   * 🎯 NUEVO: Calcula score de precisión general (Día 7)
   */
  private calculatePrecisionScore(
    relevanceAnalyses: RelevanceAnalysisResult[],
    filteringStats: FilteringStats
  ): number {
    if (relevanceAnalyses.length === 0) return 0;

    // Factores que contribuyen al score de precisión
    const averageRelevance = relevanceAnalyses.reduce((sum, analysis) =>
      sum + analysis.metrics.overallRelevance, 0) / relevanceAnalyses.length;

    const averageConfidence = relevanceAnalyses.reduce((sum, analysis) =>
      sum + analysis.confidence, 0) / relevanceAnalyses.length;

    const filteringEffectiveness = filteringStats.totalResults > 0
      ? filteringStats.passedResults / filteringStats.totalResults
      : 1;

    const improvementFactor = Math.max(0, filteringStats.averageRelevanceImprovement);

    // Combinar factores (pesos que suman 1.0)
    return (
      averageRelevance * 0.4 +
      averageConfidence * 0.3 +
      filteringEffectiveness * 0.2 +
      improvementFactor * 0.1
    );
  }

  /**
   * ⚡ NUEVO: Destructor para limpiar recursos (Día 5)
   */
  destroy(): void {
    this.intelligentCache.destroy();
    this.predictiveCache.destroy();
    this.advancedContextEngine.destroy();
  }
}

// Tipos TypeScript
export interface SearchFilters {
  type?: string;
  status?: string;
  currency?: string;
  priceRange?: { min?: number; max?: number };
  areaRange?: { min?: number; max?: number };
  minBedrooms?: number;
  minBathrooms?: number;
  location?: {
    country?: string;
    level1?: string;
    level2?: string;
    neighborhood?: string;
  };
  amenities?: string[];
  isActive?: boolean;
  isFeatured?: boolean;
  isPremium?: boolean;
  geoRadius?: {
    center: { lat: number; lon: number };
    radius: number; // en metros
  };
}

export interface SearchResult {
  id: string;
  score: number;
  payload: PropertyPayload;
}

export interface SearchWithIntentResult {
  results: SearchResult[];
  intentResult?: IntentDetectionResult;
}

// Nuevos tipos para el sistema de pesos semánticos
export interface SemanticWeightedSearchResult {
  results: SearchResult[];
  decomposition: DecompositionResult;
  weightProfile: string;
  weights: SemanticWeights;
  embeddingStats: {
    successRate: number;
    averageProcessingTime: number;
    primaryComponent: string;
    hasLocation: boolean;
  };
  searchStats: {
    totalResults: number;
    uniqueProperties: number;
    averageScore: number;
    topScore: number;
  };
  componentResults: Array<{
    component: string;
    resultCount: number;
    weight: number;
    processingTime: number;
  }>;
  processingTime: number;
  breakdown?: {
    normalization: any;
    decomposition: any;
    embeddings: any;
    search: any;
    ranking: any;
  };
}

// Nuevos tipos para el sistema de precisión semántica avanzada (Día 7)
export interface AdvancedPrecisionSearchResult extends SemanticWeightedSearchResult {
  precisionMetrics: {
    originalResultsCount: number;
    filteredResultsCount: number;
    averageRelevanceImprovement: number;
    filteringStats: FilteringStats;
    topResultConfidence: number;
    precisionScore: number;
  };
  relevanceAnalyses: RelevanceAnalysisResult[];
  rankingOptimizations: OptimizedRankingResult[];
  advancedProcessingTime: number;
}
