/**
 * Context Analyzer - Analizador de Contexto Avanzado
 * 
 * Sistema que analiza el contexto implícito en consultas inmobiliarias
 * para mejorar la comprensión y precisión de búsquedas complejas.
 * 
 * Día 6 - Análisis de Contexto Avanzado
 */

import OpenAI from 'openai';

// Tipos para análisis de contexto
export interface ContextAnalysis {
  familyContext: {
    isFamilyOriented: boolean;
    hasChildren: boolean;
    needsSchools: boolean;
    confidence: number;
  };
  economicContext: {
    budgetLevel: 'low' | 'medium' | 'high' | 'luxury';
    priceRange: {
      min: number;
      max: number;
      currency: string;
    };
    confidence: number;
  };
  urgencyContext: {
    urgencyLevel: 'low' | 'medium' | 'high' | 'immediate';
    timeframe: string;
    confidence: number;
  };
  locationContext: {
    preferredZones: string[];
    avoidZones: string[];
    proximityNeeds: string[];
    confidence: number;
  };
  lifestyleContext: {
    workFromHome: boolean;
    socialLife: boolean;
    quietPreference: boolean;
    modernPreference: boolean;
    confidence: number;
  };
  guatemalanContext: {
    localTermsUsed: string[];
    culturalPreferences: string[];
    regionalKnowledge: boolean;
    confidence: number;
  };
}

export interface ContextEnhancedQuery {
  originalQuery: string;
  enhancedQuery: string;
  contextAnalysis: ContextAnalysis;
  suggestedFilters: {
    priceRange?: { min: number; max: number };
    zones?: string[];
    amenities?: string[];
    propertyTypes?: string[];
  };
  confidence: number;
  processingTime: number;
}

/**
 * Analizador de contexto avanzado para consultas inmobiliarias
 */
export class ContextAnalyzer {
  private openai: OpenAI;
  private model: string;

  // Conocimiento específico de Guatemala
  private readonly GUATEMALA_ZONES = [
    'Zona 1', 'Zona 2', 'Zona 3', 'Zona 4', 'Zona 5', 'Zona 6', 'Zona 7', 'Zona 8', 'Zona 9', 'Zona 10',
    'Zona 11', 'Zona 12', 'Zona 13', 'Zona 14', 'Zona 15', 'Zona 16', 'Zona 17', 'Zona 18', 'Zona 19', 'Zona 20',
    'Zona 21', 'Zona 22', 'Zona 23', 'Zona 24', 'Zona 25'
  ];

  private readonly GUATEMALA_AREAS = [
    'Antigua Guatemala', 'Mixco', 'Villa Nueva', 'San José Pinula', 'Santa Catarina Pinula',
    'Carretera a El Salvador', 'Carretera al Pacífico', 'Fraijanes', 'Amatitlán'
  ];

  private readonly LOCAL_TERMS = {
    'apto': 'apartamento',
    'depto': 'departamento',
    'casa': 'casa',
    'terreno': 'terreno',
    'local': 'local comercial',
    'oficina': 'oficina',
    'bodega': 'bodega',
    'z': 'zona',
    'hab': 'habitaciones',
    'baños': 'baños',
    'mt2': 'metros cuadrados',
    'm2': 'metros cuadrados',
    'parqueo': 'estacionamiento',
    'cochera': 'garaje'
  };

  private readonly PRICE_RANGES_BY_ZONE = {
    'Zona 10': { min: 800000, max: 5000000 },
    'Zona 14': { min: 600000, max: 3000000 },
    'Zona 15': { min: 400000, max: 2000000 },
    'Zona 16': { min: 300000, max: 1500000 },
    'Zona 9': { min: 200000, max: 1000000 },
    'Zona 13': { min: 250000, max: 1200000 },
    'Antigua Guatemala': { min: 300000, max: 2000000 }
  };

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
    this.model = process.env.AI_MODEL_CONTEXT_ANALYSIS || 'gpt-4o-mini';
  }

  /**
   * Analiza el contexto de una consulta inmobiliaria
   */
  async analyzeContext(query: string, userHistory?: string[]): Promise<ContextEnhancedQuery> {
    const startTime = Date.now();

    try {
      console.log(`🔍 Analizando contexto: "${query}"`);

      // Realizar análisis de contexto con OpenAI
      const contextAnalysis = await this.performContextAnalysis(query, userHistory);

      // Generar consulta mejorada
      const enhancedQuery = await this.generateEnhancedQuery(query, contextAnalysis);

      // Generar filtros sugeridos
      const suggestedFilters = this.generateSuggestedFilters(contextAnalysis);

      // Calcular confianza general
      const confidence = this.calculateOverallConfidence(contextAnalysis);

      const result: ContextEnhancedQuery = {
        originalQuery: query,
        enhancedQuery,
        contextAnalysis,
        suggestedFilters,
        confidence,
        processingTime: Date.now() - startTime,
      };

      console.log(`✅ Contexto analizado en ${result.processingTime}ms (confianza: ${confidence.toFixed(2)})`);
      return result;

    } catch (error) {
      console.error('Error en análisis de contexto:', error);
      return this.createFallbackResult(query, Date.now() - startTime);
    }
  }

  /**
   * Realiza análisis de contexto usando OpenAI
   */
  private async performContextAnalysis(query: string, userHistory?: string[]): Promise<ContextAnalysis> {
    const prompt = this.buildContextAnalysisPrompt(query, userHistory);

    const response = await this.openai.chat.completions.create({
      model: this.model,
      messages: [
        {
          role: 'system',
          content: 'Eres un experto en análisis de contexto inmobiliario en Guatemala. Analiza consultas para detectar contexto implícito, preferencias familiares, económicas y culturales.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.2,
      max_tokens: 1000,
    });

    const content = response.choices[0].message.content || '{}';
    return this.parseContextAnalysis(content);
  }

  /**
   * Construye el prompt para análisis de contexto
   */
  private buildContextAnalysisPrompt(query: string, userHistory?: string[]): string {
    let prompt = `
Analiza esta consulta inmobiliaria guatemalteca y detecta el CONTEXTO IMPLÍCITO:

CONSULTA: "${query}"
`;

    if (userHistory && userHistory.length > 0) {
      prompt += `
HISTORIAL PREVIO:
${userHistory.slice(-3).map((q, i) => `${i + 1}. "${q}"`).join('\n')}
`;
    }

    prompt += `
Detecta el contexto en estas dimensiones:

1. CONTEXTO FAMILIAR:
   - ¿Es para una familia? ¿Hay niños?
   - ¿Necesita cercanía a colegios?

2. CONTEXTO ECONÓMICO:
   - Nivel socioeconómico (bajo/medio/alto/lujo)
   - Rango de precio estimado en USD

3. CONTEXTO DE URGENCIA:
   - Nivel de urgencia (bajo/medio/alto/inmediato)
   - Marco temporal

4. CONTEXTO DE UBICACIÓN:
   - Zonas preferidas/evitadas
   - Necesidades de proximidad

5. CONTEXTO DE ESTILO DE VIDA:
   - ¿Trabajo desde casa?
   - ¿Vida social activa?
   - ¿Preferencia por tranquilidad?
   - ¿Preferencia por modernidad?

6. CONTEXTO GUATEMALTECO:
   - Términos locales usados
   - Conocimiento cultural/regional

Responde SOLO en JSON:
{
  "familyContext": {
    "isFamilyOriented": boolean,
    "hasChildren": boolean,
    "needsSchools": boolean,
    "confidence": 0.0-1.0
  },
  "economicContext": {
    "budgetLevel": "low|medium|high|luxury",
    "priceRange": {"min": number, "max": number, "currency": "USD"},
    "confidence": 0.0-1.0
  },
  "urgencyContext": {
    "urgencyLevel": "low|medium|high|immediate",
    "timeframe": "string",
    "confidence": 0.0-1.0
  },
  "locationContext": {
    "preferredZones": ["zona1", "zona2"],
    "avoidZones": ["zona3"],
    "proximityNeeds": ["colegios", "centros comerciales"],
    "confidence": 0.0-1.0
  },
  "lifestyleContext": {
    "workFromHome": boolean,
    "socialLife": boolean,
    "quietPreference": boolean,
    "modernPreference": boolean,
    "confidence": 0.0-1.0
  },
  "guatemalanContext": {
    "localTermsUsed": ["apto", "z14"],
    "culturalPreferences": ["seguridad", "zona comercial"],
    "regionalKnowledge": boolean,
    "confidence": 0.0-1.0
  }
}
`;

    return prompt;
  }

  /**
   * Parsea la respuesta del análisis de contexto
   */
  private parseContextAnalysis(content: string): ContextAnalysis {
    try {
      const cleanContent = content.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      const parsed = JSON.parse(cleanContent);

      return {
        familyContext: {
          isFamilyOriented: parsed.familyContext?.isFamilyOriented || false,
          hasChildren: parsed.familyContext?.hasChildren || false,
          needsSchools: parsed.familyContext?.needsSchools || false,
          confidence: parsed.familyContext?.confidence || 0.5,
        },
        economicContext: {
          budgetLevel: parsed.economicContext?.budgetLevel || 'medium',
          priceRange: {
            min: parsed.economicContext?.priceRange?.min || 200000,
            max: parsed.economicContext?.priceRange?.max || 1000000,
            currency: parsed.economicContext?.priceRange?.currency || 'USD',
          },
          confidence: parsed.economicContext?.confidence || 0.5,
        },
        urgencyContext: {
          urgencyLevel: parsed.urgencyContext?.urgencyLevel || 'medium',
          timeframe: parsed.urgencyContext?.timeframe || 'No especificado',
          confidence: parsed.urgencyContext?.confidence || 0.5,
        },
        locationContext: {
          preferredZones: parsed.locationContext?.preferredZones || [],
          avoidZones: parsed.locationContext?.avoidZones || [],
          proximityNeeds: parsed.locationContext?.proximityNeeds || [],
          confidence: parsed.locationContext?.confidence || 0.5,
        },
        lifestyleContext: {
          workFromHome: parsed.lifestyleContext?.workFromHome || false,
          socialLife: parsed.lifestyleContext?.socialLife || false,
          quietPreference: parsed.lifestyleContext?.quietPreference || false,
          modernPreference: parsed.lifestyleContext?.modernPreference || false,
          confidence: parsed.lifestyleContext?.confidence || 0.5,
        },
        guatemalanContext: {
          localTermsUsed: parsed.guatemalanContext?.localTermsUsed || [],
          culturalPreferences: parsed.guatemalanContext?.culturalPreferences || [],
          regionalKnowledge: parsed.guatemalanContext?.regionalKnowledge || false,
          confidence: parsed.guatemalanContext?.confidence || 0.5,
        },
      };

    } catch (error) {
      console.warn('Error parseando análisis de contexto:', error);
      return this.createDefaultContextAnalysis();
    }
  }

  /**
   * Genera consulta mejorada basada en el contexto
   */
  private async generateEnhancedQuery(query: string, context: ContextAnalysis): Promise<string> {
    // Si la confianza es baja, mantener consulta original
    const overallConfidence = this.calculateOverallConfidence(context);
    if (overallConfidence < 0.6) {
      return query;
    }

    let enhanced = query;

    // Agregar contexto familiar
    if (context.familyContext.isFamilyOriented && context.familyContext.confidence > 0.7) {
      if (context.familyContext.needsSchools) {
        enhanced += ' cerca de colegios';
      }
    }

    // Agregar contexto de ubicación
    if (context.locationContext.preferredZones.length > 0 && context.locationContext.confidence > 0.7) {
      const zones = context.locationContext.preferredZones.join(' o ');
      if (!query.toLowerCase().includes('zona')) {
        enhanced += ` en ${zones}`;
      }
    }

    // Agregar contexto de estilo de vida
    if (context.lifestyleContext.quietPreference && context.lifestyleContext.confidence > 0.7) {
      enhanced += ' tranquilo';
    }

    if (context.lifestyleContext.modernPreference && context.lifestyleContext.confidence > 0.7) {
      enhanced += ' moderno';
    }

    return enhanced;
  }

  /**
   * Genera filtros sugeridos basados en el contexto
   */
  private generateSuggestedFilters(context: ContextAnalysis): any {
    const filters: any = {};

    // Filtro de precio
    if (context.economicContext.confidence > 0.6) {
      filters.priceRange = {
        min: context.economicContext.priceRange.min,
        max: context.economicContext.priceRange.max,
      };
    }

    // Filtro de zonas
    if (context.locationContext.preferredZones.length > 0 && context.locationContext.confidence > 0.6) {
      filters.zones = context.locationContext.preferredZones;
    }

    // Filtro de amenidades
    const amenities: string[] = [];
    if (context.familyContext.needsSchools && context.familyContext.confidence > 0.7) {
      amenities.push('Cerca Colegios');
    }
    if (context.lifestyleContext.workFromHome && context.lifestyleContext.confidence > 0.7) {
      amenities.push('Wi-Fi', 'Oficina');
    }
    if (amenities.length > 0) {
      filters.amenities = amenities;
    }

    return filters;
  }

  /**
   * Calcula confianza general del análisis
   */
  private calculateOverallConfidence(context: ContextAnalysis): number {
    const confidences = [
      context.familyContext.confidence,
      context.economicContext.confidence,
      context.urgencyContext.confidence,
      context.locationContext.confidence,
      context.lifestyleContext.confidence,
      context.guatemalanContext.confidence,
    ];

    return confidences.reduce((sum, conf) => sum + conf, 0) / confidences.length;
  }

  /**
   * Crea análisis de contexto por defecto
   */
  private createDefaultContextAnalysis(): ContextAnalysis {
    return {
      familyContext: { isFamilyOriented: false, hasChildren: false, needsSchools: false, confidence: 0.1 },
      economicContext: { budgetLevel: 'medium', priceRange: { min: 200000, max: 1000000, currency: 'USD' }, confidence: 0.1 },
      urgencyContext: { urgencyLevel: 'medium', timeframe: 'No especificado', confidence: 0.1 },
      locationContext: { preferredZones: [], avoidZones: [], proximityNeeds: [], confidence: 0.1 },
      lifestyleContext: { workFromHome: false, socialLife: false, quietPreference: false, modernPreference: false, confidence: 0.1 },
      guatemalanContext: { localTermsUsed: [], culturalPreferences: [], regionalKnowledge: false, confidence: 0.1 },
    };
  }

  /**
   * Crea resultado de fallback
   */
  private createFallbackResult(query: string, processingTime: number): ContextEnhancedQuery {
    return {
      originalQuery: query,
      enhancedQuery: query,
      contextAnalysis: this.createDefaultContextAnalysis(),
      suggestedFilters: {},
      confidence: 0.1,
      processingTime,
    };
  }
}
