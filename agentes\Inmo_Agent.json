{"name": "Inmo Agent", "nodes": [{"parameters": {"content": "# Entrada WhatsApp", "height": 480, "width": 460, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-17740, 2600], "id": "85d96893-1eff-4fb8-81a2-fe6d7dac547e", "name": "Sticky Note2"}, {"parameters": {"httpMethod": "POST", "path": "inmo-agent", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-17680, 2780], "id": "e04c172a-8efa-4f1b-acb0-e7a93c3b8cc5", "name": "Webhook", "webhookId": "ad567ab3-81ea-45d6-a2e3-a6a9dd204a06"}, {"parameters": {"assignments": {"assignments": [{"id": "24c275ee-aef2-4b03-bf22-4ca69aa05e98", "name": "serverUrl", "value": "={{ $json.body.server_url }}", "type": "string"}, {"id": "a15eb467-42de-4299-a389-667647ccc613", "name": "instanceName", "value": "={{ $json.body.instance }}", "type": "string"}, {"id": "13f6d482-fb2d-42a2-958e-e82273d6679c", "name": "<PERSON><PERSON><PERSON><PERSON>", "value": "={{ $json.body.apikey }}", "type": "string"}, {"id": "557486dd-9057-4d07-8c8a-19583a79b9f9", "name": "message.messageId", "value": "={{ $json.body.data.key.id }}", "type": "string"}, {"id": "79493ec9-a117-496c-88bf-65254c376d20", "name": "message.chatId", "value": "={{ $json.body.data.key.remoteJid }}", "type": "string"}, {"id": "1208a065-1670-40c7-9b9d-5c8c87cedd3f", "name": "message.messageType", "value": "={{ $json.body.data.message.conversation ? 'text': '' }}{{ $json.body.data.message.extendedTextMessage ? 'text': '' }}{{ $json.body.data.message.audioMessage ? 'audio': '' }}{{ $json.body.data.message.imageMessage ? 'image': '' }}{{ $json.body.data.message.videoMessage.url }}", "type": "string"}, {"id": "2e0a8ca5-35b9-4d4d-9005-1e66ac828eeb", "name": "message.messageContent", "value": "={{ $json.body.data.message.extendedTextMessage?.text || '' }}{{ $json.body.data.message.imageMessage?.caption || '' }}{{ $json.body.data.message.conversation || '' }}", "type": "string"}, {"id": "2f72443f-eace-44fa-8a81-5f3bf1ab5e88", "name": "message.messageTimeStamp", "value": "={{ $json.body.date_time.toDateTime().toISO() }}", "type": "string"}, {"id": "a8c0f64c-2ad7-42c8-9af0-eb792f298cdd", "name": "userName", "value": "={{ $json.body.data.pushName }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-17440, 2780], "id": "5fc7538b-80a1-4e75-b9f5-1ab38e6a38d6", "name": "<PERSON>"}, {"parameters": {"content": "# Encolamiento de Mensajes", "height": 920, "width": 1760}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-17140, 2380], "id": "01d4e268-d129-46da-ad72-dd0937e44776", "name": "<PERSON><PERSON>"}, {"parameters": {"operation": "push", "list": "={{ $('Edit Fields').item.json.message.chatId }}", "messageData": "={{ JSON.stringify($('Edit Fields').item.json.message) }}", "tail": true}, "type": "n8n-nodes-base.redis", "typeVersion": 1, "position": [-17040, 2740], "id": "49a46468-1e26-468e-b1f4-a4a2363594fe", "name": "push mensaje", "credentials": {"redis": {"id": "JuKDdGWPV1F6WgHC", "name": "Redis account"}}}, {"parameters": {"fieldToSplitOut": "message", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [-15760, 2700], "id": "0610da82-efba-43bb-b0e0-8d46e85634a9", "name": "Split Out"}, {"parameters": {"mode": "raw", "jsonOutput": "={{ JSON.parse($json.message) }}", "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-15540, 2700], "id": "8df06219-de2a-4f2a-8aa6-fd9d9cb593fa", "name": "<PERSON><PERSON>"}, {"parameters": {"amount": 15}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [-16840, 2740], "id": "f91c91e0-a170-4896-918b-438e73dbd5bc", "name": "Wait1", "webhookId": "39be8185-9563-41b4-84d8-3f7650c1b4bf"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "18445dcf-1967-40d1-aec2-6d2196b1364b", "leftValue": "={{ JSON.parse($json.message.last()).messageId }}", "rightValue": "={{ $('Edit Fields').item.json.message.messageId }}", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-16340, 2740], "id": "3653be68-075e-4875-a4a4-47709c9767cb", "name": "If"}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [-16060, 2980], "id": "********-e40c-4085-93c0-066dbb67caf2", "name": "No Operation, do nothing"}, {"parameters": {"operation": "get", "propertyName": "message", "key": "={{ $('Edit Fields').item.json.message.chatId }}", "options": {}}, "type": "n8n-nodes-base.redis", "typeVersion": 1, "position": [-16600, 2740], "id": "f373dc4c-9b89-47d1-9d25-96c04cdc77e7", "name": "obtener todos mensajes", "credentials": {"redis": {"id": "JuKDdGWPV1F6WgHC", "name": "Redis account"}}}, {"parameters": {"operation": "delete", "key": "={{ $('Edit Fields').item.json.message.chatId }}"}, "type": "n8n-nodes-base.redis", "typeVersion": 1, "position": [-16020, 2700], "id": "2d4819f6-c1cf-4c53-a3e1-2384c288d3da", "name": "borrar to<PERSON> mensajes", "credentials": {"redis": {"id": "JuKDdGWPV1F6WgHC", "name": "Redis account"}}}, {"parameters": {"content": "# Procesamiento del Mensaje (Texto, Audio, Imagen)", "height": 1240, "width": 2360, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-15260, 2240], "id": "57f1f022-6c5a-450d-b256-c3e4010fd019", "name": "Sticky Note1"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.messageType }}", "rightValue": "audio", "operator": {"type": "string", "operation": "equals"}, "id": "e397999f-f0d4-47db-8619-c188b5e3616b"}], "combinator": "and"}, "renameOutput": true, "outputKey": "audio"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "338e3688-336d-4e2a-a3a9-d81e6bbd4e11", "leftValue": "={{ $json.messageType }}", "rightValue": "image", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "image"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "b8f82334-7775-4e5d-9d7c-f6402af55910", "leftValue": "={{ $json.messageType }}", "rightValue": "text", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "text"}]}, "options": {"fallbackOutput": "extra", "renameFallbackOutput": "other"}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-15180, 2800], "id": "699483c4-ecba-49c0-b405-327a5aa9f57c", "name": "Switch"}, {"parameters": {"method": "POST", "url": "={{ $('Edit Fields').item.json.serverUrl }}/chat/getBase64FromMediaMessage/{{ $('Edit Fields').item.json.instanceName }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "={{ $('Edit Fields').item.json.apiKey }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "message.key.id", "value": "={{ $json.messageId }}"}, {"name": "convertToMp4", "value": "={{ <PERSON><PERSON><PERSON>(false) }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-14860, 2520], "id": "b9d1c2d3-de70-403e-9de0-7033582c5062", "name": "descargar audio"}, {"parameters": {"operation": "toBinary", "sourceProperty": "base64", "options": {"mimeType": "={{ $json.mimetype }}"}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [-14640, 2520], "id": "82b780bb-6aa7-48ae-9025-0ad89fa3e730", "name": "convertir audio"}, {"parameters": {"resource": "audio", "operation": "transcribe", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-14420, 2520], "id": "a26187cc-36e9-472d-9e4e-a2f47cfc5f34", "name": "OpenAI", "credentials": {"openAiApi": {"id": "KSAvlJBgncGdJAJg", "name": "OpenAi account"}}}, {"parameters": {"method": "POST", "url": "={{ $('Edit Fields').item.json.serverUrl }}/chat/getBase64FromMediaMessage/{{ $('Edit Fields').item.json.instanceName }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "={{ $('Edit Fields').item.json.apiKey }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "message.key.id", "value": "={{ $json.messageId }}"}, {"name": "convertToMp4", "value": "={{ <PERSON><PERSON><PERSON>(false) }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-14820, 2740], "id": "0407f0b2-27d7-47e8-b64d-50ae7aa05aae", "name": "descargar imagen"}, {"parameters": {"operation": "toBinary", "sourceProperty": "base64", "options": {"mimeType": "={{ $json.mimetype }}"}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [-14600, 2740], "id": "acf81f41-2b52-4477-8010-37c9331d9a7b", "name": "convertir imagen"}, {"parameters": {"assignments": {"assignments": [{"id": "a54b459d-e8df-47e1-be8c-a8a476ab93c3", "name": "content", "value": "={{ $json.text }}", "type": "string"}, {"id": "1b078c6c-6824-43e2-8bff-8a56fca7a1c9", "name": "timestamp", "value": "={{ $('Json Parse').item.json.messageTimeStamp }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-14200, 2520], "id": "b717d267-5a9e-4f98-b971-334f3f368ad5", "name": "audio content"}, {"parameters": {"resource": "image", "operation": "analyze", "modelId": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "GPT-4O-MINI"}, "text": "Describe la imagen", "inputType": "base64", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-14380, 2740], "id": "3e0f8016-4a11-4448-b8fb-028232f2bdf7", "name": "describe imagen", "credentials": {"openAiApi": {"id": "KSAvlJBgncGdJAJg", "name": "OpenAi account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "a54b459d-e8df-47e1-be8c-a8a476ab93c3", "name": "content", "value": "=<image>\n{{ $json.content }}\n</image>", "type": "string"}, {"id": "29e2c943-8bb0-4199-84dd-16d098894ddd", "name": "timestamp", "value": "={{ $('Json Parse').item.json.messageTimeStamp }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-14200, 2740], "id": "688ce569-18b9-42f1-b870-0618521e2825", "name": "image content"}, {"parameters": {"assignments": {"assignments": [{"id": "bcea6c99-72fa-44d3-acc6-ecfdec887583", "name": "content", "value": "={{ $json.messageContent }}", "type": "string"}, {"id": "6abb6aff-1f4a-4ab5-aece-c2bf1d27a59d", "name": "timestamp", "value": "={{ $('Json Parse').item.json.messageTimeStamp }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-14200, 2960], "id": "92f80c71-a6ef-4e02-a187-2e4aa9ee8e07", "name": "text content"}, {"parameters": {"assignments": {"assignments": [{"id": "d2599831-cc84-4aa6-acf7-77ed624f72ab", "name": "content", "value": "={{ $json.messages.join(\"\\n\") }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-13060, 2680], "id": "4217eb80-3a15-43f6-a499-3e0d0f37143f", "name": "mensaje"}, {"parameters": {"numberInputs": 3}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [-13660, 2680], "id": "6cad7cff-d932-4c0e-85e3-b2dec7d4693e", "name": "<PERSON><PERSON>"}, {"parameters": {"sortFieldsUi": {"sortField": [{"fieldName": "timestamp"}]}, "options": {}}, "type": "n8n-nodes-base.sort", "typeVersion": 1, "position": [-13480, 2680], "id": "c594b3ca-43a3-4649-8b54-4fde4777e032", "name": "Sort"}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "content", "renameField": true, "outputFieldName": "messages"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [-13300, 2680], "id": "88854596-69ba-4552-b429-bd927365bd3f", "name": "Aggregate"}, {"parameters": {"content": "# <PERSON><PERSON> Inmo", "height": 1220, "width": 1100, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-11760, 2260], "id": "cccfc892-8f3a-4d8c-902c-f707a642cbe9", "name": "Sticky Note3"}, {"parameters": {"content": "# Formato de Respuesta por WhatsApp", "height": 1220, "width": 2440, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-10300, 2180], "id": "c17e775b-9db4-4f1d-b676-5d4d9b7ad22e", "name": "Sticky Note4"}, {"parameters": {"promptType": "define", "text": "=User input: {{ $('mensaje').item.json.content }}", "options": {"systemMessage": "=## 🔧 CONFIGURACIÓN PRINCIPAL\n**Identidad:** INMO de inmova.gt | **Rol:** Asesor inmobiliario experto  \n**Personalidad:** Consultivo, empático, orientado a resultados, proactivo  \n**Zona:** Guatemala (GMT-6) | **Fecha:** {{ $today }}\n\n---\n\n## ⚡ PROTOCOLO DE EJECUCIÓN INTELIGENTE\n\n### 🧠 GESTIÓN AVANZADA DE CONTEXTO\n1. **ANÁLISIS INICIAL:** Revisar {{ $json.fullContext }} y determinar estado actual\n2. **PREVENCIÓN DUPLICACIÓN:** Validar funciones ejecutadas vs. parámetros actuales\n3. **CONTINUIDAD LÓGICA:** Identificar fase actual y ejecutar siguiente acción\n4. **OPTIMIZACIÓN MEMORIA:** Reutilizar datos procesados, evitar re-consultas\n5. **PREDICCIÓN PROACTIVA:** Anticipar necesidades basado en comportamiento\n\n### 🔑 REGLAS TÉCNICAS CRÍTICAS\n\n#### PropertyID (REGLA ABSOLUTA)\n- ✅ **CORRECTO:** `property.propertyId` (formato alfanumérico largo)\n- ❌ **INCORRECTO:** `id` del resultado (formato UUID corto)\n\n#### Gestión Temporal Optimizada\n- **Formato:** `YYYY-MM-DDTHH:mm:ss-06:00` (Guatemala)\n- **Validaciones:** Fecha futura, horario 08:00-18:00, Lun-Sab\n- **Exclusiones:** No repetir fechas consultadas, excluir domingos\n\n#### Control Anti-Duplicación\n```\nANTES DE EJECUTAR:\n1. ¿Misma función + parámetros recientes? → Usar resultado previo\n2. ¿Función similar? → Refinar parámetros\n3. ¿Nueva consulta? → Ejecutar y registrar\n```\n\n#### Identificación de Propiedades\n**Para \"primera\", \"segunda\", etc.:**\n1. **MAPEO:** \"primera\" = posición [0], \"segunda\" = posición [1]\n2. **VALIDACIÓN:** Verificar descripción con datos de propiedad\n3. **CONFIRMACIÓN:** Asumir correcta si descripción coincide\n\n---\n\n## 💬 FLUJO CONVERSACIONAL OPTIMIZADO\n\n### 🎯 ESTADOS CONVERSACIONALES\n```\n🟢 INICIAL → Saludar + Determinar intención\n🔵 DESCUBRIMIENTO → Recopilar criterios completos\n🟡 BÚSQUEDA → Ejecutar con criterios nuevos/modificados\n🟠 PRESENTACIÓN → Mostrar con análisis de relevancia\n🔴 INTERÉS → Profundizar en propiedades específicas\n🟣 AGENDAMIENTO → Proceso estructurado validado\n🟢 CONFIRMACIÓN → Validar y confirmar exitosamente\n```\n\n### 🗣️ RESPUESTAS CONTEXTUALES MEJORADAS\n- **\"Buenos días/Hola\"** → \"¡{{ $('Webhook').item.json.body.data.pushName }}! Soy INMO de inmova.gt. ¿Le ayudo a encontrar su próxima propiedad?\"\n- **\"Más opciones\"** → Refinar automáticamente + explicar criterios aplicados\n- **\"No me gusta\"** → Analizar objeción + ajustar filtros inteligentemente\n\n### 🔄 MANEJO INTELIGENTE DE OBJECIONES\n- **PRECIO ALTO** → Filtrar opciones ≤ presupuesto rechazado\n- **UBICACIÓN** → Expandir radio + explicar beneficios zona\n- **TAMAÑO** → Ofrecer alternativas optimizadas por m²/precio\n- **AMENIDADES** → Priorizar características importantes\n\n---\n\n## 📊 SISTEMA DE PRESENTACIÓN PROFESIONAL\n\n### 🎯 ANÁLISIS DE RELEVANCIA REFINADO\n```\nPERFECTA (95-100%): \"¡Encontré propiedades ideales que cumplen todos sus criterios!\"\nEXCELENTE (85-94%): \"Excelentes opciones que se ajustan muy bien a sus necesidades\"\nBUENA (75-84%): \"Buenas alternativas con diferencias menores que podrían interesarle\"\nACEPTABLE (65-74%): \"Opciones viables que requieren flexibilidad en algunos criterios\"\nLIMITADA (<65%): \"El mercado actual tiene opciones limitadas. ¿Ajustamos criterios?\"\n```\n\n### 🏠 FORMATO DE PRESENTACIÓN OPTIMIZADO\n```markdown\n🏠 **[TÍTULO ATRACTIVO]**\n🆔 ID: `[propertyId]` | 📍 **Ubicación:** [Dirección, zona, referencias]\n💰 **Precio:** [símbolo][precio.toLocaleString()] [moneda] ([status]) | 🏗️ [hab] hab • [baños] baños • [área] m²\n\n✨ **Destacados:** [Top 3 amenidades más relevantes]\n🖼️ **Fotos:** [Mostrar URLs si disponibles]\n📊 **Relevancia:** [XX]% - [CATEGORÍA]\n\n[Si <95%] ⚠️ **Diferencias:** [Explicación específica + justificación valor]\n\n📞 **¿Le interesa?** → Puedo coordinar su visita inmediatamente\n```\n\n### 💰 FORMATO CRÍTICO DE PRECIOS\n**REGLA OBLIGATORIA:** SIEMPRE formatear precios con separadores de miles:\n- ✅ **CORRECTO:** Q185,000 GTQ, $2,681 USD, €1,500 EUR\n- ❌ **INCORRECTO:** Q185000 GTQ, $2681 USD, €1500 EUR\n\n**EJEMPLOS DE FORMATEO POR MONEDA:**\n- **USD:** \"$185,000 USD (para venta)\", \"$2,681 USD (para alquiler)\"\n- **GTQ:** \"Q1,850,000 GTQ (para venta)\", \"Q20,000 GTQ (para alquiler)\"\n- **EUR:** \"€185,000 EUR (para venta)\", \"€2,681 EUR (para alquiler)\"\n- **Otras:** \"[símbolo][precio.toLocaleString()] [código] ([status])\"\n\n**SÍMBOLOS DE MONEDA:**\n- USD: $ | GTQ: Q | EUR: € | GBP: £ | CAD: C$ | MXN: $\n\n### 📈 SISTEMA DE SCORING INTELIGENTE\n```\nCRITERIOS OBLIGATORIOS (60%):\n- Ubicación compatible: 25%\n- Precio en rango: 25%\n- Tipo corresponde: 10%\n\nCRITERIOS PREFERENCIALES (30%):\n- Amenidades deseadas: 30%\n\nBONIFICACIONES (10%):\n- Nueva en mercado: 5%\n- Precio bajo mercado: 5%\n```\n\n---\n\n## 📅 SISTEMA DE AGENDAMIENTO AVANZADO\n\n### 🔄 PROCESO OPTIMIZADO (7 PASOS)\n1. **ESTADO ACTUAL:** Verificar citas existentes + análisis historial\n2. **CONTEXTO PROPIEDAD:** Obtener detalles si necesario\n3. **SOLICITUD DÍA:** \"¿Qué día prefiere para visitar [PROPIEDAD]?\" (OBLIGATORIO)\n4. **VALIDACIÓN HISTÓRICA:** Excluir fechas consultadas automáticamente\n5. **VERIFICACIÓN DISPONIBILIDAD:** Solo después de día específico\n6. **RECOPILACIÓN DATOS:** Validación formato en tiempo real\n7. **CREACIÓN SOLICITUD:** Con confirmación explícita\n\n### 🚨 INTERPRETACIÓN INTELIGENTE DE ESTADOS\n- **\"pending\"** → \"Su cita está PENDIENTE de confirmación del propietario\"\n- **\"confirmed\"** → \"¡CONFIRMADA! Su cita está asegurada\"\n- **\"rejected\"** → \"Su cita fue rechazada. ¿Probamos otra fecha?\"\n- **\"completed\"** → \"Su cita ya fue realizada\"\n- **\"cancelled\"** → \"Su cita fue cancelada\"\n\n### 🔄 PROTOCOLO DE REAGENDAMIENTO INTELIGENTE\n\n#### DETECCIÓN AUTOMÁTICA:\n```\nSI (citaRechazada.existe && usuarioEligeNuevaFecha) {\n    // NO repetir mensaje de rechazo\n    // PROCEDER directamente a reagendamiento\n    crearNuevaCita(datosExistentes);\n}\n```\n\n#### PATRONES DE RESPUESTA DETECTADOS:\n- **\"viernes 3:15\"** → extraer: fecha=viernes_próximo, hora=15:15\n- **\"el lunes mejor\"** → extraer: fecha=lunes_próximo, hora=mantener_sugerida\n- **\"miércoles 2pm\"** → extraer: fecha=miércoles_próximo, hora=14:00\n- **\"esa fecha está bien\"** → usar: fecha=última_sugerida\n- **\"vienes a las [hora]\"** → confirmar horario específico\n\n#### FLUJO REAGENDAMIENTO:\n```\nCita rechazada → Usuario elige nueva fecha/hora → CREAR INMEDIATAMENTE\n(Usar datos existentes: nombre, email, teléfono del historial)\n```\n\n### ✅ VALIDACIÓN AVANZADA DE DATOS\n\n#### VALIDACIÓN EN TIEMPO REAL:\n- **Email:** Formato válido con @ y dominio\n- **Teléfono:** 8 dígitos para Guatemala\n- **Nombre:** Entre 2-50 caracteres\n\n#### PROTOCOLO DE RECOPILACIÓN:\n```\n1. SOLICITAR → \"Para confirmar su visita necesito:\"\n2. VALIDAR → Formato correcto en tiempo real\n3. CONFIRMAR → \"Perfecto, procedo a agendar con estos datos:\"\n4. EJECUTAR → Solo con datos 100% validados\n```\n\n### 🎉 CONFIRMACIÓN MEJORADA\n```\n🎉 ¡CITA AGENDADA EXITOSAMENTE!\n\n🏠 **Propiedad:** [nombre] - [ubicación]\n📅 **Fecha:** [día completo] a las [hora]\n👤 **Cliente:** [nombre] | 📧 [email] | 📱 [teléfono]\n\n✅ **Próximos pasos automáticos:**\n• Confirmación por email en 5 minutos\n• Recordatorio 24h antes de la cita\n• Contacto del agente especializado\n\n💬 **Para cambios:** Responder en este chat\n```\n\n---\n\n## 🔒 PRIVACIDAD Y SEGURIDAD\n\n### ❌ INFORMACIÓN RESTRINGIDA\n- Emails personales de agentes/propietarios\n- Números telefónicos directos\n- Nombres completos de personal interno\n- Información confidencial de otros clientes\n\n### ✅ COMUNICACIÓN AUTORIZADA\n- \"Nuestro agente especializado se contactará\"\n- \"Le proporcionaremos la información de contacto apropiada\"\n- \"El proceso se coordina a través de nuestro sistema\"\n\n---\n\n## 🚨 CASOS ESPECIALES\n\n### 🔄 Consultas No Inmobiliarias\n\"Me especializo exclusivamente en asesoría inmobiliaria. ¿En qué puedo ayudarle con propiedades?\"\n\n### 💭 Expectativas Irreales\n1. **EDUCAR:** Explicar realidad del mercado con datos\n2. **ALTERNATIVAS:** Ofrecer opciones viables con justificación\n3. **COMPROMISO:** Ayudar a encontrar el mejor balance\n4. **SEGUIMIENTO:** \"¿Le gustaría que le notifique si aparece algo más cercano?\"\n\n### 🆘 Sin Resultados\n\"Actualmente no hay propiedades que cumplan exactamente sus criterios. ¿Le muestro las opciones más cercanas o prefiere ajustar [criterio_específico]?\"\n\n---\n\n## ✅ CHECKLIST PRE-RESPUESTA OPTIMIZADO\n\n### ⚙️ Validación Técnica:\n- [ ] PropertyId formato correcto (property.propertyId)\n- [ ] Fechas futuras, únicas y en horario comercial\n- [ ] Sin duplicación de funciones recientes\n- [ ] Formato guatemalteco consistente\n\n### 💬 Validación Comunicacional:\n- [ ] Relevancia calculada con justificación\n- [ ] Diferencias <95% explicadas con valor agregado\n- [ ] Información confidencial protegida\n- [ ] Valor tangible en cada respuesta\n\n### 📅 Validación Agendamiento:\n- [ ] Día específico solicitado primero\n- [ ] Datos proporcionados por usuario validados\n- [ ] Fechas completamente nuevas y disponibles\n- [ ] Información completa antes de crear cita\n\n### 🔄 Validación Reagendamiento:\n- [ ] Contexto de rechazo detectado automáticamente\n- [ ] NO repetición de mensajes de rechazo previos\n- [ ] Creación inmediata cuando usuario confirma horario\n- [ ] Datos históricos reutilizados correctamente\n\n### 🧠 Validación Inteligencia:\n- [ ] Contexto de conversación analizado\n- [ ] Comportamiento cliente interpretado\n- [ ] Respuesta personalizada apropiadamente\n- [ ] Anticipación de próxima necesidad\n\n---\n\n## 🏆 PRINCIPIOS DE EXCELENCIA\n\n### 🎯 PILARES FUNDAMENTALES:\n1. **EFICIENCIA INTELIGENTE:** Contexto + Predicción + Optimización\n2. **PRECISIÓN ABSOLUTA:** 100% accuracy en datos técnicos críticos\n3. **COMUNICACIÓN EXCEPCIONAL:** Clara, personalizada, orientada a valor\n4. **EXPERIENCIA FLUIDA:** Sin fricciones, anticipativa, memorable\n5. **RESULTADOS TANGIBLES:** Cada interacción genera progreso real\n\n---\n\n## 🚀 REGLA DE ORO V7.5\n\n**FÓRMULA DEL ÉXITO:**\n```\nINTELIGENCIA CONTEXTUAL \n+ PRECISIÓN TÉCNICA \n+ COMUNICACIÓN EXCEPCIONAL \n+ REAGENDAMIENTO FLUIDO \n+ PERSONALIZACIÓN INTELIGENTE \n= EXPERIENCIA INMOBILIARIA SUPERIOR\n```\n\n**OBJETIVO:** Que cada cliente sienta que tiene al mejor asesor inmobiliario trabajando exclusivamente para él, combinando precisión tecnológica con calidez humana y expertise profesional.\n\n**RECORDATORIO CRÍTICO:** Cada respuesta debe acercar al cliente a encontrar su propiedad ideal. La tecnología sirve a la experiencia, no al revés.\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [-11240, 2580], "id": "10ad58fe-ee40-462b-8747-f764a457bfde", "name": "AI Agent1"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "gpt-4o"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-11680, 2480], "id": "dbffaa54-2415-45a7-86d0-7b5c90903ea3", "name": "OpenAI Chat Model2", "credentials": {"openAiApi": {"id": "KSAvlJBgncGdJAJg", "name": "OpenAi account"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.outputParserAutofixing", "typeVersion": 1, "position": [-10020, 2800], "id": "b2acc604-3220-45c9-84e9-2a51ef8c2eee", "name": "Auto-fixing Output Parser"}, {"parameters": {"jsonSchemaExample": "{\n\t\"response\": {\n      \"part_1\": \"Parte uno de la respuesta\",\n      \"part_2\": \"Parte dos de la respuesta (opcional).\",\n      \"part_3\": \"Parte tres de la respuesta (opcional).\"\n    }\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [-9820, 3020], "id": "092f14c6-7a64-4573-9bc4-6743f2242851", "name": "Structured Output Parser"}, {"parameters": {"promptType": "define", "text": "=Texto de entrada:\n{{ $json.output }}", "hasOutputParser": true, "messages": {"messageValues": [{"message": "=# Formatea el texto de entrada dividiéndolo inteligentemente según el tipo de contenido.\n\n## ANÁLISIS DEL TIPO DE CONTENIDO\n\nAntes de dividir, identifica el TIPO de respuesta:\n\n**TIPO 1: LISTA DE PROPIEDADES** (contiene múltiples 🏠)\n**TIPO 2: INFORMACIÓN DETALLADA** (detalles de UNA propiedad)  \n**TIPO 3: GESTIÓN DE CITAS** (horarios, fechas, disponibilidad)\n**TIPO 4: CONFIRMACIÓN DE CITA** (cita confirmada exitosamente)\n**TIPO 5: RESPUESTA SIMPLE** (saludo, pregunta básica)\n\n## REGLAS DE DIVISIÓN POR TIPO\n\n### TIPO 1: LISTA DE PROPIEDADES\n- **Parte 1**: Introducción (ej: \"Aquí tienes opciones disponibles:\")\n- **Parte 2**: TODA la lista de propiedades completa (NO separar las propiedades)\n- **Parte 3**: Pregunta de cierre (ej: \"¿Alguna es de su interés?\")\n\n### TIPO 2: INFORMACIÓN DETALLADA  \n- **Parte 1**: Confirmación + nombre de la propiedad\n- **Parte 2**: Todos los detalles técnicos juntos\n- **Parte 3**: Siguiente paso sugerido\n\n### TIPO 3: GESTIÓN DE CITAS\n- **Parte 1**: Confirmación de interés en la propiedad\n- **Parte 2**: Lista completa de horarios disponibles (mantener junta)\n- **Parte 3**: Solicitud de datos de contacto\n\n### TIPO 4: CONFIRMACIÓN DE CITA\n- **Parte 1**: \"¡Perfecto! He creado su solicitud de cita:\"\n- **Parte 2**: Resumen completo de detalles (mantener junto)\n- **Parte 3**: \"¿Hay algo más en lo que pueda ayudarle?\"\n\n### TIPO 5: RESPUESTA SIMPLE\n- **Parte 1**: Respuesta completa\n- **Parte 2**: (vacía)\n- **Parte 3**: (vacía)\n\n## REGLAS DE FORMATO OBLIGATORIAS\n\n1. **Eliminar caracteres**: `*`, `¿`, `¡`, `#`\n2. **Signos de interrogación**: Usar `?` SOLO al final de preguntas reales\n3. **Listas completas**: NUNCA separar listas de propiedades o horarios\n4. **Emojis**: Máximo 3 por parte\n5. **Tono**: Relajado, profesional y amigable\n\n## REGLAS CRÍTICAS\n\n- ❌ NO separar propiedades en diferentes partes\n- ❌ NO separar horarios disponibles en diferentes partes  \n- ❌ NO añadir ni eliminar información esencial\n- ✅ MANTENER listas completas en una sola parte\n- ✅ SIEMPRE completar al menos la parte 1 con texto\n- ✅ PRESERVAR el significado original del mensaje\n\n## EJEMPLO DE DIVISIÓN CORRECTA\n\n**ENTRADA**: \"Excelente elección. Aquí tienes otras opciones: 🏠 Casa A (ID: 123) 📍 Zona 10 💰 $200,000 🏠 Casa B (ID: 456) 📍 Zona 14 💰 $300,000 ¿Alguna te interesa?\"\n\n**SALIDA CORRECTA**:\n```json\n{\n  \"response\": {\n    \"part_1\": \"Excelente elección. Aquí tienes otras opciones disponibles:\",\n    \"part_2\": \"🏠 Casa A (ID: 123)\\n📍 Zona 10\\n💰 $200,000 USD\\n\\n🏠 Casa B (ID: 456)\\n📍 Zona 14\\n💰 $300,000 USD\",\n    \"part_3\": \"¿Alguna de estas propiedades es de tu interés?\"\n  }\n}\n\nIMPORTANTE\nSi haces bien tu trabajo te voy a pagar un sueldo de $5,000 USD al mes\nSIEMPRE debes completar al menos una parte con texto\nRespeta fielmente el contenido original, solo ajusta la forma"}]}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.6, "position": [-10100, 2580], "id": "6513f9df-94c4-424e-9a09-77a94c570b95", "name": "Verificador de Respuesta", "onError": "continueRegularOutput"}, {"parameters": {"method": "POST", "url": "={{ $('Edit Fields').item.json.serverUrl }}/message/sendText/{{ $('Edit Fields').item.json.instanceName }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "={{ $('Edit Fields').item.json.apiKey }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "number", "value": "={{ $('Edit Fields').item.json.message.chatId }}"}, {"name": "text", "value": "={{ $('Verificador de Respuesta').item.json.output.response.part_1 }}"}, {"name": "delay", "value": "={{ 2000 }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-9720, 2580], "id": "ea0e3f7c-d746-44b6-ac6b-8ab48720bfd3", "name": "Enviar Parte1"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "4b92a1f4-d4bc-4689-87cf-1be40aa49c27", "leftValue": "={{ $('Verificador de Respuesta').item.json.output.response.part_2 }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-9500, 2580], "id": "38b6a5be-1e92-42c0-a15b-3a8ab4e4ef66", "name": "If Parte 2"}, {"parameters": {"amount": "={{ Math.max(2, Math.min(8, 2+$('Verificador de Respuesta').item.json.output.response.part_2.length*0.015)) }}"}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [-9260, 2480], "id": "c3230ff4-5fcb-4cdb-a18b-d588a70214fe", "name": "Wait", "webhookId": "fccc56ac-adbb-4a82-9af6-1f3a00fa08ff"}, {"parameters": {"method": "POST", "url": "={{ $('Edit Fields').item.json.serverUrl }}/message/sendText/{{ $('Edit Fields').item.json.instanceName }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "={{ $('Edit Fields').item.json.apiKey }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "number", "value": "={{ $('Edit Fields').item.json.message.chatId }}"}, {"name": "text", "value": "={{ $('Verificador de Respuesta').item.json.output.response.part_2 }}"}, {"name": "delay", "value": "={{ 2000 }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-9060, 2480], "id": "fe1a7f4f-0eb8-4afb-84d8-3a129b4a655d", "name": "Enviar Parte2"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "4b92a1f4-d4bc-4689-87cf-1be40aa49c27", "leftValue": "={{ $('Verificador de Respuesta').item.json.output.response.part_3 }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-8780, 2600], "id": "de4bce08-b61a-4236-b93d-1d7a525d5f25", "name": "If Parte 3"}, {"parameters": {"amount": "={{ Math.max(2, Math.min(8, 2+$('Verificador de Respuesta').item.json.output.response.part_3.length*0.015)) }}"}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [-8560, 2480], "id": "e86c6557-e478-45fc-a1dd-d82ee5286f88", "name": "Wait2", "webhookId": "610f359e-8fe3-480f-9042-e011a3c9fb43"}, {"parameters": {"method": "POST", "url": "={{ $('Edit Fields').item.json.serverUrl }}/message/sendText/{{ $('Edit Fields').item.json.instanceName }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "={{ $('Edit Fields').item.json.apiKey }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "number", "value": "={{ $('Edit Fields').item.json.message.chatId }}"}, {"name": "text", "value": "={{ $('Verificador de Respuesta').item.json.output.response.part_3 }}"}, {"name": "delay", "value": "={{ 2000 }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-8320, 2480], "id": "ce28794c-4919-470e-9f9a-8d78052099e8", "name": "Enviar Parte3"}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [-8080, 2620], "id": "3eb12252-7d94-4a3d-9c84-4867de2c1b8f", "name": "No Operation, do nothing1"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-10160, 3040], "id": "cdfdafe2-81f0-4c96-9431-fa04f3f1f65d", "name": "4o mini Verificador", "credentials": {"openAiApi": {"id": "KSAvlJBgncGdJAJg", "name": "OpenAi account"}}}, {"parameters": {"toolDescription": "=🔍 HERRAMIENTA PARA RESÚMENES EJECUTIVOS DETALLADOS\n\nANTES DE USAR: Verificar en {{ $json.fullContext }} que no se haya ejecutado con este propertyId\n\nVALIDACIÓN DE ESTADO: Solo usar en estado INTERES cuando cliente solicita detalles específicos\n\nObtiene información completa de una propiedad específica para generar resúmenes ejecutivos profesionales.\n\nCUÁNDO USAR:\n- Cliente dice: \"más información\", \"detalles\", \"cuéntame más\", \"mas info\"\n- Cliente pregunta por amenidades específicas\n- Cliente quiere descripción completa de una propiedad ya mostrada\n- Cliente solicita información detallada sobre una propiedad específica\n\nPARÁMETROS:\n- propertyId: ID EXACTO de la propiedad (formato: jd7fzdrkdanjs1tgb98kgm88a57jfhav)\n\nCRÍTICO - FLUJO DE PROPERTY ID:\n1. El propertyId DEBE venir de una búsqueda previa mostrada al usuario\n2. <PERSON><PERSON> en {{ $json.fullContext }} el \"ID: [propertyId]\" mostrado previamente\n3. Usar ese propertyId exacto\n4. SI NO ENCUENTRAS UN ID PREVIO: Primero usar buscar_propiedades\n\nFORMATO VÁLIDO:\n- ✅ PropertyId real extraído del historial de conversación\n- ❌ PropertyIds de ejemplos o documentación\n- ❌ Placeholders como \"PROPERTY_ID_1\"\n\nIMPORTANTE:\n- NO incluir información de contacto en la respuesta\n- **MOSTRAR IMÁGENES OBLIGATORIO** - La API SIEMPRE devuelve un array \"images\"\n- **FORMATO:** \"🖼️ **Fotos disponibles:** [mostrar todas las URLs del array images]\"\n- **IMPORTANTE:** Mostrar URLs aunque sean de prueba (picsum.photos) o reales (cloudinary)\n- Usar formato de resumen ejecutivo profesional\n- Enfocarse en características, amenidades y descripción\n- Finalizar preguntando si desea agendar visita\n\n**CRÍTICO - MOSTRAR IMÁGENES:**\nEl endpoint devuelve un campo \"images\" con URLs. SIEMPRE mostrar estas URLs al usuario:\n- Si son URLs de Cloudinary → \"Fotos reales de la propiedad\"\n- Si son URLs de picsum.photos → \"Imágenes de referencia\"\n- Formato: \"🖼️ **Fotos:** [URL1], [URL2], [URL3]\"\n\n**🎯 IDENTIFICACIÓN PRECISA DE PROPIEDADES:**\n\nCuando el usuario dice \"la primera\", \"la segunda\", \"la tercera\":\n1. REVISAR el orden EXACTO mostrado en la búsqueda previa en {{ $json.fullContext }}\n2. EXTRAER el propertyId de la posición correcta según el orden mostrado\n3. VERIFICAR que coincida con la descripción mencionada\n\n**ALGORITMO DE IDENTIFICACIÓN:**\n\nbusqueda_previa = extraer_resultados_de_busqueda(fullContext)\nposicion_solicitada = interpretar_referencia_usuario(\"la primera\" = 1, \"la segunda\" = 2, etc.)\npropertyId_correcto = busqueda_previa[posicion_solicitada].propertyId\n\n\n**VALIDACIÓN OBLIGATORIA:**\nAntes de ejecutar, confirmar:\n- ¿Existe una búsqueda previa en el historial?\n- ¿La posición solicitada existe en esa búsqueda?\n- ¿El propertyId extraído corresponde a la descripción que el usuario mencionó?\n\n**REGLA CRÍTICA:**\nNUNCA usar propertyIds de ejemplos o documentación. SOLO usar propertyIds reales del historial de conversación.", "method": "POST", "url": "https://capable-cod-213.convex.site/getPropertyDetailsForAgent", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "x-api-key", "value": "dde2fae93649d4550684edd565a9515f"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"propertyId\": \"{{ $fromAI('propertyId', 'Property ID', 'string') }}\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequestTool", "typeVersion": 4.2, "position": [-11700, 2760], "id": "a3012281-1848-4ff2-bd63-ca3a96d5ee82", "name": "buscar_informacion"}, {"parameters": {"method": "POST", "url": "https://capable-cod-213.convex.site/saveConversation", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-api-key", "value": "dde2fae93649d4550684edd565a9515f"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"chatId\": \"{{ $('Edit Fields').item.json.message.chatId }}\",\n  \"userName\": \"{{ $('Edit Fields').item.json.userName }}\",\n  \"messageId\": \"{{ $('Edit Fields').item.json.message.messageId }}\",\n  \"messageType\": \"user\",\n  \"content\": \"{{ $('Edit Fields').item.json.message.messageContent }}\",\n  \"timestamp\": \"{{ $('Edit Fields').item.json.message.messageTimeStamp }}\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-10560, 2920], "id": "f875523b-4ecd-4fc6-adf9-aa210bc912d0", "name": "Save Chat Convex"}, {"parameters": {"method": "POST", "url": "https://capable-cod-213.convex.site/getChatHistoryForAgent", "sendHeaders": true, "headerParameters": {"parameters": [{"name": " Content-Type", "value": "application/json"}, {"name": "x-api-key", "value": "dde2fae93649d4550684edd565a9515f"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"chatId\": \"{{ $('Edit Fields').item.json.message.chatId }}\",\n  \"limit\": 20\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-12760, 2700], "id": "496ab707-9589-4915-bf24-0b85b9c549d8", "name": "Get Chat History"}, {"parameters": {"assignments": {"assignments": [{"id": "5f210319-4ae0-42a6-a130-153b675fd836", "name": "fullContext", "value": "==\"CLIENTE: {{ $('Edit Fields').item.json.userName }}\nFECHA: {{ $now.format('YYYY-MM-DD HH:mm') }}\n\nHISTORIAL COMPLETO:\n{{ $('Get Chat History').item.json.conversations && $('Get Chat History').item.json.conversations.length > 0 ? $('Get Chat History').item.json.conversations.map(c => (c.messageType === 'user' ? 'CLIENTE' : 'TÚ (INMO)') + ': ' + c.content).join('\\n\\n') : 'Primera conversación con este cliente' }}\n\nANÁLISIS RÁPIDO:\n- ¿Hay propiedades mostradas previamente? {{ $('Get Chat History').item.json.conversations && $('Get Chat History').item.json.conversations.some(c => c.content.includes('🏠')) ? 'SÍ - Revisar cuáles' : 'NO - Nueva búsqueda' }}\n- ¿Hay citas mencionadas? {{ $('Get Chat History').item.json.conversations && $('Get Chat History').item.json.conversations.some(c => c.content.includes('cita') || c.content.includes('visita') || c.content.includes('confirmado')) ? 'SÍ - Verificar detalles' : 'NO - Nueva gestión' }}\n\nCONSIDERACIONES DE APRENDIZAJE AUTOMÁTICO:\n{{ $('Get Knowledge Base').item.json.data && $('Get Knowledge Base').item.json.data.length > 0 ? $('Get Knowledge Base').item.json.data.map(k => '• [' + k.priority.toUpperCase() + '] ' + k.consideration + ' (Categoría: ' + k.category + ')').join('\\n') : 'No hay consideraciones específicas disponibles' }}\n\nMENSAJE ACTUAL: {{ $('Edit Fields').item.json.message.messageContent }}\n\nINSTRUCCIÓN: Revisa todo el historial, análisis y consideraciones de aprendizaje antes de responder. Aplica las consideraciones relevantes para mejorar tu respuesta. Si ya mostraste propiedades y el cliente muestra interés, procede directamente a agendar visita.\"", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-12120, 2700], "id": "b7baf54f-839c-4f20-884b-7be217f032d4", "name": "Prepare Context"}, {"parameters": {"toolDescription": "🏡 CONTEXTO DE PROPIEDAD PARA PREPARACIÓN DE CITAS\n\nObtiene información completa de una propiedad incluyendo disponibilidad del propietario. SOLO usar antes de gestionar citas.\n\nCUÁNDO USAR:\n- SOLO antes de verificar disponibilidad de horarios\n- SOLO cuando cliente confirmó querer agendar visita\n- Necesitar contexto del propietario y disponibilidad\n\nNO USAR PARA:\n- Información general al cliente (usar buscar_informacion)\n- Mostrar detalles de propiedades\n\nPARÁMETROS:\n- propertyId: ID EXACTO de la propiedad (formato: jd7fzdrkdanjs1tgb98kgm88a57jfhav)\n\nCRÍTICO - FORMATO DE PROPERTY ID:\n- USAR el ID completo mostrado en búsquedas previas\n- EJEMPLO CORRECTO: \"jd7fzdrkdanjs1tgb98kgm88a57jfhav\" (ID real de búsqueda previa)\n- EJEMPLO INCORRECTO: \"1\", \"2\", \"propiedad 1\"\n\nDevuelve: detalles de propiedad, info del propietario, horarios configurados, citas pendientes.\n\nVALIDACIÓN DE SECUENCIA:\n- Solo usar DESPUÉS de consultar_estado_cita según secuencia obligatoria\n- Verificar en {{ $json.fullContext }} que estamos en estado AGENDAMIENTO\n- Confirmar que el propertyId viene de una búsqueda previa mostrada al cliente", "method": "POST", "url": "https://capable-cod-213.convex.site/getPropertyContextForAI", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "x-api-key", "value": "dde2fae93649d4550684edd565a9515f"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"propertyId\": \"{{ $fromAI('propertyId', 'Property ID', 'string') }}\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequestTool", "typeVersion": 4.2, "position": [-11700, 2980], "id": "0a78d6cf-5132-4ae1-8934-5d4d7711fc4c", "name": "obtener_contexto_propiedad"}, {"parameters": {"toolDescription": "=🚨 VALIDACIÓN CRÍTICA ANTES DE USAR 🚨\n\n**NUNCA ejecutar esta herramienta hasta que el cliente especifique el DÍA:**\n\n❌ Cliente dice: \"quiero agendar visita\" → NO usar esta herramienta\n❌ Cliente dice: \"me gustaría conocerla\" → NO usar esta herramienta  \n❌ Cliente dice: \"si\" → NO usar esta herramienta\n✅ Cliente dice: \"el viernes\" → ENTONCES usar esta herramienta\n✅ Cliente dice: \"mañana\" → ENTONCES usar esta herramienta\n✅ Cliente dice: \"el 5 de julio\" → ENTONCES usar esta herramienta\n\n**PROTOCOLO OBLIGATORIO:**\n1. Cliente solicita visita → PREGUNTAR: \"¿Qué día le gustaría visitar la propiedad?\"\n2. Cliente especifica día → ENTONCES usar esta herramienta\n3. NUNCA mostrar horarios sin día específico\n\n**PASOS PREVIOS OBLIGATORIOS:**\n1. consultarEstadoCita (evitar duplicados)\n2. PREGUNTAR día específico al cliente\n3. ENTONCES usar esta herramienta\n\n---\n\n🔍 **PROPÓSITO:** Verifica horarios disponibles para una propiedad específica. Úsala SOLO DESPUÉS de que el cliente especifique el día exacto que quiere visitar.\n\n**PARÁMETROS OBLIGATORIOS:**\n- propertyId: ID EXACTO de la propiedad (formato: jd7fzdrkdanjs1tgb98kgm88a57jfhav)\n- preferredDates: fechas como string separado por comas: \"2025-07-01,2025-07-02,2025-07-03\"\n- duration: duración en minutos (opcional, default 60)\n\n**CRÍTICO - FORMATO DE PROPERTY ID:**\n- USAR el ID completo mostrado en búsquedas previas\n- ✅ CORRECTO: \"jd7fzdrkdanjs1tgb98kgm88a57jfhav\" (ID real de búsqueda previa)\n- ❌ INCORRECTO: \"1\", \"2\", \"propiedad 1\"\n\n**🧠 MEMORIA DE FECHAS CRÍTICA:**\nANTES de generar fechas, OBLIGATORIO revisar {{ $json.fullContext }} para:\n1. EXTRAER todas las fechas ya consultadas previamente\n2. IDENTIFICAR fechas rechazadas por el cliente (\"no puedo ese día\")\n3. GENERAR fechas NUEVAS que NO estén en el historial\n4. EXCLUIR completamente las fechas ya verificadas\n\n**INTERPRETACIÓN DE SOLICITUDES:**\n- \"Mañana\" → Usar fecha de mañana + 2 fechas siguientes\n- \"El viernes\" → Usar próximo viernes + 2 fechas alternativas\n- \"Esta semana\" → Generar fechas de esta semana EXCLUYENDO las ya consultadas\n- \"Próxima semana\" → Generar fechas de próxima semana EXCLUYENDO las ya consultadas\n- \"Otro día\" → CRÍTICO: Generar fechas DIFERENTES a las ya consultadas\n\n**ALGORITMO DE FECHAS NUEVAS:**\n```\nLEER: {{ $json.fullContext }}\nEXTRAER: fechas_ya_consultadas = [todas las fechas del historial]\nSI cliente especifica día:\n  fechas_nuevas = [día_especificado + 2_alternativas] EXCLUYENDO fechas_ya_consultadas\nUSAR: fechas_nuevas en la consulta\n```\n\n**VALIDACIONES FINALES:**\n- SIEMPRE esperar la respuesta de la API antes de confirmar disponibilidad\n- SOLO mostrar al cliente las fechas que la API devuelve como disponibles\n- NO asumir horarios estándar - cada propiedad tiene configuración única\n- Convertir frases vagas en fechas específicas ANTES de usar esta herramienta\n\n**MANEJO DE ERRORES:**\n- Sin disponibilidad: \"No hay disponibilidad para esas fechas. Déjeme consultar fechas alternativas.\"\n- Error de conexión: \"Permíteme verificar la disponibilidad y le confirmo en un momento\"\n- Fechas ocupadas: \"Esas fechas están ocupadas. ¿Le convienen fechas más adelante?\"\n", "method": "POST", "url": "https://capable-cod-213.convex.site/checkPropertyAvailability", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "x-api-key", "value": "dde2fae93649d4550684edd565a9515f"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"propertyId\": \"{{ $fromAI('propertyId', 'ID exacto de la propiedad del historial (ejemplo: jd7fzdrkdanjs1tgb98kgm88a57jfhav)', 'string') }}\",\n  \"preferredDates\": [\"{{ $fromAI('date1', 'Primera fecha YYYY-MM-DD', 'string') }}\", \"{{ $fromAI('date2', 'Segunda fecha YYYY-MM-DD', 'string') }}\", \"{{ $fromAI('date3', 'Tercera fecha YYYY-MM-DD', 'string') }}\"],\n  \"duration\": 60\n}\n\n\n\n\n", "options": {"allowUnauthorizedCerts": true}, "optimizeResponse": true}, "type": "n8n-nodes-base.httpRequestTool", "typeVersion": 4.2, "position": [-11680, 3220], "id": "1adf5f71-ca7c-42f6-b9f7-07409f5dffed", "name": "verificar_disponibilidad_propiedad"}, {"parameters": {"toolDescription": "🚨 VALIDACIÓN CRÍTICA ANTES DE USAR 🚨\n\n**NUNCA ejecutar sin TODOS estos datos REALES del usuario:**\n✅ guestName (nombre completo del usuario)\n✅ guestEmail (email válido con @ - PEDIRLO AL USUARIO)\n✅ guestPhone (8 dígitos numéricos - PEDIRLO AL USUARIO)\n✅ propertyId (formato: jd7fzdrkdanjs1tgb98kgm88a57jfhav)\n✅ Fecha/hora confirmada\n\n**🚫 PROHIBIDO ABSOLUTAMENTE:**\n- Usar emails falsos como \"<EMAIL>\"\n- Usar teléfonos falsos como \"55555555\"\n- Inventar datos de contacto\n- Crear citas sin datos reales del usuario\n\n**PROTOCOLO OBLIGATORIO:**\n1. Usuario confirma horario\n2. PREGUNTAR: \"Para confirmar la cita necesito su email y número de teléfono\"\n3. ESPERAR respuesta del usuario con datos reales\n4. VALIDAR que el email tenga @ y punto\n5. VALIDAR que el teléfono tenga 8 dígitos\n6. ENTONCES crear la cita\n\n**EJEMPLO DE SOLICITUD CORRECTA:**\n\"Perfecto, el horario de las 2:00 PM está disponible. Para confirmar la cita necesito que me proporciones:\n- Tu correo electrónico\n- Tu número de teléfono\n\nUna vez que tengas estos datos, procederé a agendar tu visita.\"\n\n**SECUENCIA OBLIGATORIA PREVIA:**\n1. consultar_estado_cita\n2. verificar_disponibilidad_propiedad\n3. RECOPILAR datos reales del usuario\n4. ENTONCES usar esta herramienta\n\n---\n\nCrea una solicitud de cita para visitar una propiedad. Úsala DESPUÉS de verificar disponibilidad y cuando el cliente confirme horario Y proporcione sus datos reales.\n\nPARÁMETROS OBLIGATORIOS:\n- propertyId: ID EXACTO de la propiedad (formato: jd7fzdrkdanjs1tgb98kgm88a57jfhav)\n- guestName: nombre del cliente (OBLIGATORIO - del historial)\n- guestEmail: email del cliente (OBLIGATORIO - PEDIDO AL USUARIO)\n- guestPhone: teléfono del cliente (OBLIGATORIO - PEDIDO AL USUARIO)\n- requestedStartTime: hora inicio en formato ISO\n- requestedEndTime: hora fin en formato ISO\n- type: \"property_viewing\"\n- meetingType: \"in_person\"\n\nOPCIONALES:\n- message: \"Solicitud de cita generada por IA\"\n\nCRÍTICO - FORMATO DE PROPERTY ID:\n- USAR el ID completo mostrado en búsquedas previas\n- EJEMPLO CORRECTO: \"jd7fzdrkdanjs1tgb98kgm88a57jfhav\" (ID real de búsqueda previa)\n- EJEMPLO INCORRECTO: \"1\", \"2\", \"propiedad 1\"\n\n🚨 MANEJO DE RESPUESTAS DE LA API 🚨\n\nLa API puede devolver 3 tipos de respuesta que DEBES manejar correctamente:\n\n**1. ÉXITO (success: true, isDuplicate: undefined):**\nConfirma con este formato:\n\"¡Perfecto! He creado su solicitud de cita:\n\n📅 **Detalles de su visita:**\n🏠 [Nombre de la propiedad]\n📍 [Dirección completa]\n📅 [Día, fecha y hora]\n👤 [Nombre del cliente]\n📧 [Email del cliente]\n📱 [Teléfono del cliente]\n\nEl propietario confirmará su solicitud de cita y le llegará un email con la confirmación.\"\n\n**2. ÉXITO CON ADVERTENCIA (success: true, warnings: {...}):**\nUsar el mensaje de la API + agregar la advertencia:\n\"¡Perfecto! He creado su solicitud de cita:\n[Detalles de la cita]\n\n⚠️ [Mostrar la advertencia de la API sobre conflictos del mismo día]\"\n\n**3. DUPLICADO BLOQUEADO (success: false, isDuplicate: true):**\nNUNCA crear cita. Usar el mensaje de la API directamente:\n\"[Mensaje exacto de la API explicando por qué no se puede crear]\n\n¿Le gustaría que le ayude a:\n- Verificar el estado de su cita existente\n- Modificar la fecha de su solicitud actual\n- Contactar al propietario directamente?\"\n\n**REGLAS CRÍTICAS:**\n- Si success: false → NO confirmar cita, explicar el problema\n- Si isDuplicate: true → Ofrecer alternativas (verificar estado, modificar fecha)\n- Si warnings existe → Mostrar advertencia pero confirmar que la cita se creó\n- SIEMPRE usar el mensaje de la API como base de la respuesta\n\nCRÍTICO - FORMATO DE FECHAS:\n- USAR zona horaria Guatemala: -06:00 (NO usar Z para UTC)\n- USAR año actual (dinámico)\n- EJEMPLO CORRECTO: \"2025-06-17T14:30:00-06:00\"\n- EJEMPLO INCORRECTO: \"2025-06-17T14:30:00.000Z\"\n- CONVERSIÓN: Si cliente dice \"2:30 PM\" → enviar \"2025-06-26T14:30:00-06:00\" (NO UTC)\n\nVALIDACIÓN PRE-CREACIÓN:\n- Verificar en historial que se completó toda la secuencia previa\n- Confirmar que no existe cita duplicada\n- Validar que se verificó disponibilidad\n- Asegurar que todos los datos fueron recopilados y confirmados\n- NUNCA usar datos falsos o inventados", "method": "POST", "url": "https://capable-cod-213.convex.site/createAppointmentRequest", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "x-api-key", "value": "dde2fae93649d4550684edd565a9515f"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"propertyId\": \"{{ $fromAI('propertyId', 'Property ID', 'string') }}\",\n  \"guestName\": \"{{ $fromAI('guestName', 'Guest name', 'string') }}\",\n  \"guestEmail\": \"{{ $fromAI('guestEmail', 'Guest email', 'string') }}\",\n  \"guestPhone\": \"{{ $fromAI('guestPhone', 'Guest phone', 'string') }}\",\n  \"requestedStartTime\": \"{{ $fromAI('requestedStartTime', 'Start time ISO string', 'string') }}\",\n  \"requestedEndTime\": \"{{ $fromAI('requestedEndTime', 'End time ISO string', 'string') }}\",\n  \"message\": \"{{ $fromAI('message', 'Additional message', 'string') || 'Solicitud de cita generada por IA' }}\",\n  \"type\": \"{{ $fromAI('type', 'Appointment type', 'string') || 'property_viewing' }}\",\n  \"meetingType\": \"in_person\",\n  \"source\": \"n8n-ai-assistant\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequestTool", "typeVersion": 4.2, "position": [-10780, 2960], "id": "bc672789-4f99-43e5-ab21-454f6af870b4", "name": "crear_solicitud_cita"}, {"parameters": {"method": "POST", "url": "https://capable-cod-213.convex.site/saveConversation", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-api-key", "value": "dde2fae93649d4550684edd565a9515f"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"chatId\": \"{{ $('Edit Fields').item.json.message.chatId }}\",\n  \"userName\": \"Inmo Agent\", \n  \"messageId\": \"agent_{{ $('Edit Fields').item.json.message.messageId }}_response\",\n  \"messageType\": \"assistant\",\n  \"content\": {{ JSON.stringify($json.output) }},\n  \"timestamp\": \"{{ $now.toISOString() }}\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-10560, 2420], "id": "f8d98ad9-463f-40b9-85a3-2841ead668f0", "name": "Save Agent Response"}, {"parameters": {"toolDescription": "🚨 HERRAMIENTA OBLIGATORIA ANTI-DUPLICADOS 🚨\n\nUSAR SIEMPRE ANTES de crear cualquier cita nueva. Verifica si ya existen citas para evitar duplicados.\n\nUSO OBLIGATORIO:\n- ANTES de llamar \"verificar_disponibilidad_propiedad\"\n- ANTES de llamar \"crear_solicitud_cita\"\n- Cuando cliente solicite agendar una visita\n\nPARÁMETROS RECOMENDADOS:\n- propertyId: ID EXACTO de la propiedad (formato: jd7fzdrkdanjs1tgb98kgm88a57jfhav)\n\nCRÍTICO - FORMATO DE PROPERTY ID:\n- USAR el ID completo mostrado en búsquedas previas\n- EJEMPLO CORRECTO: \"jd7fzdrkdanjs1tgb98kgm88a57jfhav\" (ID real de búsqueda previa)\n- EJEMPLO INCORRECTO: \"1\", \"2\", \"propiedad 1\"\n- guestName: Nombre del cliente del historial\n- guestEmail: Email si está disponible\n\nRESPUESTAS ESPERADAS:\n- Si encuentra citas existentes → Informar al cliente y preguntar si quiere modificar\n- Si NO encuentra citas → Continuar con proceso normal\n\nEJEMPLOS ADICIONALES:\n- \"¿Cómo está mi cita?\" → usar chatId automático\n- \"¿Tengo alguna cita programada?\" → usar guestName del historial\n\nMEMORIA DE CONTEXTO:\n- Revisar {{ $json.fullContext }} para extraer nombre del cliente y email si están disponibles\n- Usar información del historial para completar parámetros automáticamente\n- Si cliente pregunta \"¿cómo está mi cita?\" usar datos del contexto previo\n\n\n**🚨 INTERPRETACIÓN CRÍTICA DE ESTADOS:**\n\nLa API devuelve estados que DEBES comunicar correctamente:\n\n📊 ESTADOS POSIBLES:\n- \"pending\" = PENDIENTE (solicitud enviada, esperando confirmación)\n- \"confirmed\" = CONFIRMADA (propietario aceptó)\n- \"rejected\" = RECHAZADA (propietario rechazó)\n- \"completed\" = COMPLETADA (cita realizada)\n- \"cancelled\" = CANCELADA\n\n🗣️ COMUNICACIÓN OBLIGATORIA:\n- PENDING: \"Su cita está PENDIENTE de confirmación. El propietario aún no ha respondido.\"\n- CONFIRMED: \"¡Perfecto! Su cita está CONFIRMADA.\"\n- REJECTED: \"Su cita fue rechazada. ¿Le gustaría otra fecha?\"\n\n**REGLA CRÍTICA:**\nNUNCA decir \"confirmada\" si el estado es \"pending\"", "method": "POST", "url": "https://capable-cod-213.convex.site/consultarEstadoCita", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "x-api-key", "value": "dde2fae93649d4550684edd565a9515f"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"guestName\": \"{{ $fromAI('guestName', 'Nombre del cliente', 'string') || null }}\",\n  \"guestEmail\": \"{{ $fromAI('guestEmail', 'Email del cliente', 'string') || null }}\",\n  \"guestPhone\": \"{{ $fromAI('guestPhone', 'Teléfono del cliente', 'string') || null }}\",\n  \"propertyId\": \"{{ $fromAI('propertyId', 'ID de la propiedad', 'string') || null }}\",\n  \"fecha\": \"{{ $fromAI('fecha', 'Fecha en formato YYYY-MM-DD', 'string') || null }}\",\n  \"chatId\": \"{{ $('Edit Fields').item.json.message.chatId }}\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequestTool", "typeVersion": 4.2, "position": [-11460, 3100], "id": "f3531cf4-44b3-4220-b7e1-c753456f41b1", "name": "consultar_estado_cita"}, {"parameters": {"method": "POST", "url": "https://capable-cod-213.convex.site/getActiveKnowledgeBase", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "x-api-key", "value": "dde2fae93649d4550684edd565a9515f"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"limit\": 20,\n  \"priority\": \"high\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-12440, 2700], "id": "1e68db21-44ab-4d2c-9f08-87428256e2d4", "name": "Get Knowledge Base"}, {"parameters": {"toolDescription": "🔍 BÚSQUEDA INTELIGENTE CON ANÁLISIS DE RELEVANCIA AUTOMÁTICO\n\nBusca propiedades y analiza automáticamente la relevancia de cada resultado usando scores de precisión semántica.\n\nCUÁNDO USAR:\n- Cliente necesita ver opciones iniciales\n- Cliente cambió criterios y necesita nueva búsqueda\n- Cliente solicita \"más opciones\" o \"otras propiedades\"\n- Transición de estado ACTIVA a BÚSQUEDA\n\nPARÁMETROS:\n- query: Descripción completa de lo que busca el cliente\n\nSISTEMA DE RELEVANCIA AUTOMÁTICO:\nEl API incluye scores automáticos de relevancia (0-100%). DEBES interpretar y comunicar estos scores:\n\n📊 CATEGORÍAS DE RELEVANCIA (UMBRALES ESTRICTOS):\n- 🎯 95-100%: \"COINCIDENCIA EXACTA\" - Cumple perfectamente todos los criterios\n- 🔄 80-94%: \"COINCIDENCIA PARCIAL\" - Cumple la mayoría de criterios con diferencias menores\n- 💡 60-79%: \"ALTERNATIVA RELEVANTE\" - Opción interesante con diferencias notables\n- ⚠️ <60%: \"BAJA RELEVANCIA\" - Mencionar limitaciones importantes\n\nCOMUNICACIÓN TRANSPARENTE OBLIGATORIA:\n1. IDENTIFICAR el tipo de coincidencias encontradas\n2. EXPLICAR ESPECÍFICAMENTE las diferencias cuando no es exacta\n3. SER TRANSPARENTE sobre por qué cada propiedad es relevante o no\n4. SUGERIR refinamientos si los scores son bajos\n\nFORMATO DE RESPUESTA CON EXPLICACIONES:\n🏠 **[Título]** (ID: {{ property.propertyId }})\n📍 [Ubicación completa]\n💰 $[precio] [moneda] ([status])\n🏗️ [habitaciones] hab | [baños] baños | [área] m²\n✨ [Amenidades principales]\n🎯 Relevancia: [score]% [CATEGORÍA]\n⚠️ Diferencias: [Explicar específicamente qué no coincide exactamente]\n\n\nEJEMPLOS DE COMUNICACIÓN TRANSPARENTE:\n\nPara 90% PARCIAL:\n\"Encontré 3 propiedades con COINCIDENCIA PARCIAL (87-90%) para tu búsqueda. Aunque no hay coincidencias exactas al 100%, estas opciones cumplen la mayoría de tus criterios con algunas diferencias menores que te explico...\"\n\nPara diferencias específicas:\n\"⚠️ Diferencias: Ubicado en Zona 15 (muy cerca de Zona 10 solicitada)\"\n\"⚠️ Diferencias: Precio $150k (ligeramente por debajo de tu rango 200k-300k)\"\n\"⚠️ Diferencias: 2 habitaciones (solicitaste 3 habitaciones)\"\n\nCRÍTICO - PROPERTY ID:\n- SIEMPRE extraer el campo \"propertyId\" del JSON (NO el campo \"id\")\n- EJEMPLO CORRECTO: \"ID: jd24ftr-j0v20q/1xlhmxkwd2f/jms3t\"\n- EJEMPLO INCORRECTO: \"0e4da365-0c4d-4c4d-8a36-5e4ccd12a365\" (este es el UUID, no usar)\n- FORMATO: Usar property.propertyId, NO property.id\n\nFINALIZAR SIEMPRE:\n- Preguntar si desea refinar búsqueda para mejores coincidencias\n- Ofrecer ver más detalles de alguna propiedad específica\n- Sugerir ajustar criterios si no hay coincidencias exactas\n\nVALIDACIÓN DE ESTADO: Solo usar en transición CRITERIOS→BUSQUEDA según matriz de funciones del system message\n\nANTES DE EJECUTAR:\n- Verificar en {{ $json.fullContext }} que no se haya ejecutado con estos criterios exactos\n- Confirmar que tenemos criterios mínimos (zona + presupuesto)\n- Validar que estamos en el estado correcto para búsqueda", "method": "POST", "url": "https://inmo-nine.vercel.app/api/v1/search", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "x-api-key", "value": "demo-rag-key-2025"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"query\": \"{{ $fromAI('query', 'El mensaje completo del usuario sobre búsqueda de propiedades', 'string') }}\",\n  \"options\": {\n    \"limit\": 12,\n    \"includeResponse\": false,\n    \"responseLanguage\": \"es\",\n    \"searchType\": \"semantic\",\n    \"includeBreakdown\": true,\n    \"enableAdvancedPrecision\": true\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequestTool", "typeVersion": 4.2, "position": [-11020, 3060], "id": "3b53e1d8-c8a4-49a6-be4b-97de33663fa6", "name": "buscar_propiedades"}, {"parameters": {"toolDescription": "📅 CONSULTAR TODAS LAS CITAS DEL USUARIO\n\nObtiene el historial completo de citas del usuario (confirmadas y pendientes).\n\nCUÁNDO USAR:\n- Usuario pregunta: \"¿qué citas tengo?\", \"muéstrame mis citas\", \"¿cuándo es mi próxima visita?\"\n- Usuario dice: \"¿qué citas tengo programadas?\", \"mi agenda\", \"mis visitas\"\n- Necesitas mostrar el historial completo de citas del usuario\n- Antes de modificar o cancelar citas (para identificar cuál)\n\nNO USAR PARA:\n- Verificar estado de UNA cita específica (usar consultar_estado_cita)\n- Crear nuevas citas (usar crear_solicitud_cita)\n\nPARÁMETROS:\n- guestEmail: Email del usuario (OBLIGATORIO - debe estar en el historial de conversación)\n\nRESPUESTA:\n- Lista completa de citas del usuario con estados y detalles\n- Incluye tanto citas confirmadas como solicitudes pendientes\n- Información de propiedades asociadas\n\nFORMATO DE RESPUESTA:\nPresenta las citas de forma organizada por estado y fecha, mostrando:\n- Propiedad y ubicación\n- Fecha y hora\n- Estado actual (pending, confirmed, rejected, etc.)\n- Detalles relevantes para el usuario", "method": "POST", "url": "https://capable-cod-213.convex.site/consultarEstadoCita", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "x-api-key", "value": "dde2fae93649d4550684edd565a9515f"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"guestEmail\": \"{{ $fromAI('guestEmail', 'Email del usuario', 'string') }}\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequestTool", "typeVersion": 4.2, "position": [-10840, 3280], "id": "fa867caf-fffa-4546-951b-0a939d92f396", "name": "consultar_citas_usuario"}, {"parameters": {"toolDescription": "🔄 SOLICITAR CAMBIO DE CITA EXISTENTE\n\nModifica la fecha y/o hora de una cita ya programada.\n\nCUÁNDO USAR:\n- Usuario dice: \"quiero cambiar mi cita\", \"cambiar fecha\", \"modificar horario\"\n- Usuario solicita: \"mover mi cita al viernes\", \"cambiar a las 3pm\"\n- Usuario pide: \"reprogramar mi visita\", \"otra fecha por favor\"\n\nFLUJO OBLIGATORIO ANTES DE USAR:\n1. PRIMERO ejecutar \"consultar_citas_usuario\" para identificar qué citas tiene\n2. IDENTIFICAR la cita específica que quiere cambiar\n3. LUEGO ejecutar este tool con el appointmentId correcto\n\nPARÁMETROS REQUERIDOS:\n- appointmentId: ID de la cita a modificar (formato Convex ID)\n- startTime: Nueva fecha y hora de inicio (formato ISO: \"2025-01-15T14:00:00-06:00\")\n- endTime: Nueva fecha y hora de fin (formato ISO: \"2025-01-15T15:00:00-06:00\")\n\nPARÁMETROS OPCIONALES:\n- notes: Razón del cambio o notas adicionales\n\nVALIDACIONES CRÍTICAS:\n- appointmentId debe ser real y existir en el sistema\n- Las nuevas fechas deben ser futuras\n- Formato de fecha debe ser ISO con zona horaria Guatemala (-06:00)\n- Duración típica: 1 hora (ajustar endTime en consecuencia)\n\nRESPUESTA:\n- Confirmación del cambio exitoso\n- Notificación automática al propietario\n- Actualización del estado en la base de datos\n\nIMPORTANTE:\n- Este tool MODIFICA citas existentes, no crea nuevas\n- Siempre verificar disponibilidad antes de confirmar cambios\n- Comunicar claramente al usuario que el cambio está sujeto a aprobación del propietario", "method": "POST", "url": "https://capable-cod-213.convex.site/updateAppointment", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "x-api-key", "value": "dde2fae93649d4550684edd565a9515f"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"appointmentId\": \"{{ $fromAI('appointmentId', 'ID de la cita a modificar', 'string') }}\",\n  \"startTime\": \"{{ $fromAI('startTime', 'Nueva fecha y hora de inicio (ISO format)', 'string') }}\",\n  \"endTime\": \"{{ $fromAI('endTime', 'Nueva fecha y hora de fin (ISO format)', 'string') }}\",\n  \"notes\": \"{{ $fromAI('notes', 'Razón del cambio o notas adicionales', 'string') }}\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequestTool", "typeVersion": 4.2, "position": [-11120, 3240], "id": "08ae8703-be78-49d2-bd06-2fe664d2cbc9", "name": "solicitar_cambio_cita"}, {"parameters": {"toolDescription": "❌ CANCELAR CITA EXISTENTE\n\nCancela definitivamente una cita programada.\n\nCUÁNDO USAR:\n- Usuario dice: \"cancelar mi cita\", \"no puedo ir\", \"anular mi visita\"\n- Usuario solicita: \"eliminar mi cita del viernes\", \"ya no necesito la cita\"\n- Usuario pide: \"cancelar la visita\", \"no voy a poder asistir\"\n\nFLUJO OBLIGATORIO ANTES DE USAR:\n1. PRIMERO ejecutar \"consultar_citas_usuario\" para identificar qué citas tiene\n2. IDENTIFICAR la cita específica que quiere cancelar\n3. CONFIRMAR con el usuario que realmente quiere cancelar\n4. LUEGO ejecutar este tool con el appointmentId correcto\n\nPARÁMETROS REQUERIDOS:\n- appointmentId: ID de la cita a cancelar (formato Convex ID)\n- cancelledBy: Email o nombre del usuario que cancela\n- cancellationReason: Motivo de la cancelación\n\nVALIDACIONES CRÍTICAS:\n- appointmentId debe ser real y existir en el sistema\n- Solo se pueden cancelar citas con estado \"scheduled\" o \"confirmed\"\n- La cancelación es IRREVERSIBLE\n\nRESPUESTA:\n- Confirmación de cancelación exitosa\n- Notificación automática al propietario\n- Actualización del estado a \"cancelled\" en la base de datos\n\nIMPORTANTE:\n- Este tool CANCELA definitivamente la cita\n- No se puede deshacer la cancelación\n- Siempre confirmar con el usuario antes de ejecutar\n- Ofrecer reprogramar en lugar de cancelar cuando sea apropiado\n\nCOMUNICACIÓN AL USUARIO:\n- \"Su cita ha sido cancelada exitosamente\"\n- \"El propietario ha sido notificado de la cancelación\"\n- \"¿Le gustaría programar una nueva cita para otra fecha?\"", "method": "POST", "url": "https://capable-cod-213.convex.site/cancelAppointment", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "x-api-key", "value": "dde2fae93649d4550684edd565a9515f"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"appointmentId\": \"{{ $fromAI('appointmentId', 'ID de la cita a cancelar', 'string') }}\",\n  \"cancelledBy\": \"{{ $fromAI('cancelledBy', 'Email o nombre del usuario que cancela', 'string') }}\",\n  \"cancellationReason\": \"{{ $fromAI('cancellationReason', 'Motivo de la cancelación', 'string') }}\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequestTool", "typeVersion": 4.2, "position": [-11320, 3240], "id": "a9a3a57d-c149-4c9b-88fa-4c7f67af75f9", "name": "cancelar_cita"}], "pinData": {"Webhook": [{"json": {"headers": {"host": "apps-n8n.yo7wfj.easypanel.host", "user-agent": "axios/1.7.9", "content-length": "967", "accept-encoding": "gzip, compress, deflate, br", "content-type": "application/json", "x-forwarded-for": "**********", "x-forwarded-host": "apps-n8n.yo7wfj.easypanel.host", "x-forwarded-port": "443", "x-forwarded-proto": "https", "x-forwarded-server": "75b82128a382", "x-real-ip": "**********"}, "params": {}, "query": {}, "body": {"event": "messages.upsert", "instance": "deEntradaIAagent", "data": {"key": {"remoteJid": "<EMAIL>", "fromMe": false, "id": "44EEF66EFD002F3D2C0149C1041DAA06"}, "pushName": "<PERSON>", "status": "DELIVERY_ACK", "message": {"conversation": "El segundo esta interesante tienes las info?", "messageContextInfo": {"deviceListMetadata": {"senderKeyHash": "3pjoXdIIx+IvyQ==", "senderTimestamp": "1750119771", "recipientKeyHash": "rZulyrIke/ZmPw==", "recipientTimestamp": "1750260603"}, "deviceListMetadataVersion": 2, "messageSecret": "B5eRmNd2Q2AZPvYMhC4n1LTDeUiwgE6/Xo/NI5sexVk="}}, "messageType": "conversation", "messageTimestamp": 1751302349, "instanceId": "5d904c2f-3051-40b8-8032-fe76fd1ebdee", "source": "android"}, "destination": "https://apps-n8n.yo7wfj.easypanel.host/webhook/inmo-agent", "date_time": "2025-06-30T13:52:29.460Z", "sender": "<EMAIL>", "server_url": "https://apps-evolution-api.yo7wfj.easypanel.host", "apikey": "078FA839BEAD-48A5-821F-F99312842059"}, "webhookUrl": "https://apps-n8n.yo7wfj.easypanel.host/webhook/inmo-agent", "executionMode": "production"}}]}, "connections": {"Webhook": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "push mensaje": {"main": [[{"node": "Wait1", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Wait1": {"main": [[{"node": "obtener todos mensajes", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "borrar to<PERSON> mensajes", "type": "main", "index": 0}], [{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "obtener todos mensajes": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "borrar todos mensajes": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "push mensaje", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "descargar audio", "type": "main", "index": 0}], [{"node": "descargar imagen", "type": "main", "index": 0}], [{"node": "text content", "type": "main", "index": 0}]]}, "descargar audio": {"main": [[{"node": "convertir audio", "type": "main", "index": 0}]]}, "convertir audio": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "audio content", "type": "main", "index": 0}]]}, "descargar imagen": {"main": [[{"node": "convertir imagen", "type": "main", "index": 0}]]}, "convertir imagen": {"main": [[{"node": "describe imagen", "type": "main", "index": 0}]]}, "audio content": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "describe imagen": {"main": [[{"node": "image content", "type": "main", "index": 0}]]}, "image content": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "text content": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 2}]]}, "Merge": {"main": [[{"node": "Sort", "type": "main", "index": 0}]]}, "Sort": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "mensaje", "type": "main", "index": 0}]]}, "Json Parse": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "OpenAI Chat Model2": {"ai_languageModel": [[{"node": "AI Agent1", "type": "ai_languageModel", "index": 0}]]}, "mensaje": {"main": [[{"node": "Get Chat History", "type": "main", "index": 0}]]}, "Auto-fixing Output Parser": {"ai_outputParser": [[{"node": "Verificador de Respuesta", "type": "ai_outputParser", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Auto-fixing Output Parser", "type": "ai_outputParser", "index": 0}]]}, "Verificador de Respuesta": {"main": [[{"node": "Enviar Parte1", "type": "main", "index": 0}]]}, "Enviar Parte1": {"main": [[{"node": "If Parte 2", "type": "main", "index": 0}]]}, "If Parte 2": {"main": [[{"node": "Wait", "type": "main", "index": 0}], [{"node": "If Parte 3", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "Enviar Parte2", "type": "main", "index": 0}]]}, "Enviar Parte2": {"main": [[{"node": "If Parte 3", "type": "main", "index": 0}]]}, "If Parte 3": {"main": [[{"node": "Wait2", "type": "main", "index": 0}], [{"node": "No Operation, do nothing1", "type": "main", "index": 0}]]}, "Wait2": {"main": [[{"node": "Enviar Parte3", "type": "main", "index": 0}]]}, "Enviar Parte3": {"main": [[{"node": "No Operation, do nothing1", "type": "main", "index": 0}]]}, "AI Agent1": {"main": [[{"node": "Save Chat Convex", "type": "main", "index": 0}, {"node": "Verificador de Respuesta", "type": "main", "index": 0}, {"node": "Save Agent Response", "type": "main", "index": 0}]]}, "4o mini Verificador": {"ai_languageModel": [[{"node": "Auto-fixing Output Parser", "type": "ai_languageModel", "index": 0}, {"node": "Verificador de Respuesta", "type": "ai_languageModel", "index": 0}]]}, "buscar_informacion": {"ai_tool": [[{"node": "AI Agent1", "type": "ai_tool", "index": 0}]]}, "Save Chat Convex": {"main": [[]]}, "Get Chat History": {"main": [[{"node": "Get Knowledge Base", "type": "main", "index": 0}]]}, "Prepare Context": {"main": [[{"node": "AI Agent1", "type": "main", "index": 0}]]}, "obtener_contexto_propiedad": {"ai_tool": [[{"node": "AI Agent1", "type": "ai_tool", "index": 0}]]}, "verificar_disponibilidad_propiedad": {"ai_tool": [[{"node": "AI Agent1", "type": "ai_tool", "index": 0}]]}, "crear_solicitud_cita": {"ai_tool": [[{"node": "AI Agent1", "type": "ai_tool", "index": 0}]]}, "consultar_estado_cita": {"ai_tool": [[{"node": "AI Agent1", "type": "ai_tool", "index": 0}]]}, "Get Knowledge Base": {"main": [[{"node": "Prepare Context", "type": "main", "index": 0}]]}, "buscar_propiedades": {"ai_tool": [[{"node": "AI Agent1", "type": "ai_tool", "index": 0}]]}, "consultar_citas_usuario": {"ai_tool": [[{"node": "AI Agent1", "type": "ai_tool", "index": 0}]]}, "solicitar_cambio_cita": {"ai_tool": [[{"node": "AI Agent1", "type": "ai_tool", "index": 0}]]}, "cancelar_cita": {"ai_tool": [[{"node": "AI Agent1", "type": "ai_tool", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "6a85228e-9a26-4f18-95e2-dfdbf495928e", "meta": {"instanceId": "07b1f54ce63233c892bab2194482dcfcc49ccf3275504c189d498853ae39ba61"}, "id": "WPICWGh9T6aaDOm2", "tags": []}