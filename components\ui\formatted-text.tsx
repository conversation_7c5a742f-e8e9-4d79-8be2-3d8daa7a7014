import React from 'react';

interface FormattedTextProps {
  text: string;
  className?: string;
}

export function FormattedText({ text, className = "" }: FormattedTextProps) {
  // Función para formatear el texto
  const formatText = (text: string) => {
    return text
      .split('\n')
      .map((line: any, index: any) => (
        <React.Fragment key={`line-${index}-${line.substring(0, 10)}`}>
          {line}
          {index < text.split('\n').length - 1 && <br />}
        </React.Fragment>
      ));
  };

  return (
    <div className={`whitespace-pre-wrap ${className}`}>
      {formatText(text)}
    </div>
  );
}

// Componente más avanzado con soporte para párrafos y listas
export function RichFormattedText({ text, className = "" }: FormattedTextProps) {
  const formatAdvancedText = (text: string) => {
    const lines = text.split('\n');
    const elements: React.ReactNode[] = [];
    
    lines.forEach((line, index) => {
      const trimmedLine = line.trim();
      
      if (trimmedLine === '') {
        // Línea vacía - agregar espacio
        elements.push(<div key={`empty-${index}`} className="h-4" />);
      } else if (trimmedLine.startsWith('• ') || trimmedLine.startsWith('- ')) {
        // Lista con viñetas
        elements.push(
          <div key={`bullet-${index}-${trimmedLine.substring(0, 10)}`} className="flex items-start gap-2 mb-2">
            <span className="text-blue-600 mt-1">•</span>
            <span>{trimmedLine.substring(2)}</span>
          </div>
        );
      } else if (trimmedLine.match(/^\d+\.\s/)) {
        // Lista numerada
        elements.push(
          <div key={`numbered-${index}-${trimmedLine.substring(0, 10)}`} className="flex items-start gap-2 mb-2">
            <span className="text-blue-600 font-medium">{trimmedLine.match(/^\d+\./)?.[0]}</span>
            <span>{trimmedLine.replace(/^\d+\.\s/, '')}</span>
          </div>
        );
      } else if (trimmedLine.length > 0) {
        // Párrafo normal
        elements.push(
          <p key={`paragraph-${index}-${trimmedLine.substring(0, 10)}`} className="mb-3 last:mb-0">
            {trimmedLine}
          </p>
        );
      }
    });
    
    return elements;
  };

  return (
    <div className={`leading-relaxed ${className}`}>
      {formatAdvancedText(text)}
    </div>
  );
} 