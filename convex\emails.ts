import { v } from "convex/values";
import { action } from "./_generated/server";

// Configuración de Resend
const RESEND_API_KEY = process.env.RESEND_API_KEY;

// Templates de email
const EMAIL_TEMPLATES = {
  appointmentRequestOwner: (data: any) => ({
    subject: `Nueva solicitud de cita - ${data.propertyTitle}`,
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Nueva Solicitud de Cita</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #2563eb; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #f8fafc; padding: 30px; border-radius: 0 0 8px 8px; }
            .property-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2563eb; }
            .client-info { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; }
            .button { display: inline-block; background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; }
            .button:hover { background: #1d4ed8; }
            .footer { text-align: center; margin-top: 30px; color: #64748b; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🏠 Nueva Solicitud de Cita</h1>
            </div>
            <div class="content">
              <p>¡Hola! Has recibido una nueva solicitud de cita para tu propiedad.</p>
              
              <div class="property-card">
                <h3>📍 ${data.propertyTitle}</h3>
                <p><strong>Ubicación:</strong> ${data.propertyAddress}</p>
                <p><strong>Precio:</strong> ${data.propertyPrice}</p>
              </div>

              <div class="client-info">
                <h3>👤 Información del Cliente</h3>
                <p><strong>Nombre:</strong> ${data.guestName}</p>
                <p><strong>Email:</strong> ${data.guestEmail}</p>
                ${data.guestPhone ? `<p><strong>Teléfono:</strong> ${data.guestPhone}</p>` : ''}
                <p><strong>Fecha solicitada:</strong> ${data.requestedDate}</p>
                <p><strong>Hora:</strong> ${data.requestedTime}</p>
                <p><strong>Modalidad:</strong> ${data.meetingType === 'in_person' ? 'Presencial' : data.meetingType === 'video_call' ? 'Videollamada' : 'Llamada telefónica'}</p>
                ${data.message ? `<p><strong>Mensaje:</strong><br/>${data.message}</p>` : ''}
              </div>

              <div style="text-align: center; margin: 30px 0;">
                <a href="${data.dashboardUrl}" class="button">Ver en Mi Agenda</a>
              </div>

              <p><strong>⏰ ¿Qué hacer ahora?</strong></p>
              <ol>
                <li>Revisa la solicitud en tu agenda</li>
                <li>Confirma o propón una nueva fecha</li>
                <li>El cliente será notificado automáticamente</li>
              </ol>
            </div>
            <div class="footer">
              <p>Este email fue enviado por <strong>Inmova.gt</strong><br/>
              Si no puedes ver el botón, visita: ${data.dashboardUrl}</p>
            </div>
          </div>
        </body>
      </html>
    `
  }),

  appointmentRequestGuest: (data: any) => ({
    subject: `Solicitud de cita recibida - ${data.propertyTitle}`,
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Solicitud Recibida</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #10b981; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #f0fdf4; padding: 30px; border-radius: 0 0 8px 8px; }
            .property-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #10b981; }
            .status-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center; }
            .footer { text-align: center; margin-top: 30px; color: #64748b; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>✅ ¡Solicitud Recibida!</h1>
            </div>
            <div class="content">
              <p>Hola <strong>${data.guestName}</strong>,</p>
              <p>Hemos recibido tu solicitud de visita. Un agente se pondrá en contacto contigo pronto.</p>
              
              <div class="property-card">
                <h3>📍 Propiedad Solicitada</h3>
                <p><strong>${data.propertyTitle}</strong></p>
                <p>${data.propertyAddress}</p>
                <p><strong>Precio:</strong> ${data.propertyPrice}</p>
              </div>

              <div class="status-card">
                <h3>📅 Detalles de tu Solicitud</h3>
                <p><strong>Fecha solicitada:</strong> ${data.requestedDate}</p>
                <p><strong>Hora:</strong> ${data.requestedTime}</p>
                <p><strong>Modalidad:</strong> ${data.meetingType === 'in_person' ? 'Presencial' : data.meetingType === 'video_call' ? 'Videollamada' : 'Llamada telefónica'}</p>
                <p style="background: #fef3c7; padding: 10px; border-radius: 6px; margin-top: 15px;">
                  <strong>Estado:</strong> Pendiente de confirmación
                </p>
              </div>

              <p><strong>🕐 ¿Qué sigue?</strong></p>
              <ul>
                <li>El propietario revisará tu solicitud</li>
                <li>Te contactaremos para confirmar la cita</li>
                <li>Recibirás una notificación con la confirmación</li>
              </ul>

              <p>Si tienes alguna pregunta, no dudes en contactarnos.</p>
            </div>
            <div class="footer">
              <p>Gracias por usar <strong>Inmova.gt</strong><br/>
              Tu plataforma inmobiliaria de confianza</p>
            </div>
          </div>
        </body>
      </html>
    `
  }),

  appointmentConfirmed: (data: any) => ({
    subject: `Cita confirmada - ${data.propertyTitle}`,
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Cita Confirmada</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #059669; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #f0fdf4; padding: 30px; border-radius: 0 0 8px 8px; }
            .appointment-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 2px solid #059669; }
            .highlight { background: #d1fae5; padding: 15px; border-radius: 6px; margin: 15px 0; }
            .footer { text-align: center; margin-top: 30px; color: #64748b; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🎉 ¡Cita Confirmada!</h1>
            </div>
            <div class="content">
              <p>Hola <strong>${data.guestName}</strong>,</p>
              <p>¡Excelente noticia! Tu cita ha sido confirmada.</p>
              
              <div class="appointment-card">
                <h3>📅 Detalles de tu Cita</h3>
                <p><strong>Propiedad:</strong> ${data.propertyTitle}</p>
                <p><strong>Fecha:</strong> ${data.appointmentDate}</p>
                <p><strong>Hora:</strong> ${data.appointmentTime}</p>
                <p><strong>Modalidad:</strong> ${data.meetingType === 'in_person' ? 'Presencial' : data.meetingType === 'video_call' ? 'Videollamada' : 'Llamada telefónica'}</p>
                ${data.location ? `<p><strong>Ubicación:</strong> ${data.location}</p>` : ''}
                ${data.meetingUrl ? `<p><strong>Link de videollamada:</strong> <a href="${data.meetingUrl}">${data.meetingUrl}</a></p>` : ''}
              </div>

              <div class="highlight">
                <p><strong>📞 Información de Contacto</strong></p>
                <p><strong>Agente:</strong> ${data.ownerName}</p>
                ${data.ownerPhone ? `<p><strong>Teléfono:</strong> ${data.ownerPhone}</p>` : ''}
                <p><strong>Email:</strong> ${data.ownerEmail}</p>
              </div>

              <p><strong>📝 Recordatorio:</strong></p>
              <ul>
                <li>Llega puntual a tu cita</li>
                <li>Trae una identificación oficial</li>
                <li>Prepara tus preguntas sobre la propiedad</li>
                ${data.meetingType === 'in_person' ? '<li>Verifica la dirección antes de salir</li>' : ''}
                ${data.meetingType === 'video_call' ? '<li>Prueba tu conexión de internet</li>' : ''}
              </ul>

              ${data.response ? `<div style="background: #e0f2fe; padding: 15px; border-radius: 6px; margin: 15px 0;"><strong>Mensaje del agente:</strong><br/>${data.response}</div>` : ''}
            </div>
            <div class="footer">
              <p>¡Nos vemos pronto! - <strong>Inmova.gt</strong></p>
            </div>
          </div>
        </body>
      </html>
    `
  }),

  // 📧 NUEVO: Notificación de mensaje web recibido
  webMessageReceived: (data: any) => ({
    subject: `Nueva consulta sobre ${data.propertyTitle}`,
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Nueva Consulta Web</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #7c3aed; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #faf5ff; padding: 30px; border-radius: 0 0 8px 8px; }
            .property-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #7c3aed; }
            .message-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; }
            .lead-badge { display: inline-block; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; }
            .inquiry { background: #dbeafe; color: #1e40af; }
            .viewing { background: #d1fae5; color: #059669; }
            .offer { background: #fef3c7; color: #d97706; }
            .negotiation { background: #fce7f3; color: #be185d; }
            .button { display: inline-block; background: #7c3aed; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; }
            .footer { text-align: center; margin-top: 30px; color: #64748b; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>💬 Nueva Consulta Web</h1>
            </div>
            <div class="content">
              <p>¡Hola! Has recibido una nueva consulta desde inmo.gt</p>
              
              <div class="property-card">
                <h3>🏠 ${data.propertyTitle}</h3>
                <p><strong>Ubicación:</strong> ${data.propertyAddress}</p>
                <p><strong>Precio:</strong> ${data.propertyPrice}</p>
                <span class="lead-badge ${data.leadType}">${data.leadTypeName}</span>
              </div>

              <div class="message-card">
                <h3>👤 Información del Cliente</h3>
                <p><strong>Nombre:</strong> ${data.senderName}</p>
                <p><strong>Email:</strong> ${data.senderEmail}</p>
                ${data.senderPhone ? `<p><strong>Teléfono:</strong> ${data.senderPhone}</p>` : ''}
                <p><strong>Asunto:</strong> ${data.subject}</p>
                
                <div style="background: #f8fafc; padding: 15px; border-radius: 6px; margin: 15px 0;">
                  <strong>Mensaje:</strong><br/>
                  ${data.message}
                </div>
                
                <p><strong>Enviado:</strong> ${data.createdAt}</p>
              </div>

              <div style="text-align: center; margin: 30px 0;">
                <a href="${data.dashboardUrl}" class="button">Responder Consulta</a>
              </div>

              <p><strong>💡 Consejo:</strong> Responde rápidamente para aumentar tus posibilidades de cerrar la venta. Los clientes valoran la atención inmediata.</p>
            </div>
            <div class="footer">
              <p>Este email fue enviado por <strong>Inmo.gt</strong></p>
            </div>
          </div>
        </body>
      </html>
    `
  }),

  // 📧 Confirmación de mensaje enviado (para compradores)
  messageConfirmation: (data: any) => ({
    subject: `✅ Tu consulta sobre ${data.propertyTitle} ha sido enviada`,
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Consulta Enviada</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #10b981; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #f8fafc; padding: 30px; border-radius: 0 0 8px 8px; }
            .property-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #10b981; }
            .message-summary { background: #ecfdf5; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #10b981; }
            .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; color: #6b7280; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>✅ ¡Consulta Enviada!</h1>
              <p>Tu mensaje ha sido enviado exitosamente</p>
            </div>

            <div class="content">
              <p>Hola <strong>${data.senderName}</strong>,</p>

              <p>Tu consulta sobre la propiedad ha sido enviada exitosamente al propietario. Te responderán a la brevedad posible.</p>

              <div class="property-card">
                <h2>${data.propertyTitle}</h2>
                <p><strong>📍 Ubicación:</strong> ${data.propertyAddress}</p>
                <p><strong>💰 Precio:</strong> ${data.propertyPrice}</p>
                <p><strong>📝 Asunto:</strong> ${data.subject}</p>
              </div>

              <div class="message-summary">
                <h3>📋 Resumen de tu consulta:</h3>
                <div style="background: white; padding: 15px; border-radius: 6px;">
                  <p style="margin: 0; white-space: pre-wrap;">${data.message}</p>
                </div>
              </div>

              <div style="background: #dbeafe; padding: 15px; border-radius: 8px; border-left: 4px solid #3b82f6;">
                <p style="margin: 0;"><strong>📧 Próximos pasos:</strong> El propietario recibirá tu consulta y te responderá directamente a tu email: <strong>${data.senderEmail}</strong></p>
              </div>

              <div style="background: #fef3c7; padding: 15px; border-radius: 8px; border-left: 4px solid #f59e0b; margin-top: 15px;">
                <p style="margin: 0;"><strong>💡 Consejo:</strong> Mantén tu email activo para recibir la respuesta. Si no recibes respuesta en 24-48 horas, puedes enviar otra consulta.</p>
              </div>
            </div>

            <div class="footer">
              <p>¿Necesitas ayuda? Contáctanos en <a href="mailto:<EMAIL>"><EMAIL></a></p>
              <p>© 2024 Inmova.gt - Tu plataforma inmobiliaria de confianza</p>
            </div>
          </div>
        </body>
      </html>
    `
  }),

  // 📧 Recordatorio de cita (24 horas antes)
  appointmentReminder24h: (data: any) => ({
    subject: `🔔 Recordatorio: Cita mañana - ${data.propertyTitle}`,
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Recordatorio de Cita</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #f59e0b; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #fefbf3; padding: 30px; border-radius: 0 0 8px 8px; }
            .appointment-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b; }
            .highlight { background: #fef3c7; padding: 15px; border-radius: 8px; margin: 15px 0; }
            .button { display: inline-block; background: #f59e0b; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 5px; }
            .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; color: #6b7280; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🔔 Recordatorio de Cita</h1>
              <p>Tu cita es mañana</p>
            </div>

            <div class="content">
              <p>Hola <strong>${data.participantName}</strong>,</p>

              <p>Te recordamos que tienes una cita programada para <strong>mañana</strong>.</p>

              <div class="appointment-card">
                <h2>📅 Detalles de la Cita</h2>
                <p><strong>🏠 Propiedad:</strong> ${data.propertyTitle}</p>
                <p><strong>📍 Ubicación:</strong> ${data.propertyAddress}</p>
                <p><strong>📅 Fecha:</strong> ${data.appointmentDate}</p>
                <p><strong>🕐 Hora:</strong> ${data.appointmentTime}</p>
                <p><strong>👥 Tipo:</strong> ${data.meetingType === 'in_person' ? 'Presencial' : data.meetingType === 'video_call' ? 'Videollamada' : 'Llamada telefónica'}</p>
                ${data.location ? `<p><strong>📍 Lugar:</strong> ${data.location}</p>` : ''}
                ${data.meetingUrl ? `<p><strong>🔗 Enlace:</strong> <a href="${data.meetingUrl}">${data.meetingUrl}</a></p>` : ''}
              </div>

              <div class="highlight">
                <p><strong>👤 Te reunirás con:</strong> ${data.otherParticipantName}</p>
                ${data.otherParticipantPhone ? `<p><strong>📞 Teléfono:</strong> ${data.otherParticipantPhone}</p>` : ''}
                <p><strong>📧 Email:</strong> ${data.otherParticipantEmail}</p>
              </div>

              <div style="text-align: center; margin: 30px 0;">
                <a href="${data.dashboardUrl}" class="button">Ver Detalles</a>
                ${data.meetingUrl ? `<a href="${data.meetingUrl}" class="button">Unirse a Videollamada</a>` : ''}
              </div>

              <div style="background: #dbeafe; padding: 15px; border-radius: 8px; border-left: 4px solid #3b82f6;">
                <p style="margin: 0;"><strong>💡 Consejos:</strong></p>
                <ul style="margin: 10px 0;">
                  <li>Llega 5-10 minutos antes</li>
                  <li>Prepara tus preguntas con anticipación</li>
                  <li>Lleva identificación si es necesario</li>
                </ul>
              </div>
            </div>

            <div class="footer">
              <p>¿Necesitas reprogramar? Contáctanos en <a href="mailto:<EMAIL>"><EMAIL></a></p>
              <p>© 2024 Inmova.gt - Tu plataforma inmobiliaria de confianza</p>
            </div>
          </div>
        </body>
      </html>
    `
  }),

  // 📧 Recordatorio de cita (1 hora antes)
  appointmentReminder1h: (data: any) => ({
    subject: `⏰ ¡Tu cita es en 1 hora! - ${data.propertyTitle}`,
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Cita en 1 Hora</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #dc2626; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #fef2f2; padding: 30px; border-radius: 0 0 8px 8px; }
            .urgent-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #dc2626; }
            .countdown { background: #fee2e2; padding: 20px; border-radius: 8px; text-align: center; margin: 20px 0; }
            .button { display: inline-block; background: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 5px; font-weight: bold; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>⏰ ¡Cita en 1 Hora!</h1>
              <p>Es hora de prepararse</p>
            </div>

            <div class="content">
              <div class="countdown">
                <h2 style="color: #dc2626; margin: 0;">🚨 TU CITA ES EN 1 HORA 🚨</h2>
              </div>

              <p>Hola <strong>${data.participantName}</strong>,</p>

              <p>Tu cita programada comenzará en <strong>1 hora</strong>. ¡Es momento de prepararse!</p>

              <div class="urgent-card">
                <h3>📋 Información Rápida</h3>
                <p><strong>🏠 Propiedad:</strong> ${data.propertyTitle}</p>
                <p><strong>🕐 Hora:</strong> ${data.appointmentTime}</p>
                <p><strong>👥 Con:</strong> ${data.otherParticipantName}</p>
                ${data.location ? `<p><strong>📍 Lugar:</strong> ${data.location}</p>` : ''}
                ${data.meetingUrl ? `<p><strong>🔗 Enlace:</strong> <a href="${data.meetingUrl}" style="color: #dc2626; font-weight: bold;">${data.meetingUrl}</a></p>` : ''}
              </div>

              <div style="text-align: center; margin: 30px 0;">
                ${data.meetingUrl ? `<a href="${data.meetingUrl}" class="button">🎥 UNIRSE AHORA</a>` : ''}
                <a href="tel:${data.otherParticipantPhone || ''}" class="button">📞 LLAMAR</a>
              </div>

              <div style="background: #fef3c7; padding: 15px; border-radius: 8px; border-left: 4px solid #f59e0b;">
                <p style="margin: 0;"><strong>⚡ Último momento:</strong> Asegúrate de tener todo listo. ¡Nos vemos pronto!</p>
              </div>
            </div>
          </div>
        </body>
      </html>
    `
  }),

  // 📧 Notificación de propiedad agregada a favoritos
  propertyAddedToFavorites: (data: any) => ({
    subject: `⭐ Tu propiedad fue agregada a favoritos - ${data.propertyTitle}`,
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Propiedad en Favoritos</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #10b981; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #f0fdf4; padding: 30px; border-radius: 0 0 8px 8px; }
            .property-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #10b981; }
            .buyer-info { background: #ecfdf5; padding: 15px; border-radius: 8px; margin: 15px 0; }
            .stats { background: #f3f4f6; padding: 15px; border-radius: 8px; margin: 15px 0; }
            .button { display: inline-block; background: #10b981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
            .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; color: #6b7280; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>⭐ ¡Excelente Noticia!</h1>
              <p>Tu propiedad despertó interés</p>
            </div>

            <div class="content">
              <p>Hola <strong>${data.ownerName}</strong>,</p>

              <p>¡Tenemos buenas noticias! Un comprador potencial ha agregado tu propiedad a su lista de favoritos.</p>

              <div class="property-card">
                <h2>🏠 ${data.propertyTitle}</h2>
                <p><strong>📍 Ubicación:</strong> ${data.propertyAddress}</p>
                <p><strong>💰 Precio:</strong> ${data.propertyPrice}</p>
              </div>

              <div class="buyer-info">
                <h3>👤 Comprador Interesado</h3>
                <p><strong>Nombre:</strong> ${data.buyerName}</p>
                <p><strong>Email:</strong> ${data.buyerEmail}</p>
                ${data.buyerPhone ? `<p><strong>Teléfono:</strong> ${data.buyerPhone}</p>` : ''}
                <p><strong>Fecha:</strong> ${data.favoriteDate}</p>
              </div>

              <div class="stats">
                <h3>📊 Estadísticas de tu Propiedad</h3>
                <p><strong>Total de favoritos:</strong> ${data.totalFavorites}</p>
                <p><strong>Vistas totales:</strong> ${data.totalViews || 'N/A'}</p>
                <p><strong>Consultas recibidas:</strong> ${data.totalInquiries || 0}</p>
              </div>

              <div style="text-align: center; margin: 30px 0;">
                <a href="${data.propertyUrl}" class="button">Ver Propiedad</a>
                <a href="${data.dashboardUrl}" class="button">Ver Dashboard</a>
              </div>

              <div style="background: #fef3c7; padding: 15px; border-radius: 8px; border-left: 4px solid #f59e0b;">
                <p style="margin: 0;"><strong>💡 Consejo:</strong> Los compradores que agregan propiedades a favoritos están seriamente interesados. ¡Es un buen momento para contactarlos directamente!</p>
              </div>
            </div>

            <div class="footer">
              <p>¿Quieres optimizar tu propiedad? Contáctanos en <a href="mailto:<EMAIL>"><EMAIL></a></p>
              <p>© 2024 Inmova.gt - Tu plataforma inmobiliaria de confianza</p>
            </div>
          </div>
        </body>
      </html>
    `
  }),

  // 📧 Suscripción por vencer (7 días antes)
  subscriptionExpiring: (data: any) => ({
    subject: `⚠️ Tu suscripción ${data.planName} vence en ${data.daysLeft} días`,
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Suscripción por Vencer</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #f59e0b; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #fefbf3; padding: 30px; border-radius: 0 0 8px 8px; }
            .warning-card { background: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b; }
            .plan-info { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; }
            .benefits { background: #f0fdf4; padding: 15px; border-radius: 8px; margin: 15px 0; }
            .button { display: inline-block; background: #f59e0b; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 5px; font-weight: bold; }
            .button-primary { background: #10b981; }
            .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; color: #6b7280; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>⚠️ Suscripción por Vencer</h1>
              <p>No pierdas el acceso a tus beneficios</p>
            </div>

            <div class="content">
              <p>Hola <strong>${data.userName}</strong>,</p>

              <div class="warning-card">
                <h2 style="color: #f59e0b; margin-top: 0;">🚨 Tu suscripción ${data.planName} vence en ${data.daysLeft} días</h2>
                <p><strong>Fecha de vencimiento:</strong> ${data.expirationDate}</p>
              </div>

              <div class="plan-info">
                <h3>📋 Tu Plan Actual</h3>
                <p><strong>Plan:</strong> ${data.planName}</p>
                <p><strong>Precio:</strong> ${data.planPrice}</p>
                <p><strong>Propiedades activas:</strong> ${data.activeProperties}</p>
                <p><strong>Créditos restantes:</strong> ${data.remainingCredits}</p>
              </div>

              <div class="benefits">
                <h3>✨ Beneficios que perderás:</h3>
                <ul>
                  <li>Publicación ilimitada de propiedades</li>
                  <li>Posicionamiento premium</li>
                  <li>Estadísticas avanzadas</li>
                  <li>Soporte prioritario</li>
                  <li>Herramientas de marketing</li>
                </ul>
              </div>

              <div style="text-align: center; margin: 30px 0;">
                <a href="${data.renewUrl}" class="button button-primary">🔄 RENOVAR AHORA</a>
                <a href="${data.dashboardUrl}" class="button">Ver Dashboard</a>
              </div>

              <div style="background: #dbeafe; padding: 15px; border-radius: 8px; border-left: 4px solid #3b82f6;">
                <p style="margin: 0;"><strong>💰 Oferta especial:</strong> Renueva ahora y obtén un 10% de descuento en tu próximo mes. ¡No dejes que tus propiedades pierdan visibilidad!</p>
              </div>
            </div>

            <div class="footer">
              <p>¿Necesitas ayuda? Contáctanos en <a href="mailto:<EMAIL>"><EMAIL></a></p>
              <p>© 2024 Inmova.gt - Tu plataforma inmobiliaria de confianza</p>
            </div>
          </div>
        </body>
      </html>
    `
  }),

  // 📧 Trial por vencer (3 días antes)
  trialExpiring: (data: any) => ({
    subject: `🎯 Tu prueba gratuita termina en ${data.daysLeft} días - ¡Actualiza ahora!`,
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Trial por Vencer</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #7c3aed; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #faf5ff; padding: 30px; border-radius: 0 0 8px 8px; }
            .trial-card { background: #ede9fe; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #7c3aed; }
            .stats-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; }
            .upgrade-offer { background: #10b981; color: white; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center; }
            .button { display: inline-block; background: #7c3aed; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 5px; font-weight: bold; }
            .button-success { background: #10b981; }
            .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; color: #6b7280; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🎯 ¡Tu Trial Está por Terminar!</h1>
              <p>Es momento de decidir</p>
            </div>

            <div class="content">
              <p>Hola <strong>${data.userName}</strong>,</p>

              <div class="trial-card">
                <h2 style="color: #7c3aed; margin-top: 0;">⏰ Tu prueba gratuita termina en ${data.daysLeft} días</h2>
                <p><strong>Fecha de finalización:</strong> ${data.trialEndDate}</p>
                <p><strong>Tiempo restante:</strong> ${data.hoursLeft} horas</p>
              </div>

              <div class="stats-card">
                <h3>📊 Tu Actividad Durante el Trial</h3>
                <p><strong>Propiedades publicadas:</strong> ${data.propertiesPublished}</p>
                <p><strong>Consultas recibidas:</strong> ${data.inquiriesReceived}</p>
                <p><strong>Vistas totales:</strong> ${data.totalViews}</p>
                <p><strong>Favoritos obtenidos:</strong> ${data.favoritesReceived}</p>
              </div>

              <div class="upgrade-offer">
                <h3 style="margin-top: 0;">🚀 ¡Oferta Especial de Conversión!</h3>
                <p style="font-size: 18px; margin: 15px 0;"><strong>50% de descuento en tu primer mes</strong></p>
                <p>Solo por ser usuario de prueba</p>
              </div>

              <div style="text-align: center; margin: 30px 0;">
                <a href="${data.upgradeUrl}" class="button button-success">🎉 ACTUALIZAR CON 50% OFF</a>
                <a href="${data.dashboardUrl}" class="button">Ver Dashboard</a>
              </div>

              <div style="background: #fee2e2; padding: 15px; border-radius: 8px; border-left: 4px solid #dc2626;">
                <p style="margin: 0;"><strong>⚠️ Importante:</strong> Después del trial, tus propiedades se ocultarán hasta que actualices tu plan. ¡No pierdas tus leads potenciales!</p>
              </div>

              <div style="background: #f0fdf4; padding: 15px; border-radius: 8px; border-left: 4px solid #10b981; margin-top: 15px;">
                <p style="margin: 0;"><strong>✨ Con el plan Premium obtienes:</strong> Propiedades ilimitadas, posicionamiento destacado, estadísticas avanzadas y soporte prioritario.</p>
              </div>
            </div>

            <div class="footer">
              <p>¿Tienes preguntas? Contáctanos en <a href="mailto:<EMAIL>"><EMAIL></a></p>
              <p>© 2024 Inmova.gt - Tu plataforma inmobiliaria de confianza</p>
            </div>
          </div>
        </body>
      </html>
    `
  }),

  // 📧 NUEVO: Notificación de créditos agotados
  lowCreditsWarning: (data: any) => ({
    subject: `⚠️ Créditos agotados - Actualiza tu plan`,
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Créditos Agotados</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #dc2626; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #fef2f2; padding: 30px; border-radius: 0 0 8px 8px; }
            .alert-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 2px solid #dc2626; }
            .upgrade-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #059669; }
            .button { display: inline-block; background: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 5px; }
            .button-green { background: #059669; }
            .footer { text-align: center; margin-top: 30px; color: #64748b; font-size: 14px; }
            .features { list-style: none; padding: 0; }
            .features li { padding: 8px 0; }
            .features li:before { content: "✅ "; color: #059669; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>⚠️ Créditos Agotados</h1>
            </div>
            <div class="content">
              <p>Hola <strong>${data.userName}</strong>,</p>
              
              <div class="alert-card">
                <h3>🚨 Has agotado tus créditos</h3>
                <p><strong>Plan actual:</strong> ${data.currentPlan}</p>
                <p><strong>Créditos usados:</strong> ${data.creditsUsed} de ${data.totalCredits}</p>
                <p><strong>Créditos restantes:</strong> <span style="color: #dc2626; font-weight: bold;">0</span></p>
                
                <div style="background: #fee2e2; padding: 15px; border-radius: 6px; margin: 15px 0;">
                  <strong>⚠️ Limitaciones actuales:</strong>
                  <ul style="margin: 10px 0 0 20px;">
                    <li>No puedes recibir más consultas</li>
                    <li>No puedes destacar propiedades</li>
                    <li>Funciones premium deshabilitadas</li>
                  </ul>
                </div>
              </div>

              <div class="upgrade-card">
                <h3>🚀 Soluciones Disponibles</h3>
                
                <p><strong>Plan Pro - $19/mes</strong></p>
                <ul class="features">
                  <li>100 créditos mensuales</li>
                  <li>20 propiedades máximo</li>
                  <li>Soporte prioritario</li>
                </ul>
                
                <p><strong>Plan Premium - $49/mes</strong></p>
                <ul class="features">
                  <li>Créditos ilimitados</li>
                  <li>Propiedades ilimitadas</li>
                  <li>Analytics avanzados</li>
                  <li>Soporte VIP</li>
                </ul>
              </div>

              <div style="text-align: center; margin: 30px 0;">
                <a href="${data.upgradeUrl}" class="button button-green">Actualizar Plan Ahora</a>
                <a href="${data.dashboardUrl}" class="button">Ver Mi Cuenta</a>
              </div>

              <p><strong>💰 ¿No quieres actualizar?</strong> Puedes esperar hasta el próximo mes para que se renueven tus créditos, pero perderás oportunidades de negocio.</p>
            </div>
            <div class="footer">
              <p>¡No pierdas clientes! - <strong>Inmo.gt</strong></p>
            </div>
          </div>
        </body>
             </html>
     `
   }),

   // 📧 NUEVO: Notificación de posición destacada vencida
   featuredPositionExpired: (data: any) => ({
     subject: `Tu posición destacada ha vencido - ${data.propertyTitle}`,
     html: `
       <!DOCTYPE html>
       <html>
         <head>
           <meta charset="utf-8">
           <title>Posición Destacada Vencida</title>
           <style>
             body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; line-height: 1.6; color: #333; }
             .container { max-width: 600px; margin: 0 auto; padding: 20px; }
             .header { background: #f59e0b; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
             .content { background: #fffbeb; padding: 30px; border-radius: 0 0 8px 8px; }
             .property-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b; }
             .renewal-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 2px solid #10b981; }
             .button { display: inline-block; background: #f59e0b; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 5px; }
             .button-green { background: #10b981; }
             .footer { text-align: center; margin-top: 30px; color: #64748b; font-size: 14px; }
           </style>
         </head>
         <body>
           <div class="container">
             <div class="header">
               <h1>⏰ Posición Destacada Vencida</h1>
             </div>
             <div class="content">
               <p>Hola <strong>${data.userName}</strong>,</p>
               
               <div class="property-card">
                 <h3>🏠 ${data.propertyTitle}</h3>
                 <p><strong>Ubicación:</strong> ${data.propertyAddress}</p>
                 <p><strong>Precio:</strong> ${data.propertyPrice}</p>
                 <p><strong>Período destacado:</strong> ${data.featuredDuration} días</p>
                 <p><strong>Vencido el:</strong> ${data.expiredDate}</p>
               </div>

               <div style="background: #fef3c7; padding: 15px; border-radius: 6px; margin: 15px 0;">
                 <p><strong>⚠️ Tu propiedad ya no aparece en la sección destacada</strong></p>
                 <p>Esto significa menos visibilidad y posibles menos consultas.</p>
               </div>

               <div class="renewal-card">
                 <h3>🚀 ¿Quieres renovar la posición destacada?</h3>
                 <p><strong>Beneficios de destacar tu propiedad:</strong></p>
                 <ul>
                   <li>✅ Mayor visibilidad en la página principal</li>
                   <li>✅ Aparece en los primeros resultados</li>
                   <li>✅ Hasta 5x más consultas</li>
                   <li>✅ Vende más rápido</li>
                 </ul>
                 <p><strong>Costo:</strong> Solo 10 créditos por 7 días más</p>
               </div>

               <div style="text-align: center; margin: 30px 0;">
                 <a href="${data.renewUrl}" class="button button-green">Renovar Ahora</a>
                 <a href="${data.dashboardUrl}" class="button">Ver Mi Dashboard</a>
               </div>

               <p><strong>💡 Tip:</strong> Las propiedades destacadas reciben 5 veces más consultas que las normales.</p>
             </div>
             <div class="footer">
               <p>¡Mantén tu propiedad visible! - <strong>Inmo.gt</strong></p>
             </div>
           </div>
         </body>
       </html>
     `
   }),

   // 📧 NUEVO: Notificación de posición premium vencida
   premiumPositionExpired: (data: any) => ({
     subject: `Tu posición premium ha vencido - ${data.propertyTitle}`,
     html: `
       <!DOCTYPE html>
       <html>
         <head>
           <meta charset="utf-8">
           <title>Posición Premium Vencida</title>
           <style>
             body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; line-height: 1.6; color: #333; }
             .container { max-width: 600px; margin: 0 auto; padding: 20px; }
             .header { background: #dc2626; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
             .content { background: #fef2f2; padding: 30px; border-radius: 0 0 8px 8px; }
             .property-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #dc2626; }
             .premium-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 2px solid #7c3aed; }
             .button { display: inline-block; background: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 5px; }
             .button-purple { background: #7c3aed; }
             .footer { text-align: center; margin-top: 30px; color: #64748b; font-size: 14px; }
           </style>
         </head>
         <body>
           <div class="container">
             <div class="header">
               <h1>👑 Posición Premium Vencida</h1>
             </div>
             <div class="content">
               <p>Hola <strong>${data.userName}</strong>,</p>
               
               <div class="property-card">
                 <h3>🏠 ${data.propertyTitle}</h3>
                 <p><strong>Ubicación:</strong> ${data.propertyAddress}</p>
                 <p><strong>Precio:</strong> ${data.propertyPrice}</p>
                 <p><strong>Período premium:</strong> ${data.premiumDuration} días</p>
                 <p><strong>Vencido el:</strong> ${data.expiredDate}</p>
               </div>

               <div style="background: #fee2e2; padding: 15px; border-radius: 6px; margin: 15px 0;">
                 <p><strong>⚠️ Tu propiedad ya no está en la posición premium del home</strong></p>
                 <p>Perdiste la máxima visibilidad posible en la plataforma.</p>
               </div>

               <div class="premium-card">
                 <h3>👑 ¿Quieres renovar la posición premium?</h3>
                 <p><strong>Beneficios exclusivos de Premium Home:</strong></p>
                 <ul>
                   <li>🌟 Posición #1 en la página principal</li>
                   <li>🚀 Primera propiedad que ven todos los visitantes</li>
                   <li>📈 Hasta 10x más consultas</li>
                   <li>⚡ Venta ultra rápida garantizada</li>
                   <li>💎 Badge premium visible</li>
                 </ul>
                 <p><strong>Costo:</strong> Solo 25 créditos por 7 días más</p>
                 <p><em>Disponibilidad limitada: ${data.availableSlots ? 'Posición disponible' : 'Posición ocupada por otra propiedad'}</em></p>
               </div>

               <div style="text-align: center; margin: 30px 0;">
                 <a href="${data.renewUrl}" class="button button-purple">Renovar Premium</a>
                 <a href="${data.dashboardUrl}" class="button">Ver Dashboard</a>
               </div>

               <p><strong>🔥 ¡Acción limitada!</strong> Solo hay 1 posición premium disponible. Renueva ahora antes que la tome otra propiedad.</p>
             </div>
             <div class="footer">
               <p>¡Mantén tu posición #1! - <strong>Inmo.gt</strong></p>
             </div>
           </div>
         </body>
       </html>
     `
   }),

   // 📧 NUEVO: Respuesta a mensaje web
   messageReply: (data: any) => ({
     subject: `Respuesta a tu consulta sobre ${data.propertyTitle}`,
     html: `
       <!DOCTYPE html>
       <html>
         <head>
           <meta charset="utf-8">
           <title>Respuesta a tu Consulta</title>
           <style>
             body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; line-height: 1.6; color: #333; }
             .container { max-width: 600px; margin: 0 auto; padding: 20px; }
             .header { background: #059669; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
             .content { background: #f0fdf4; padding: 30px; border-radius: 0 0 8px 8px; }
             .property-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #059669; }
             .message-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #d1fae5; }
             .reply-card { background: #ecfdf5; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #10b981; }
             .button { display: inline-block; background: #059669; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; margin: 10px 5px; }
             .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
           </style>
         </head>
         <body>
           <div class="container">
             <div class="header">
               <h1>💬 Respuesta a tu Consulta</h1>
               <p>El propietario ha respondido tu mensaje</p>
             </div>
             <div class="content">
               <p>Hola <strong>${data.clientName}</strong>,</p>

               <p>El propietario <strong>${data.ownerName}</strong> ha respondido a tu consulta sobre la siguiente propiedad:</p>

               <div class="property-card">
                 <h3>🏠 ${data.propertyTitle}</h3>
                 <p><strong>📍 Ubicación:</strong> ${data.propertyAddress}</p>
                 <p><strong>💰 Precio:</strong> ${data.propertyPrice}</p>
               </div>

               <div class="message-card">
                 <h3>📝 Tu mensaje original:</h3>
                 <p><strong>Asunto:</strong> ${data.originalSubject}</p>
                 <div style="background: #f8fafc; padding: 15px; border-radius: 6px; margin: 10px 0;">
                   ${data.originalMessage}
                 </div>
               </div>

               <div class="reply-card">
                 <h3>💬 Respuesta del propietario:</h3>
                 <div style="background: white; padding: 15px; border-radius: 6px; margin: 10px 0;">
                   ${data.replyMessage}
                 </div>
                 <p><strong>Respondido el:</strong> ${data.replyDate}</p>
               </div>

               ${data.ownerPhone ? `
                 <div style="background: #dbeafe; padding: 15px; border-radius: 6px; margin: 15px 0;">
                   <h3>📞 Información de Contacto</h3>
                   <p><strong>Propietario:</strong> ${data.ownerName}</p>
                   <p><strong>Email:</strong> ${data.ownerEmail}</p>
                   <p><strong>Teléfono:</strong> ${data.ownerPhone}</p>
                 </div>
               ` : ''}

               <div style="text-align: center; margin: 30px 0;">
                 <a href="mailto:${data.ownerEmail}?subject=Re: ${data.originalSubject}" class="button">Responder por Email</a>
                 ${data.propertyUrl ? `<a href="${data.propertyUrl}" class="button" style="background: #6b7280;">Ver Propiedad</a>` : ''}
               </div>

               <p style="color: #666; font-size: 14px;">
                 💡 <strong>Tip:</strong> Puedes responder directamente a este email o usar el botón de arriba para continuar la conversación.
               </p>
             </div>
             <div class="footer">
               <p>¡Encuentra tu hogar ideal! - <strong>Inmova.gt</strong></p>
             </div>
           </div>
         </body>
       </html>
     `
   }),

   // 📧 NUEVO: Notificación de cita rechazada
   appointmentRejected: (data: any) => ({
     subject: `Solicitud de cita no aprobada - ${data.propertyTitle}`,
     html: `
       <!DOCTYPE html>
       <html>
         <head>
           <meta charset="utf-8">
           <title>Solicitud de Cita No Aprobada</title>
           <style>
             body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; line-height: 1.6; color: #333; }
             .container { max-width: 600px; margin: 0 auto; padding: 20px; }
             .header { background: #dc2626; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
             .content { background: #fef2f2; padding: 30px; border-radius: 0 0 8px 8px; }
             .property-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #dc2626; }
             .appointment-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #fecaca; }
             .highlight { background: #fee2e2; padding: 15px; border-radius: 6px; margin: 15px 0; }
             .button { display: inline-block; background: #059669; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; margin: 10px 5px; }
             .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
           </style>
         </head>
         <body>
           <div class="container">
             <div class="header">
               <h1>❌ Solicitud No Aprobada</h1>
               <p>Tu solicitud de cita no pudo ser aprobada</p>
             </div>
             <div class="content">
               <p>Hola <strong>${data.guestName}</strong>,</p>

               <p>Lamentamos informarte que tu solicitud de cita para la siguiente propiedad no pudo ser aprobada:</p>

               <div class="property-card">
                 <h3>🏠 ${data.propertyTitle}</h3>
                 <p><strong>📍 Ubicación:</strong> ${data.propertyAddress}</p>
                 <p><strong>💰 Precio:</strong> ${data.propertyPrice}</p>
               </div>

               <div class="appointment-card">
                 <h3>📅 Detalles de la solicitud:</h3>
                 <p><strong>Fecha solicitada:</strong> ${data.requestedDate}</p>
                 <p><strong>Hora solicitada:</strong> ${data.requestedTime}</p>
                 <p><strong>Tipo de reunión:</strong> ${data.meetingType === 'in_person' ? 'Presencial' : data.meetingType === 'virtual' ? 'Virtual' : data.meetingType}</p>
               </div>

               ${data.response ? `
                 <div class="highlight">
                   <h3>💬 Mensaje del propietario:</h3>
                   <p>${data.response}</p>
                 </div>
               ` : ''}

               ${data.proposedStartTime && data.proposedEndTime ? `
                 <div style="background: #e0f2fe; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #0288d1;">
                   <h3 style="color: #0277bd; margin: 0 0 15px 0;">📅 Nueva fecha propuesta:</h3>
                   <div style="background: white; padding: 15px; border-radius: 6px;">
                     <p style="margin: 5px 0;"><strong>📅 Fecha:</strong> ${data.proposedDate}</p>
                     <p style="margin: 5px 0;"><strong>🕐 Hora:</strong> ${data.proposedTimeRange}</p>
                     ${data.proposedLocation ? `<p style="margin: 5px 0;"><strong>📍 Ubicación:</strong> ${data.proposedLocation}</p>` : ''}
                   </div>
                   <p style="margin: 15px 0 0 0; font-size: 14px; color: #0277bd;">
                     💡 <strong>Tip:</strong> Puedes responder a este email o contactar directamente al propietario para confirmar esta nueva fecha.
                   </p>
                 </div>
               ` : ''}

               <div style="background: #dbeafe; padding: 15px; border-radius: 6px; margin: 15px 0;">
                 <h3>🤝 ¿Qué puedes hacer ahora?</h3>
                 <ul>
                   <li>Contactar directamente al propietario para coordinar otra fecha</li>
                   <li>Solicitar una nueva cita con fechas alternativas</li>
                   <li>Explorar otras propiedades similares</li>
                 </ul>
               </div>

               <div style="background: #f3f4f6; padding: 15px; border-radius: 6px; margin: 15px 0;">
                 <h3>📞 Información de Contacto</h3>
                 <p><strong>Propietario:</strong> ${data.ownerName}</p>
                 <p><strong>Email:</strong> ${data.ownerEmail}</p>
                 ${data.ownerPhone ? `<p><strong>Teléfono:</strong> ${data.ownerPhone}</p>` : ''}
               </div>

               <div style="text-align: center; margin: 30px 0;">
                 <a href="mailto:${data.ownerEmail}?subject=Nueva solicitud de cita - ${data.propertyTitle}" class="button">Contactar Propietario</a>
                 ${data.propertyUrl ? `<a href="${data.propertyUrl}" class="button" style="background: #6b7280;">Ver Propiedad</a>` : ''}
               </div>

               <p style="color: #666; font-size: 14px;">
                 💡 <strong>Tip:</strong> Los propietarios suelen tener horarios específicos. Intenta proponer varias opciones de fecha y hora.
               </p>
             </div>
             <div class="footer">
               <p>¡No te rindas, encuentra tu hogar ideal! - <strong>Inmova.gt</strong></p>
             </div>
           </div>
         </body>
       </html>
     `
   }),

   // 📧 DUPLICADO ELIMINADO: webMessageReceived ya existe arriba
   // webMessageReceived: (data: any) => ({
     // DUPLICADO ELIMINADO - Ver función original arriba
     /*
     subject: `💬 Nueva consulta sobre ${data.propertyTitle}`,
     html: `...`
     */
   // }),

   // 📧 DUPLICADO ELIMINADO: messageReply ya existe arriba
   // messageReply: (data: any) => ({
     // DUPLICADO ELIMINADO - Ver función original arriba
     /*
     subject: `✅ Respuesta sobre ${data.propertyTitle}`,
     html: `...`
     */
   // }),

  // 📧 NUEVO: Notificación de cuenta eliminada completamente
  accountDeleted: (data: any) => ({
    subject: `🔒 Cuenta eliminada - Confirmación de Inmova.gt`,
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Cuenta Eliminada</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #dc2626; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #fef2f2; padding: 30px; border-radius: 0 0 8px 8px; }
            .info-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #dc2626; }
            .stats-card { background: #f9fafb; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #e5e7eb; }
            .warning-box { background: #fef3c7; padding: 15px; border-radius: 8px; border-left: 4px solid #f59e0b; margin: 20px 0; }
            .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; color: #6b7280; font-size: 14px; }
            .highlight { background: #fee2e2; padding: 15px; border-radius: 6px; margin: 15px 0; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🔒 Cuenta Eliminada</h1>
              <p>Confirmación de eliminación completa</p>
            </div>
            <div class="content">
              <p>Hola <strong>${data.userName}</strong>,</p>

              <p>Confirmamos que tu cuenta de Inmova.gt ha sido <strong>eliminada completamente</strong> según tu solicitud.</p>

              <div class="info-card">
                <h3>📋 Resumen de Eliminación</h3>
                <p><strong>📧 Email:</strong> ${data.userEmail}</p>
                <p><strong>🗓️ Fecha de eliminación:</strong> ${new Date(data.deletionDate).toLocaleDateString('es-ES', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}</p>
                <p><strong>⏰ Hora:</strong> ${new Date(data.deletionDate).toLocaleTimeString('es-ES')}</p>
              </div>

              ${data.deletedCount && data.totalDeleted > 0 ? `
              <div class="stats-card">
                <h3>📊 Datos Eliminados</h3>
                <p>Se eliminaron un total de <strong>${data.totalDeleted}</strong> registros:</p>
                <ul>
                  ${data.deletedCount.properties > 0 ? `<li>🏠 <strong>${data.deletedCount.properties}</strong> propiedades</li>` : ''}
                  ${data.deletedCount.favorites > 0 ? `<li>❤️ <strong>${data.deletedCount.favorites}</strong> favoritos</li>` : ''}
                  ${data.deletedCount.messages > 0 ? `<li>💬 <strong>${data.deletedCount.messages}</strong> mensajes</li>` : ''}
                  ${data.deletedCount.appointments > 0 ? `<li>📅 <strong>${data.deletedCount.appointments}</strong> citas</li>` : ''}
                  ${data.deletedCount.transactions > 0 ? `<li>💳 <strong>${data.deletedCount.transactions}</strong> transacciones</li>` : ''}
                </ul>
              </div>
              ` : ''}

              <div class="highlight">
                <h4>🔐 ¿Qué se eliminó?</h4>
                <ul>
                  <li><strong>✅ Todos tus datos</strong> de la plataforma Inmova</li>
                  <li><strong>✅ Tu cuenta de autenticación</strong> (email y contraseña)</li>
                  <li><strong>✅ Toda tu información personal</strong></li>
                  <li><strong>✅ Historial de actividad</strong> y transacciones</li>
                </ul>
              </div>

              <div class="warning-box">
                <h4>⚠️ Importante</h4>
                <p><strong>Esta eliminación es permanente e irreversible.</strong> No podemos recuperar tu cuenta ni tus datos.</p>
                <p>Si deseas volver a usar Inmova.gt en el futuro, deberás crear una cuenta completamente nueva.</p>
              </div>

              <div style="background: #f0f9ff; padding: 15px; border-radius: 8px; border-left: 4px solid #3b82f6; margin: 20px 0;">
                <h4>💙 Gracias por haber sido parte de Inmova</h4>
                <p>Lamentamos verte partir. Si en algún momento cambias de opinión, siempre serás bienvenido/a de vuelta.</p>
                <p>Si tienes algún comentario sobre tu experiencia o sugerencias para mejorar, puedes escribirnos a <a href="mailto:<EMAIL>"><EMAIL></a></p>
              </div>

              <div style="background: #ecfdf5; padding: 15px; border-radius: 8px; border-left: 4px solid #10b981; margin: 20px 0;">
                <h4>🔄 ¿Quieres volver en el futuro?</h4>
                <p>Puedes crear una nueva cuenta en cualquier momento visitando <a href="https://inmova.gt">inmova.gt</a></p>
                <p>Recuerda que empezarás desde cero con una cuenta completamente nueva.</p>
              </div>

              <div class="footer">
                <p>Este es un email automático de confirmación de eliminación de cuenta.</p>
                <p>© 2024 Inmova.gt - Plataforma inmobiliaria de Guatemala</p>
                <p style="font-size: 12px; color: #9ca3af;">
                  Este email se envió para confirmar la eliminación de tu cuenta según las políticas de privacidad y GDPR.
                </p>
              </div>
            </div>
          </div>
        </body>
      </html>
    `
  })
};

// Función para enviar email usando Resend
async function sendEmail(to: string, template: any) {
  if (!RESEND_API_KEY) {
    console.error("RESEND_API_KEY no está configurada");
    return { success: false, error: "Email service not configured" };
  }

  try {
    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${RESEND_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        from: process.env.RESEND_FROM_EMAIL || 'Inmova.gt <<EMAIL>>',
        to,
        subject: template.subject,
        html: template.html,
      }),
    });

    if (!response.ok) {
      const error = await response.text();
      console.error("Error enviando email:", error);
      return { success: false, error };
    }

    const result = await response.json();
    return { success: true, id: result.id };
  } catch (error) {
    console.error("Error en sendEmail:", error);
    return { success: false, error: error instanceof Error ? error.message : String(error) };
  }
}

// Action para notificar nueva solicitud de cita
export const notifyAppointmentRequest = action({
  args: {
    ownerEmail: v.string(),
    ownerName: v.string(),
    guestEmail: v.string(),
    guestName: v.string(),
    guestPhone: v.optional(v.string()),
    propertyTitle: v.string(),
    propertyAddress: v.string(),
    propertyPrice: v.string(),
    requestedStartTime: v.string(),
    requestedEndTime: v.string(),
    meetingType: v.string(),
    message: v.optional(v.string()),
    dashboardUrl: v.string(),
  },
  handler: async (ctx, args) => {
    // Usar zona horaria de Guatemala para formateo correcto
    const requestedDate = new Date(args.requestedStartTime).toLocaleDateString('es-GT', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      timeZone: 'America/Guatemala'
    });

    const requestedTime = new Date(args.requestedStartTime).toLocaleTimeString('es-GT', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
      timeZone: 'America/Guatemala'
    });

    // Notificar al propietario
    const ownerTemplate = EMAIL_TEMPLATES.appointmentRequestOwner({
      ...args,
      requestedDate,
      requestedTime,
    });

    // Notificar al cliente
    const guestTemplate = EMAIL_TEMPLATES.appointmentRequestGuest({
      ...args,
      requestedDate,
      requestedTime,
    });

    const results = await Promise.allSettled([
      sendEmail(args.ownerEmail, ownerTemplate),
      sendEmail(args.guestEmail, guestTemplate),
    ]);

    // Verificar si ambos emails se enviaron exitosamente
    const ownerSuccess = results[0].status === 'fulfilled' && results[0].value.success;
    const guestSuccess = results[1].status === 'fulfilled' && results[1].value.success;

    return {
      success: ownerSuccess && guestSuccess,
      ownerNotification: results[0],
      guestNotification: results[1],
      message: ownerSuccess && guestSuccess
        ? "Ambas notificaciones enviadas exitosamente"
        : "Error enviando una o ambas notificaciones"
    };
  },
});

// Action para notificar cita confirmada
export const notifyAppointmentConfirmed = action({
  args: {
    guestEmail: v.string(),
    guestName: v.string(),
    ownerName: v.string(),
    ownerEmail: v.string(),
    ownerPhone: v.optional(v.string()),
    propertyTitle: v.string(),
    appointmentStartTime: v.string(),
    appointmentEndTime: v.string(),
    meetingType: v.string(),
    location: v.optional(v.string()),
    meetingUrl: v.optional(v.string()),
    response: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const appointmentDate = new Date(args.appointmentStartTime).toLocaleDateString('es-GT', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      timeZone: 'America/Guatemala'
    });

    const appointmentTime = new Date(args.appointmentStartTime).toLocaleTimeString('es-GT', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
      timeZone: 'America/Guatemala'
    });

    const template = EMAIL_TEMPLATES.appointmentConfirmed({
      ...args,
      appointmentDate,
      appointmentTime,
    });

    const result = await sendEmail(args.guestEmail, template);
    return result;
  },
});



// 📧 NUEVA: Action para notificar créditos agotados  
export const notifyLowCredits = action({
  args: {
    userEmail: v.string(),
    userName: v.string(),
    currentPlan: v.string(),
    creditsUsed: v.number(),
    totalCredits: v.number(),
    dashboardUrl: v.string(),
    upgradeUrl: v.string(),
  },
  handler: async (ctx, args) => {
    const template = EMAIL_TEMPLATES.lowCreditsWarning({
      ...args,
      currentPlan: args.currentPlan === 'free' ? 'Plan Gratuito' : 
                   args.currentPlan === 'pro' ? 'Plan Pro' : 'Plan Premium'
    });

    const result = await sendEmail(args.userEmail, template);
    return result;
  },
});

// 📧 NUEVA: Action para notificar posición destacada vencida
export const notifyFeaturedExpired = action({
  args: {
    userEmail: v.string(),
    userName: v.string(),
    propertyTitle: v.string(),
    propertyAddress: v.string(),
    propertyPrice: v.string(),
    featuredDuration: v.number(),
    expiredDate: v.string(),
    renewUrl: v.string(),
    dashboardUrl: v.string(),
  },
  handler: async (ctx, args) => {
    const template = EMAIL_TEMPLATES.featuredPositionExpired({
      ...args,
      expiredDate: new Date(args.expiredDate).toLocaleDateString('es-ES', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
    });

    const result = await sendEmail(args.userEmail, template);
    return result;
  },
});

// 📧 NUEVA: Action para notificar posición premium vencida
export const notifyPremiumExpired = action({
  args: {
    userEmail: v.string(),
    userName: v.string(),
    propertyTitle: v.string(),
    propertyAddress: v.string(),
    propertyPrice: v.string(),
    premiumDuration: v.number(),
    expiredDate: v.string(),
    availableSlots: v.number(),
    renewUrl: v.string(),
    dashboardUrl: v.string(),
  },
  handler: async (ctx, args) => {
    const template = EMAIL_TEMPLATES.premiumPositionExpired({
      ...args,
      expiredDate: new Date(args.expiredDate).toLocaleDateString('es-ES', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
    });

    const result = await sendEmail(args.userEmail, template);
    return result;
  },
});

// 📧 NUEVA: Action para notificar mensaje recibido (para owners/agentes)
export const notifyWebMessageReceived = action({
  args: {
    ownerEmail: v.string(),
    ownerName: v.string(),
    senderName: v.string(),
    senderEmail: v.string(),
    senderPhone: v.optional(v.string()),
    propertyTitle: v.string(),
    propertyAddress: v.string(),
    propertyPrice: v.string(),
    subject: v.string(),
    message: v.string(),
    leadType: v.string(),
    createdAt: v.string(),
    dashboardUrl: v.string(),
  },
  handler: async (ctx, args) => {
    const leadTypeNames = {
      inquiry: "Consulta General",
      viewing: "Solicitud de Visita",
      offer: "Oferta de Compra",
      negotiation: "Negociación"
    };

    const template = EMAIL_TEMPLATES.webMessageReceived({
      ...args,
      leadTypeName: leadTypeNames[args.leadType as keyof typeof leadTypeNames] || "Consulta",
      createdAt: new Date(args.createdAt).toLocaleDateString('es-ES', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }),
    });

    const result = await sendEmail(args.ownerEmail, template);
    return result;
  },
});

// 📧 NUEVA: Action para confirmar mensaje enviado (para compradores)
export const notifyMessageConfirmation = action({
  args: {
    senderName: v.string(),
    senderEmail: v.string(),
    propertyTitle: v.string(),
    propertyAddress: v.string(),
    propertyPrice: v.string(),
    subject: v.string(),
    message: v.string(),
  },
  handler: async (ctx, args) => {
    const template = EMAIL_TEMPLATES.messageConfirmation(args);
    const result = await sendEmail(args.senderEmail, template);
    return result;
  },
});

// 📧 NUEVA: Recordatorio de cita (24 horas antes)
export const notifyAppointmentReminder24h = action({
  args: {
    participantEmail: v.string(),
    participantName: v.string(),
    otherParticipantName: v.string(),
    otherParticipantEmail: v.string(),
    otherParticipantPhone: v.optional(v.string()),
    propertyTitle: v.string(),
    propertyAddress: v.string(),
    appointmentStartTime: v.string(),
    meetingType: v.string(),
    location: v.optional(v.string()),
    meetingUrl: v.optional(v.string()),
    dashboardUrl: v.string(),
  },
  handler: async (ctx, args) => {
    const appointmentDate = new Date(args.appointmentStartTime).toLocaleDateString('es-GT', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      timeZone: 'America/Guatemala'
    });

    const appointmentTime = new Date(args.appointmentStartTime).toLocaleTimeString('es-GT', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
      timeZone: 'America/Guatemala'
    });

    const template = EMAIL_TEMPLATES.appointmentReminder24h({
      ...args,
      appointmentDate,
      appointmentTime,
    });

    const result = await sendEmail(args.participantEmail, template);
    return result;
  },
});

// 📧 NUEVA: Recordatorio de cita (1 hora antes)
export const notifyAppointmentReminder1h = action({
  args: {
    participantEmail: v.string(),
    participantName: v.string(),
    otherParticipantName: v.string(),
    otherParticipantEmail: v.string(),
    otherParticipantPhone: v.optional(v.string()),
    propertyTitle: v.string(),
    appointmentStartTime: v.string(),
    meetingType: v.string(),
    location: v.optional(v.string()),
    meetingUrl: v.optional(v.string()),
    dashboardUrl: v.string(),
  },
  handler: async (ctx, args) => {
    const appointmentTime = new Date(args.appointmentStartTime).toLocaleTimeString('es-GT', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
      timeZone: 'America/Guatemala'
    });

    const template = EMAIL_TEMPLATES.appointmentReminder1h({
      ...args,
      appointmentTime,
    });

    const result = await sendEmail(args.participantEmail, template);
    return result;
  },
});

// 📧 NUEVA: Notificación de propiedad agregada a favoritos
export const notifyPropertyAddedToFavorites = action({
  args: {
    ownerEmail: v.string(),
    ownerName: v.string(),
    buyerName: v.string(),
    buyerEmail: v.string(),
    buyerPhone: v.optional(v.string()),
    propertyTitle: v.string(),
    propertyAddress: v.string(),
    propertyPrice: v.string(),
    propertyUrl: v.string(),
    totalFavorites: v.number(),
    totalViews: v.optional(v.number()),
    totalInquiries: v.optional(v.number()),
    dashboardUrl: v.string(),
  },
  handler: async (ctx, args) => {
    const favoriteDate = new Date().toLocaleDateString('es-ES', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    const template = EMAIL_TEMPLATES.propertyAddedToFavorites({
      ...args,
      favoriteDate,
    });

    const result = await sendEmail(args.ownerEmail, template);
    return result;
  },
});

// 📧 NUEVA: Notificación de suscripción por vencer
export const notifySubscriptionExpiring = action({
  args: {
    userEmail: v.string(),
    userName: v.string(),
    planName: v.string(),
    planPrice: v.string(),
    daysLeft: v.number(),
    expirationDate: v.string(),
    activeProperties: v.number(),
    remainingCredits: v.number(),
    renewUrl: v.string(),
    dashboardUrl: v.string(),
  },
  handler: async (ctx, args) => {
    const template = EMAIL_TEMPLATES.subscriptionExpiring(args);
    const result = await sendEmail(args.userEmail, template);
    return result;
  },
});

// 📧 NUEVA: Notificación de trial por vencer
export const notifyTrialExpiring = action({
  args: {
    userEmail: v.string(),
    userName: v.string(),
    daysLeft: v.number(),
    hoursLeft: v.number(),
    trialEndDate: v.string(),
    propertiesPublished: v.number(),
    inquiriesReceived: v.number(),
    totalViews: v.number(),
    favoritesReceived: v.number(),
    upgradeUrl: v.string(),
    dashboardUrl: v.string(),
  },
  handler: async (ctx, args) => {
    const template = EMAIL_TEMPLATES.trialExpiring(args);
    const result = await sendEmail(args.userEmail, template);
    return result;
  },
});

// 📧 NUEVA: Action para notificar respuesta a mensaje
export const notifyMessageReply = action({
  args: {
    clientEmail: v.string(),
    clientName: v.string(),
    ownerName: v.string(),
    ownerEmail: v.string(),
    ownerPhone: v.optional(v.string()),
    propertyTitle: v.string(),
    propertyAddress: v.string(),
    propertyPrice: v.string(),
    propertyUrl: v.string(),
    originalSubject: v.string(),
    originalMessage: v.string(),
    replyMessage: v.string(),
    replyDate: v.string(),
  },
  handler: async (ctx, args) => {
    const template = EMAIL_TEMPLATES.messageReply(args);
    const result = await sendEmail(args.clientEmail, template);
    return result;
  },
});

// 🧪 FUNCIÓN DE TESTING: Probar configuración de Resend
export const testEmailConfiguration = action({
  args: {
    testEmail: v.string(),
  },
  handler: async (ctx, args) => {
    // Verificar configuración
    if (!RESEND_API_KEY) {
      return {
        success: false,
        error: "RESEND_API_KEY no está configurada",
        config: {
          hasApiKey: false,
          fromEmail: process.env.RESEND_FROM_EMAIL || 'No configurado'
        }
      };
    }

    // Template de prueba
    const testTemplate = {
      subject: "🧪 Prueba de Configuración - Inmova.gt",
      html: `
        <!DOCTYPE html>
        <html>
          <head>
            <meta charset="utf-8">
            <title>Prueba de Email</title>
            <style>
              body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; line-height: 1.6; color: #333; }
              .container { max-width: 600px; margin: 0 auto; padding: 20px; }
              .header { background: #059669; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
              .content { background: #f0fdf4; padding: 30px; border-radius: 0 0 8px 8px; }
              .success { background: #d1fae5; padding: 15px; border-radius: 6px; margin: 15px 0; }
            </style>
          </head>
          <body>
            <div class="container">
              <div class="header">
                <h1>✅ Configuración Exitosa</h1>
              </div>
              <div class="content">
                <div class="success">
                  <h2>🎉 ¡Resend está funcionando correctamente!</h2>
                  <p>Este email de prueba confirma que:</p>
                  <ul>
                    <li>✅ RESEND_API_KEY está configurada</li>
                    <li>✅ RESEND_FROM_EMAIL está configurada</li>
                    <li>✅ El dominio está verificado</li>
                    <li>✅ Las notificaciones funcionarán correctamente</li>
                  </ul>
                </div>

                <p><strong>Configuración actual:</strong></p>
                <ul>
                  <li><strong>From Email:</strong> ${process.env.RESEND_FROM_EMAIL || 'Inmova.gt <<EMAIL>>'}</li>
                  <li><strong>Fecha de prueba:</strong> ${new Date().toLocaleString('es-ES')}</li>
                </ul>

                <p>Ahora puedes usar todas las funcionalidades de notificación:</p>
                <ul>
                  <li>📧 Notificaciones de citas</li>
                  <li>📧 Confirmaciones de mensajes</li>
                  <li>📧 Recordatorios automáticos</li>
                  <li>📧 Alertas de vencimiento</li>
                </ul>

                <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
                <p style="text-align: center; color: #6b7280; font-size: 14px;">
                  Este es un email de prueba generado automáticamente por Inmova.gt
                </p>
              </div>
            </div>
          </body>
        </html>
      `
    };

    // Intentar enviar email de prueba
    const result = await sendEmail(args.testEmail, testTemplate);

    return {
      ...result,
      config: {
        hasApiKey: !!RESEND_API_KEY,
        fromEmail: process.env.RESEND_FROM_EMAIL || 'Inmova.gt <<EMAIL>>',
        timestamp: new Date().toISOString()
      }
    };
  },
});

// 📧 NUEVA: Action para notificar cita rechazada
export const notifyAppointmentRejected = action({
  args: {
    guestEmail: v.string(),
    guestName: v.string(),
    ownerName: v.string(),
    ownerEmail: v.string(),
    ownerPhone: v.optional(v.string()),
    propertyTitle: v.string(),
    propertyAddress: v.string(),
    propertyPrice: v.string(),
    propertyUrl: v.optional(v.string()),
    requestedStartTime: v.string(),
    requestedEndTime: v.string(),
    meetingType: v.string(),
    response: v.optional(v.string()),
    // 🆕 Nuevos campos para fecha propuesta
    proposedStartTime: v.optional(v.string()),
    proposedEndTime: v.optional(v.string()),
    proposedLocation: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const requestedDate = new Date(args.requestedStartTime).toLocaleDateString('es-GT', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      timeZone: 'America/Guatemala'
    });

    const requestedTime = new Date(args.requestedStartTime).toLocaleTimeString('es-GT', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
      timeZone: 'America/Guatemala'
    });

    // 🆕 Procesar fecha propuesta si existe
    let proposedDate, proposedTimeRange;
    if (args.proposedStartTime && args.proposedEndTime) {
      proposedDate = new Date(args.proposedStartTime).toLocaleDateString('es-GT', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        timeZone: 'America/Guatemala'
      });

      const proposedStartTime = new Date(args.proposedStartTime).toLocaleTimeString('es-GT', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
        timeZone: 'America/Guatemala'
      });

      const proposedEndTime = new Date(args.proposedEndTime).toLocaleTimeString('es-GT', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
        timeZone: 'America/Guatemala'
      });

      proposedTimeRange = `${proposedStartTime} - ${proposedEndTime}`;
    }

    const template = EMAIL_TEMPLATES.appointmentRejected({
      ...args,
      requestedDate,
      requestedTime,
      proposedDate,
      proposedTimeRange,
    });

    const result = await sendEmail(args.guestEmail, template);
    return result;
  },
});

// 📧 NUEVA: Notificación de cuenta eliminada completamente
export const notifyAccountDeleted = action({
  args: {
    userEmail: v.string(),
    userName: v.string(),
    deletionDate: v.string(),
    totalDeleted: v.optional(v.number()),
    deletedCount: v.optional(v.object({
      properties: v.number(),
      favorites: v.number(),
      messages: v.number(),
      appointments: v.number(),
      transactions: v.number(),
    })),
  },
  handler: async (ctx, args) => {
    const template = EMAIL_TEMPLATES.accountDeleted(args);
    const result = await sendEmail(args.userEmail, template);
    return result;
  },
});