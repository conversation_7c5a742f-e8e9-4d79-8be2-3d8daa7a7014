import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';
import { NextResponse } from 'next/server';

// Rutas protegidas que requieren autenticación
const isProtectedRoute = createRouteMatcher([
  "/dashboard(.*)",
  "/seller(.*)",
  "/agent(.*)",
  "/favorites(.*)"
]);

export default clerkMiddleware(async (auth, req) => {
  // Redirigir /search a /properties
  if (req.nextUrl.pathname === '/search') {
    const url = new URL('/properties', req.url);
    // Preservar query parameters si los hay
    url.search = req.nextUrl.search;
    return NextResponse.redirect(url);
  }

  // Proteger rutas que requieren autenticación
  if (isProtectedRoute(req)) {
    const { userId } = await auth();

    if (!userId) {
      // Redirigir a login si no está autenticado
      return NextResponse.redirect(new URL("/sign-in", req.url));
    }
  }

  return NextResponse.next();
});

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
}