import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Configuración por defecto
const DEFAULT_SETTINGS = [
  {
    key: "trial_days",
    value: 15,
    description: "Días de trial para nuevos usuarios seller/agent"
  },
  {
    key: "premium_price",
    value: 99,
    description: "Precio mensual del plan premium en USD"
  },
  {
    key: "trial_enabled",
    value: true,
    description: "Si el sistema de trial está habilitado"
  }
];

// Inicializar configuración por defecto
export const initializeDefaultSettings = mutation({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    // Verificar que sea admin
    const user = await ctx.db
      .query("users")
      .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.subject))
      .first();

    if (!user || user.role !== "admin") {
      throw new Error("Solo administradores pueden inicializar configuración");
    }

    const now = Date.now();
    const results = [];

    for (const setting of DEFAULT_SETTINGS) {
      // Verificar si ya existe
      const existing = await ctx.db
        .query("adminSettings")
        .withIndex("by_key", (q) => q.eq("key", setting.key))
        .first();

      if (!existing) {
        const id = await ctx.db.insert("adminSettings", {
          key: setting.key,
          value: setting.value,
          description: setting.description,
          updatedBy: identity.subject,
          updatedAt: now,
        });
        results.push({ key: setting.key, created: true, id });
      } else {
        results.push({ key: setting.key, created: false, existing: true });
      }
    }

    return { success: true, results };
  },
});

// Función pública para inicializar configuración (solo para setup inicial)
export const initializePublicSettings = mutation({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();
    const results = [];

    for (const setting of DEFAULT_SETTINGS) {
      // Verificar si ya existe
      const existing = await ctx.db
        .query("adminSettings")
        .withIndex("by_key", (q) => q.eq("key", setting.key))
        .first();

      if (!existing) {
        const id = await ctx.db.insert("adminSettings", {
          key: setting.key,
          value: setting.value,
          description: setting.description,
          updatedBy: "system",
          updatedAt: now,
        });
        results.push({ key: setting.key, created: true, id });
      } else {
        results.push({ key: setting.key, created: false, existing: true });
      }
    }

    return { success: true, results };
  },
});

// Obtener configuración admin
export const getAdminSettings = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    // Verificar que sea admin
    const user = await ctx.db
      .query("users")
      .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.subject))
      .first();

    if (!user || user.role !== "admin") {
      throw new Error("Solo administradores pueden ver configuración");
    }

    const settings = await ctx.db.query("adminSettings").collect();
    
    // Convertir a objeto para fácil acceso
    const settingsObj: Record<string, any> = {};
    settings.forEach(setting => {
      settingsObj[setting.key] = {
        value: setting.value,
        description: setting.description,
        updatedAt: setting.updatedAt,
        updatedBy: setting.updatedBy
      };
    });

    return settingsObj;
  },
});

// Obtener configuración pública (sin verificar admin)
export const getPublicSettings = query({
  args: {},
  handler: async (ctx) => {
    const settings = await ctx.db.query("adminSettings").collect();
    
    // Solo devolver configuraciones públicas
    const publicKeys = ["trial_days", "premium_price", "trial_enabled"];
    const publicSettings: Record<string, any> = {};
    
    settings.forEach(setting => {
      if (publicKeys.includes(setting.key)) {
        publicSettings[setting.key] = setting.value;
      }
    });

    return publicSettings;
  },
});

// Actualizar configuración
export const updateSetting = mutation({
  args: {
    key: v.string(),
    value: v.union(v.string(), v.number(), v.boolean()),
    description: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    // Verificar que sea admin
    const user = await ctx.db
      .query("users")
      .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.subject))
      .first();

    if (!user || user.role !== "admin") {
      throw new Error("Solo administradores pueden actualizar configuración");
    }

    const existing = await ctx.db
      .query("adminSettings")
      .withIndex("by_key", (q) => q.eq("key", args.key))
      .first();

    const now = Date.now();

    if (existing) {
      // Actualizar existente
      await ctx.db.patch(existing._id, {
        value: args.value,
        description: args.description || existing.description,
        updatedBy: identity.subject,
        updatedAt: now,
      });
      return { success: true, action: "updated", key: args.key };
    } else {
      // Crear nuevo
      await ctx.db.insert("adminSettings", {
        key: args.key,
        value: args.value,
        description: args.description || "",
        updatedBy: identity.subject,
        updatedAt: now,
      });
      return { success: true, action: "created", key: args.key };
    }
  },
});

// Extender trial de un usuario específico
export const extendUserTrial = mutation({
  args: {
    userId: v.optional(v.string()),
    userEmail: v.optional(v.string()),
    additionalDays: v.number(),
    reason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    // Verificar que sea admin
    const user = await ctx.db
      .query("users")
      .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.subject))
      .first();

    if (!user || user.role !== "admin") {
      throw new Error("Solo administradores pueden extender trials");
    }

    // Determinar el userId
    let targetUserId = args.userId;

    if (!targetUserId && args.userEmail) {
      // Buscar usuario por email
      const targetUser = await ctx.db
        .query("users")
        .filter((q: any) => q.eq(q.field("email"), args.userEmail))
        .first();

      if (!targetUser) {
        throw new Error(`Usuario con email ${args.userEmail} no encontrado`);
      }

      targetUserId = targetUser.tokenIdentifier;
    }

    if (!targetUserId) {
      throw new Error("Debe proporcionar userId o userEmail");
    }

    // Buscar suscripción del usuario
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("userId", (q) => q.eq("userId", targetUserId))
      .first();

    if (!subscription) {
      throw new Error("Usuario no tiene suscripción");
    }

    const now = Date.now();
    const additionalMs = args.additionalDays * 24 * 60 * 60 * 1000;

    let newTrialEndDate: number;
    
    if (subscription.isTrialActive && subscription.trialEndDate) {
      // Extender trial existente
      newTrialEndDate = subscription.trialEndDate + additionalMs;
    } else {
      // Crear nuevo trial
      newTrialEndDate = now + additionalMs;
    }

    await ctx.db.patch(subscription._id, {
      isTrialActive: true,
      trialEndDate: newTrialEndDate,
      trialDaysGranted: (subscription.trialDaysGranted || 0) + args.additionalDays,
      updatedAt: now,
    });

    // Log de la acción (opcional - podrías crear una tabla de logs)
    console.log(`Admin ${identity.subject} extendió trial de usuario ${targetUserId} por ${args.additionalDays} días. Razón: ${args.reason || "No especificada"}`);

    return {
      success: true,
      newTrialEndDate,
      totalDaysGranted: (subscription.trialDaysGranted || 0) + args.additionalDays,
      reason: args.reason
    };
  },
});

// Obtener estadísticas de trials
export const getTrialStats = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      console.log("getTrialStats called without authentication");
      throw new Error("No autenticado");
    }

    // Verificar que sea admin
    const user = await ctx.db
      .query("users")
      .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.subject))
      .first();

    if (!user || user.role !== "admin") {
      console.log("getTrialStats called by non-admin user:", user?.role || "no user");
      throw new Error("Solo administradores pueden ver estadísticas");
    }

    const subscriptions = await ctx.db.query("subscriptions").collect();
    
    const stats = {
      totalUsers: subscriptions.length,
      activeTrials: subscriptions.filter(s => s.isTrialActive).length,
      expiredTrials: subscriptions.filter(s => s.hasUsedTrial && !s.isTrialActive).length,
      paidSubscriptions: subscriptions.filter(s => s.plan !== "free" && !s.isTrialActive).length,
      conversionRate: 0,
    };

    // Calcular tasa de conversión
    const totalTrialsUsed = stats.expiredTrials + stats.paidSubscriptions;
    if (totalTrialsUsed > 0) {
      stats.conversionRate = (stats.paidSubscriptions / totalTrialsUsed) * 100;
    }

    return stats;
  },
});

// Obtener todos los usuarios (solo para admin)
export const getAllUsers = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    // Verificar que sea admin
    const user = await ctx.db
      .query("users")
      .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.subject))
      .first();

    if (!user || user.role !== "admin") {
      throw new Error("Solo administradores pueden ver todos los usuarios");
    }

    const users = await ctx.db.query("users").collect();

    // Enriquecer con información de suscripción
    const enrichedUsers = await Promise.all(
      users.map(async (user) => {
        // Buscar suscripción del usuario
        const subscription = await ctx.db
          .query("subscriptions")
          .withIndex("userId", (q) => q.eq("userId", user.tokenIdentifier))
          .first();

        return {
          _id: user._id,
          tokenIdentifier: user.tokenIdentifier, // Necesario para cambiar plan
          name: user.name,
          email: user.email,
          role: user.role,
          trialEndsAt: subscription?.trialEndDate || null,
          subscriptionStatus: subscription?.status || "free",
          isTrialActive: subscription?.isTrialActive || false,
          plan: subscription?.plan || "free",
          createdAt: user._creationTime,
        };
      })
    );

    return enrichedUsers;
  },
});

// Función de admin para cambiar plan de usuario (útil para pruebas)
export const adminChangePlan = mutation({
  args: {
    userId: v.string(), // tokenIdentifier del usuario
    newPlan: v.union(v.literal("free"), v.literal("premium")), // Solo free y premium
    resetCredits: v.optional(v.boolean()), // Si resetear créditos usados
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    console.log("🔧 Admin cambiando plan:", {
      adminId: identity.subject,
      targetUserId: args.userId,
      newPlan: args.newPlan,
      resetCredits: args.resetCredits
    });

    // Verificar que sea admin
    const adminUser = await ctx.db
      .query("users")
      .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.subject))
      .first();

    if (!adminUser || adminUser.role !== "admin") {
      throw new Error("Solo administradores pueden cambiar planes");
    }

    // Verificar que el usuario objetivo existe
    const targetUser = await ctx.db
      .query("users")
      .withIndex("by_token", (q) => q.eq("tokenIdentifier", args.userId))
      .first();

    if (!targetUser) {
      throw new Error(`Usuario con ID ${args.userId} no encontrado`);
    }

    console.log("👤 Usuario objetivo encontrado:", targetUser.name || targetUser.email);

    // Buscar suscripción del usuario objetivo
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("userId", (q) => q.eq("userId", args.userId))
      .first();

    console.log("🔍 Suscripción encontrada:", subscription ? "Sí" : "No", subscription?.plan);

    // Obtener límites del nuevo plan
    const newLimits = getPlanLimits(args.newPlan);
    const now = Date.now();

    if (subscription) {
      // Actualizar suscripción existente
      console.log("✏️ Actualizando suscripción existente:", subscription._id);
      await ctx.db.patch(subscription._id, {
        plan: args.newPlan,
        credits: newLimits.credits,
        maxProperties: newLimits.maxProperties,
        creditsUsed: args.resetCredits ? 0 : Math.min(subscription.creditsUsed, newLimits.credits),
        status: "active",
        updatedAt: now,
      });
      console.log("✅ Suscripción actualizada exitosamente");
    } else {
      // Crear nueva suscripción
      console.log("➕ Creando nueva suscripción para usuario:", args.userId);
      const newSubscriptionId = await ctx.db.insert("subscriptions", {
        userId: args.userId,
        plan: args.newPlan,
        status: "active",
        credits: newLimits.credits,
        creditsUsed: 0,
        maxProperties: newLimits.maxProperties,
        propertiesCount: 0,
        createdAt: now,
        updatedAt: now,
      });
      console.log("✅ Nueva suscripción creada:", newSubscriptionId);
    }

    return {
      success: true,
      message: `Plan de ${targetUser.name || targetUser.email} cambiado a ${args.newPlan}`,
      newPlan: args.newPlan,
      newLimits,
      creditsReset: args.resetCredits || false,
    };
  },
});



// Función auxiliar para obtener límites de plan (solo free y premium)
function getPlanLimits(plan: string) {
  switch (plan) {
    case "premium":
      return { credits: 300, maxProperties: 999999 }; // Ilimitadas
    default: // free
      return { credits: 10, maxProperties: 5 };
  }
}
