"use client";

import React from 'react';
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2 } from 'lucide-react';

interface LocationDropdownProps {
  level: number;
  countryCode: string;
  parentId?: Id<"locationData">;
  value: Id<"locationData"> | string | null;
  onChange: (value: Id<"locationData"> | null) => void;
  placeholder: string;
  disabled?: boolean;
  className?: string;
}

export const LocationDropdown: React.FC<LocationDropdownProps> = ({
  level,
  countryCode,
  parentId,
  value,
  onChange,
  placeholder,
  disabled = false,
  className = ""
}) => {
  // Cargar opciones basadas en si es nivel raíz o tiene parent
  const options = useQuery(
    parentId 
      ? api.locationData.getChildrenByParent
      : api.locationData.getRootLevels,
    parentId 
      ? { parentId, countryCode }
      : { countryCode, level }
  );

  const handleValueChange = (selectedValue: string) => {
    if (selectedValue === "__none__" || selectedValue === "none" || selectedValue === "") {
      onChange(null);
    } else {
      onChange(selectedValue as Id<"locationData">);
    }
  };

  // Mostrar loading mientras se cargan las opciones
  if (options === undefined) {
    return (
      <Select disabled>
        <SelectTrigger className={`mt-1 ${className}`}>
          <div className="flex items-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span className="text-muted-foreground">Cargando...</span>
          </div>
        </SelectTrigger>
      </Select>
    );
  }

  // Si no hay opciones disponibles
  if (!options || options.length === 0) {
    return (
      <Select disabled>
        <SelectTrigger className={`mt-1 ${className}`}>
          <SelectValue placeholder="No hay opciones disponibles" />
        </SelectTrigger>
      </Select>
    );
  }



  // Asegurar que siempre tengamos un valor controlado
  let finalValue = "__none__";

  if (!value) {
    finalValue = "__none__";
  } else if (options && options.length > 0) {
    const valueExists = options.some((option: any) => option._id === value);
    finalValue = valueExists ? value : "__none__";
  } else {
    // Si aún no hay opciones cargadas, usar __none__ para evitar problemas
    finalValue = "__none__";
  }

  return (
    <Select
      value={finalValue}
      onValueChange={handleValueChange}
      disabled={disabled}
    >
      <SelectTrigger className={`mt-1 ${className}`}>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {/* Opción para limpiar selección */}
        <SelectItem value="__none__">
          <span className="text-muted-foreground">-- Seleccionar --</span>
        </SelectItem>

        {/* Opciones de ubicación */}
        {options && options.length > 0 && options.map((option: any) => (
          <SelectItem key={option._id} value={option._id}>
            {option.name}{option.code ? ` (${option.code})` : ''}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};

// Componente especializado para autocompletado (para colonias y direcciones libres)
interface LocationAutocompleteProps {
  level: number;
  countryCode: string;
  parentId?: Id<"locationData">;
  value: string;
  onChange: (value: string) => void;
  placeholder: string;
  disabled?: boolean;
  className?: string;
}

export const LocationAutocomplete: React.FC<LocationAutocompleteProps> = ({
  level,
  countryCode,
  parentId,
  value,
  onChange,
  placeholder,
  disabled = false,
  className = ""
}) => {
  // Para autocompletado, usaremos un input normal por ahora
  // En el futuro se puede implementar con Combobox de shadcn/ui
  return (
    <input
      type="text"
      value={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder={placeholder}
      disabled={disabled}
      className={`flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 mt-1 ${className}`}
    />
  );
};

// Componente wrapper que decide entre dropdown o autocomplete
interface SmartLocationInputProps {
  level: number;
  countryCode: string;
  parentId?: Id<"locationData">;
  value: string;
  onChange: (value: string) => void;
  placeholder: string;
  disabled?: boolean;
  className?: string;
  forceDropdown?: boolean;
  forceInput?: boolean;
}

export const SmartLocationInput: React.FC<SmartLocationInputProps> = ({
  level,
  countryCode,
  parentId,
  value,
  onChange,
  placeholder,
  disabled = false,
  className = "",
  forceDropdown = false,
  forceInput = false
}) => {
  // Determinar si usar dropdown o input libre
  const shouldUseDropdown = () => {
    if (forceDropdown) return true;
    if (forceInput) return false;
    
    // Lógica para Guatemala
    if (countryCode === 'GT') {
      // Siempre dropdown para niveles 1 y 2
      if (level === 1 || level === 2) return true;
      
      // Para nivel 3 (zonas): dropdown solo si es Ciudad de Guatemala
      if (level === 3) {
        // Aquí necesitaríamos verificar si el municipio seleccionado es "Guatemala"
        // Por simplicidad, usaremos dropdown por defecto para nivel 3
        return true;
      }
      
      // Nivel 4 (colonias): siempre input libre
      return false;
    }
    
    // Para otros países: dropdown para niveles 1 y 2, input libre para el resto
    return level <= 2;
  };

  if (shouldUseDropdown()) {
    return (
      <LocationDropdown
        level={level}
        countryCode={countryCode}
        parentId={parentId}
        value={value && value !== "" ? (value as Id<"locationData">) : null}
        onChange={(newValue) => onChange(newValue ? newValue : "")}
        placeholder={placeholder}
        disabled={disabled}
        className={className}
      />
    );
  } else {
    return (
      <LocationAutocomplete
        level={level}
        countryCode={countryCode}
        parentId={parentId}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        disabled={disabled}
        className={className}
      />
    );
  }
};
