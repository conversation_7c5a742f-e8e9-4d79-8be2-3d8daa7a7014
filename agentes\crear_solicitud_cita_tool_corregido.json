{"parameters": {"toolDescription": "🚨 VALIDACIÓN CRÍTICA ANTES DE USAR 🚨\n\n**NUNCA ejecutar sin TODOS estos datos:**\n✅ guestName (nombre completo)\n✅ guestEmail (email válido con @)\n✅ guestPhone (8 dígitos numéricos)\n✅ propertyId (formato: jd7fzdrkdanjs1tgb98kgm88a57jfhav)\n✅ Fecha/hora confirmada\n\n**SI FALTA ALGÚN DATO:** Solicitar primero:\n\"Para confirmar la cita necesito su email y número de teléfono. ¿Podría proporcionármelos?\"\n\n**SECUENCIA OBLIGATORIA PREVIA:**\n1. consultar_estado_cita\n2. verificar_disponibilidad_propiedad\n3. RECOPILAR todos los datos\n4. ENTONCES usar esta herramienta\n\n---\n\nCrea una solicitud de cita para visitar una propiedad.\n\nPARÁMETROS OBLIGATORIOS:\n- propertyId: ID EXACTO de la propiedad (formato: jd7fzdrkdanjs1tgb98kgm88a57jfhav)\n- guestName: nombre del cliente (OBLIGATORIO)\n- guestEmail: email del cliente (OBLIGATORIO)\n- guestPhone: teléfono del cliente (OBLIGATORIO)\n- requestedStartTime: hora inicio en formato ISO\n- requestedEndTime: hora fin en formato ISO\n- type: \"property_viewing\"\n- meetingType: \"in_person\"\n\nRESPUESTA EXITOSA:\nConfirma SIEMPRE con este formato:\n\"¡Perfecto! He confirmado su cita:\n\n📅 **Detalles de su visita:**\n🏠 [Nombre de la propiedad]\n📍 [Dirección completa]\n📅 [Día, fecha y hora]\n👤 [Nombre del cliente]\n📧 [Email del cliente]\n📱 [Teléfono del cliente]\n\nEl propietario será notificado y recibirá un recordatorio 2 horas antes de la cita.\"\n\nCRÍTICO - FORMATO DE FECHAS:\n- USAR zona horaria Guatemala: -06:00 (NO usar Z para UTC)\n- EJEMPLO CORRECTO: \"2025-06-17T14:30:00-06:00\"\n- EJEMPLO INCORRECTO: \"2025-06-17T14:30:00.000Z\"\n\nVALIDACIÓN PRE-CREACIÓN:\n- Verificar en historial que se completó toda la secuencia previa\n- Confirmar que no existe cita duplicada\n- Validar que se verificó disponibilidad\n- Asegurar que todos los datos fueron recopilados y confirmados", "method": "POST", "url": "https://capable-cod-213.convex.site/createAppointmentRequest", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "x-api-key", "value": "dde2fae93649d4550684edd565a9515f"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"propertyId\": \"{{ $fromAI('propertyId', 'Property ID', 'string') }}\",\n  \"guestName\": \"{{ $fromAI('guestName', 'Guest name', 'string') }}\",\n  \"guestEmail\": \"{{ $fromAI('guestEmail', 'Guest email', 'string') }}\",\n  \"guestPhone\": \"{{ $fromAI('guestPhone', 'Guest phone', 'string') }}\",\n  \"requestedStartTime\": \"{{ $fromAI('requestedStartTime', 'Start time ISO string', 'string') }}\",\n  \"requestedEndTime\": \"{{ $fromAI('requestedEndTime', 'End time ISO string', 'string') }}\",\n  \"message\": \"{{ $fromAI('message', 'Additional message', 'string') || 'Solicitud de cita generada por IA' }}\",\n  \"type\": \"{{ $fromAI('type', 'Appointment type', 'string') || 'property_viewing' }}\",\n  \"meetingType\": \"in_person\",\n  \"source\": \"n8n-ai-assistant\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequestTool", "typeVersion": 4.2, "position": [-10980, 3080], "id": "bc672789-4f99-43e5-ab21-454f6af870b4", "name": "crear_solicitud_cita"}