"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { api } from "@/convex/_generated/api";
import { useMutation, useQuery } from "convex/react";
import { toast } from "sonner";
import { useAlert } from "@/hooks/use-alert";
import { useState } from "react";
import React from "react";
import { useUser } from "@clerk/clerk-react";
import {
  MessageSquare,
  Plus,
  Edit,
  Trash2,
  CheckCircle,
  Clock,
  Eye,
  Save,
  X,
  Lock,
  Bot,
  History,
  Settings,
  FileText,
  Zap,
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

export default function SystemMessagesPage() {
  const { user } = useUser();
  const [loading, setLoading] = useState<string | null>(null);
  const { showConfirm } = useAlert();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState<any>(null);
  const [formData, setFormData] = useState({
    version: "",
    title: "",
    description: "",
    content: "",
    tags: "",
    makeActive: false,
  });

  // Queries
  const currentUser = useQuery(api.users.getCurrentUser);
  const systemMessages = useQuery(api.systemMessages.getSystemMessageHistory, { limit: 50 });
  const activeMessage = useQuery(api.systemMessages.getActiveSystemMessage);

  // Mutations
  const createSystemMessage = useMutation(api.systemMessages.createSystemMessage);
  const activateSystemMessage = useMutation(api.systemMessages.activateSystemMessage);
  const deleteSystemMessage = useMutation(api.systemMessages.deleteSystemMessage);
  const initializeDefault = useMutation(api.systemMessages.initializeDefaultSystemMessage);

  // Verificar que el usuario existe y es admin
  if (!currentUser || currentUser.role !== 'admin') {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="max-w-md mx-auto text-center bg-white rounded-lg shadow-lg p-8">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Lock className="h-8 w-8 text-red-600" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Acceso Denegado</h1>
          <p className="text-gray-600 mb-6">
            No tienes permisos para acceder a la gestión de system messages.
          </p>
        </div>
      </div>
    );
  }

  const handleCreateMessage = async () => {
    if (!formData.version || !formData.title || !formData.content) {
      toast.error("Por favor completa todos los campos requeridos");
      return;
    }

    setLoading("create");
    try {
      const tags = formData.tags.split(",").map(tag => tag.trim()).filter(Boolean);

      await createSystemMessage({
        version: formData.version,
        title: formData.title,
        description: formData.description,
        content: formData.content,
        tags,
        makeActive: formData.makeActive,
      });

      toast.success("System message creado exitosamente");
      setDialogOpen(false);
      setFormData({
        version: "",
        title: "",
        description: "",
        content: "",
        tags: "",
        makeActive: false,
      });
    } catch (error) {
      console.error("Error:", error);
      toast.error("Error al crear system message");
    } finally {
      setLoading(null);
    }
  };

  const handleActivateMessage = async (messageId: any) => {
    setLoading(`activate-${messageId}`);
    try {
      await activateSystemMessage({ messageId });
      toast.success("System message activado exitosamente");
    } catch (error) {
      console.error("Error:", error);
      toast.error("Error al activar system message");
    } finally {
      setLoading(null);
    }
  };

  const handleDeleteMessage = async (messageId: any) => {
    showConfirm(
      "¿Estás seguro de que quieres eliminar este system message?",
      async () => {
        setLoading(`delete-${messageId}`);
        try {
          await deleteSystemMessage({ messageId });
          toast.success("System message eliminado exitosamente");
        } catch (error) {
          console.error("Error:", error);
          toast.error("Error al eliminar system message");
        } finally {
          setLoading(null);
        }
      },
      {
        title: "Confirmar eliminación",
        confirmText: "Eliminar",
        cancelText: "Cancelar"
      }
    );
  };

  const handleInitializeDefault = async () => {
    setLoading("initialize");
    try {
      const result = await initializeDefault();
      toast.success(result.message);
    } catch (error) {
      console.error("Error:", error);
      toast.error("Error al inicializar system message por defecto");
    } finally {
      setLoading(null);
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleString('es-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <div className="flex flex-col gap-6 p-4 md:p-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-semibold tracking-tight flex items-center gap-2">
            <Bot className="h-6 w-6 md:h-8 md:w-8" />
            Gestión de System Messages
          </h1>
          <p className="text-muted-foreground mt-2 text-sm md:text-base">
            Administra los mensajes del sistema para el agente de IA
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button
            onClick={handleInitializeDefault}
            variant="outline"
            disabled={loading === "initialize"}
          >
            {loading === "initialize" ? (
              <Settings className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <Settings className="h-4 w-4 mr-2" />
            )}
            Inicializar
          </Button>
          
          <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Nuevo System Message
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Crear Nuevo System Message</DialogTitle>
                <DialogDescription>
                  Crea una nueva versión del system message para el agente de IA
                </DialogDescription>
              </DialogHeader>
              
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="version">Versión *</Label>
                    <Input
                      id="version"
                      placeholder="v2.1"
                      value={formData.version}
                      onChange={(e) => setFormData({...formData, version: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="title">Título *</Label>
                    <Input
                      id="title"
                      placeholder="System Message v2.1 - Mejoras en citas"
                      value={formData.title}
                      onChange={(e) => setFormData({...formData, title: e.target.value})}
                    />
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="description">Descripción</Label>
                  <Input
                    id="description"
                    placeholder="Descripción de los cambios realizados"
                    value={formData.description}
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                  />
                </div>
                
                <div>
                  <Label htmlFor="tags">Tags (separados por comas)</Label>
                  <Input
                    id="tags"
                    placeholder="mejoras, citas, validaciones"
                    value={formData.tags}
                    onChange={(e) => setFormData({...formData, tags: e.target.value})}
                  />
                </div>
                
                <div>
                  <Label htmlFor="content">Contenido del System Message *</Label>
                  <Textarea
                    id="content"
                    placeholder="### 👤 HISTORIAL CONVERSACIONAL..."
                    value={formData.content}
                    onChange={(e) => setFormData({...formData, content: e.target.value})}
                    className="min-h-[300px] font-mono text-sm"
                  />
                </div>
                
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="makeActive"
                    checked={formData.makeActive}
                    onChange={(e) => setFormData({...formData, makeActive: e.target.checked})}
                  />
                  <Label htmlFor="makeActive">Activar inmediatamente</Label>
                </div>
              </div>
              
              <DialogFooter>
                <Button variant="outline" onClick={() => setDialogOpen(false)}>
                  Cancelar
                </Button>
                <Button onClick={handleCreateMessage} disabled={loading === "create"}>
                  {loading === "create" ? (
                    <Save className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  Crear System Message
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* System Message Activo */}
      {activeMessage && (
        <Card className="border-green-200 bg-green-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-green-800">
              <Zap className="h-5 w-5" />
              System Message Activo
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <p className="text-sm text-green-600">Versión</p>
                <p className="font-medium text-green-900">{activeMessage.version}</p>
              </div>
              <div>
                <p className="text-sm text-green-600">Título</p>
                <p className="font-medium text-green-900">{activeMessage.title}</p>
              </div>
              <div>
                <p className="text-sm text-green-600">Creado</p>
                <p className="font-medium text-green-900">{formatDate(activeMessage.createdAt)}</p>
              </div>
            </div>
            {activeMessage.description && (
              <div className="mt-3">
                <p className="text-sm text-green-600">Descripción</p>
                <p className="text-green-800">{activeMessage.description}</p>
              </div>
            )}
            <div className="mt-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setSelectedMessage(activeMessage);
                  setPreviewOpen(true);
                }}
              >
                <Eye className="h-4 w-4 mr-2" />
                Ver Contenido
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Historial de System Messages */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Historial de System Messages
          </CardTitle>
        </CardHeader>
        <CardContent>
          {systemMessages && systemMessages.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Versión</TableHead>
                  <TableHead>Título</TableHead>
                  <TableHead>Estado</TableHead>
                  <TableHead>Creado</TableHead>
                  <TableHead>Acciones</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {systemMessages.map((message: any) => (
                  <TableRow key={message._id}>
                    <TableCell>
                      <Badge variant="outline">{message.version}</Badge>
                    </TableCell>
                    <TableCell className="font-medium">{message.title}</TableCell>
                    <TableCell>
                      {message.isActive ? (
                        <Badge className="bg-green-600">Activo</Badge>
                      ) : (
                        <Badge variant="secondary">Inactivo</Badge>
                      )}
                    </TableCell>
                    <TableCell>{formatDate(message.createdAt)}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedMessage(message);
                            setPreviewOpen(true);
                          }}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        
                        {!message.isActive && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleActivateMessage(message._id)}
                            disabled={loading === `activate-${message._id}`}
                          >
                            <CheckCircle className="h-4 w-4" />
                          </Button>
                        )}
                        
                        {!message.isActive && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteMessage(message._id)}
                            disabled={loading === `delete-${message._id}`}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No hay system messages creados</p>
              <p className="text-sm text-gray-400 mt-2">
                Haz clic en &quot;Inicializar&quot; para crear el system message por defecto
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Preview Dialog */}
      <Dialog open={previewOpen} onOpenChange={setPreviewOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              Preview: {selectedMessage?.title}
            </DialogTitle>
            <DialogDescription>
              Versión {selectedMessage?.version} - {selectedMessage?.description}
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-4">
            <pre className="whitespace-pre-wrap text-sm bg-gray-50 p-4 rounded-lg border overflow-auto max-h-[60vh]">
              {selectedMessage?.content}
            </pre>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setPreviewOpen(false)}>
              Cerrar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
