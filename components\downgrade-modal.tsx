"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON><PERSON>riangle, ArrowDown, Check, X } from "lucide-react";
import { toast } from "sonner";
import { useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";

interface DowngradeModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentPlan: "premium"; // Solo premium puede hacer downgrade
  onSuccess?: () => void;
}

export function DowngradeModal({ isOpen, onClose, currentPlan, onSuccess }: DowngradeModalProps) {
  const [loading, setLoading] = useState(false);
  const downgradePlan = useMutation(api.subscriptions.downgradePlan);

  const getDowngradeOptions = () => {
    // Solo hay una opción: downgrade de premium a free
    return [
      {
        plan: "free" as const,
        name: "<PERSON><PERSON><PERSON><PERSON>",
        price: "$0/mes",
        credits: 10,
        properties: 5,
        features: ["10 créditos mensuales", "5 propiedades", "Soporte básico"]
      }
    ];
  };

  const handleDowngrade = async (targetPlan: "free") => {
    try {
      setLoading(true);
      
      const result = await downgradePlan({ targetPlan });
      
      if (result.success) {
        toast.success(result.message);
        onSuccess?.();
        onClose();
      }
    } catch (error: any) {
      console.error("Error en downgrade:", error);
      toast.error(error.message || "Error al cambiar el plan");
    } finally {
      setLoading(false);
    }
  };

  const options = getDowngradeOptions();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ArrowDown className="h-5 w-5 text-orange-500" />
            Cambiar a un Plan Inferior
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="h-5 w-5 text-orange-500 mt-0.5" />
              <div>
                <h4 className="font-medium text-orange-800">Importante</h4>
                <p className="text-sm text-orange-700 mt-1">
                  Al cambiar a un plan inferior, podrías perder acceso a algunas funcionalidades. 
                  Asegúrate de que tu uso actual sea compatible con los límites del nuevo plan.
                </p>
              </div>
            </div>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            {options.map((option: any) => (
              <Card key={option.plan} className="relative">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>Plan {option.name}</span>
                    <span className="text-lg font-bold text-green-600">{option.price}</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Check className="h-4 w-4 text-green-500" />
                      <span className="text-sm">{option.credits} créditos mensuales</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Check className="h-4 w-4 text-green-500" />
                      <span className="text-sm">Hasta {option.properties} propiedades</span>
                    </div>
                    {option.features.slice(2).map((feature: any, index: any) => (
                      <div key={index} className="flex items-center gap-2">
                        <Check className="h-4 w-4 text-green-500" />
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>

                  <Button 
                    className="w-full" 
                    variant={option.plan === "free" ? "outline" : "default"}
                    onClick={() => handleDowngrade(option.plan)}
                    disabled={loading}
                  >
                    {loading ? "Procesando..." : `Cambiar a ${option.name}`}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="mt-6 text-center text-sm text-muted-foreground space-y-2">
            <p>✅ El cambio es inmediato</p>
            <p>✅ Puedes volver a actualizar en cualquier momento</p>
            <p>⚠️ Los créditos no utilizados se ajustarán al nuevo límite</p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
