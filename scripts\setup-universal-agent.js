#!/usr/bin/env node

/**
 * 🎯 Script: Configurar Agente Universal
 * 
 * Este script asigna el usuario actual del sistema como agente universal
 * para todas las propiedades existentes, habilitando la funcionalidad
 * completa del módulo de agenda.
 * 
 * Uso:
 *   node scripts/setup-universal-agent.js
 * 
 * Funcionalidades:
 * - Verifica el estado actual de asignación de agentes
 * - Asigna el usuario actual como agente universal
 * - Proporciona estadísticas detalladas del proceso
 * - Valida que la funcionalidad de agenda esté habilitada
 */

const { ConvexHttpClient } = require("convex/browser");

// Configuración
const CONVEX_URL = process.env.NEXT_PUBLIC_CONVEX_URL || "https://capable-cod-213.convex.cloud";

if (!CONVEX_URL) {
  console.error("❌ Error: NEXT_PUBLIC_CONVEX_URL no está configurado");
  process.exit(1);
}

const client = new ConvexHttpClient(CONVEX_URL);

// Función principal
async function setupUniversalAgent() {
  console.log("🚀 Iniciando configuración de Agente Universal...\n");

  try {
    // Paso 1: Verificar estado actual
    console.log("📊 Paso 1: Verificando estado actual de asignación...");
    
    const currentStatus = await client.query("properties:getAgentAssignmentStatus");
    
    console.log("\n📈 Estado Actual:");
    console.log(`👤 Usuario Actual: ${currentStatus.currentUser.name} (${currentStatus.currentUser.email})`);
    console.log(`🏠 Total Propiedades: ${currentStatus.statistics.totalProperties}`);
    console.log(`✅ Con Agente: ${currentStatus.statistics.withAgent}`);
    console.log(`❌ Sin Agente: ${currentStatus.statistics.withoutAgent}`);
    console.log(`🎯 Asignadas al Usuario Actual: ${currentStatus.statistics.assignedToCurrentUser} (${currentStatus.statistics.percentageAssignedToCurrentUser}%)`);
    console.log(`👥 Asignadas a Otros: ${currentStatus.statistics.assignedToOthers}`);

    if (currentStatus.otherAgents.length > 0) {
      console.log("\n👥 Otros Agentes Encontrados:");
      currentStatus.otherAgents.forEach(agent => {
        console.log(`   - ${agent.name} (${agent.email}): ${agent.propertiesCount} propiedades`);
      });
    }

    // Verificar si es necesario hacer cambios
    if (!currentStatus.readyForUniversalAssignment) {
      console.log("\n✅ ¡Perfecto! Todas las propiedades ya están asignadas al usuario actual.");
      console.log("🎉 La funcionalidad de agenda debería estar completamente habilitada.");
      return;
    }

    // Paso 2: Confirmar acción
    console.log(`\n⚠️  Se van a actualizar ${currentStatus.statistics.withoutAgent + currentStatus.statistics.assignedToOthers} propiedades`);
    console.log("🔄 Esto asignará el usuario actual como agente universal para todas las propiedades.");
    
    // En un entorno de producción, podrías agregar confirmación interactiva aquí
    console.log("⏳ Procediendo con la asignación...\n");

    // Paso 3: Ejecutar asignación
    console.log("🔧 Paso 2: Ejecutando asignación de agente universal...");
    
    const result = await client.mutation("properties:assignCurrentUserAsUniversalAgent");
    
    console.log("\n✅ Asignación Completada:");
    console.log(`📝 ${result.message}`);
    console.log(`🏠 Total Propiedades: ${result.details.totalProperties}`);
    console.log(`🔄 Actualizadas: ${result.details.updatedCount}`);
    console.log(`✅ Ya Asignadas: ${result.details.alreadyAssignedCount}`);
    
    if (result.details.errors) {
      console.log(`❌ Errores: ${result.details.errors.length}`);
      result.details.errors.forEach(error => {
        console.log(`   - ${error}`);
      });
    }

    console.log("\n👤 Agente Asignado:");
    console.log(`   - Nombre: ${result.details.agentInfo.name}`);
    console.log(`   - Email: ${result.details.agentInfo.email}`);
    console.log(`   - ID: ${result.details.agentInfo.userId}`);
    console.log(`   - Rol: ${result.details.agentInfo.role}`);

    // Paso 4: Verificar resultado final
    console.log("\n🔍 Paso 3: Verificando resultado final...");
    
    const finalStatus = await client.query("properties:getAgentAssignmentStatus");
    
    console.log("\n📊 Estado Final:");
    console.log(`🎯 Asignadas al Usuario Actual: ${finalStatus.statistics.assignedToCurrentUser} (${finalStatus.statistics.percentageAssignedToCurrentUser}%)`);
    console.log(`❌ Sin Agente: ${finalStatus.statistics.withoutAgent}`);
    console.log(`👥 Asignadas a Otros: ${finalStatus.statistics.assignedToOthers}`);

    // Validación final
    if (finalStatus.statistics.percentageAssignedToCurrentUser === 100) {
      console.log("\n🎉 ¡ÉXITO COMPLETO!");
      console.log("✅ Todas las propiedades están asignadas al usuario actual");
      console.log("✅ La funcionalidad de agenda está completamente habilitada");
      console.log("✅ Los usuarios podrán agendar citas para cualquier propiedad");
    } else {
      console.log("\n⚠️  Asignación parcial completada");
      console.log(`📊 ${finalStatus.statistics.percentageAssignedToCurrentUser}% de propiedades asignadas`);
      
      if (finalStatus.statistics.withoutAgent > 0) {
        console.log(`❌ ${finalStatus.statistics.withoutAgent} propiedades aún sin agente`);
      }
      
      if (finalStatus.statistics.assignedToOthers > 0) {
        console.log(`👥 ${finalStatus.statistics.assignedToOthers} propiedades asignadas a otros agentes`);
      }
    }

    console.log("\n📋 Próximos Pasos:");
    console.log("1. ✅ Probar funcionalidad de agendamiento en el frontend");
    console.log("2. ✅ Verificar que las notificaciones lleguen correctamente");
    console.log("3. ✅ Confirmar que el flujo end-to-end funciona");

  } catch (error) {
    console.error("\n❌ Error durante la configuración:");
    console.error(error.message);
    
    if (error.message.includes("No autenticado")) {
      console.error("\n💡 Solución: Asegúrate de estar autenticado en el sistema");
      console.error("   - Inicia sesión en la aplicación web");
      console.error("   - Verifica que tengas permisos de administrador");
    }
    
    process.exit(1);
  }
}

// Función de ayuda
function showHelp() {
  console.log(`
🎯 Script: Configurar Agente Universal

Uso:
  node scripts/setup-universal-agent.js

Descripción:
  Este script asigna el usuario actual del sistema como agente universal
  para todas las propiedades existentes, habilitando la funcionalidad
  completa del módulo de agenda.

Requisitos:
  - Usuario autenticado en el sistema
  - Variable NEXT_PUBLIC_CONVEX_URL configurada
  - Permisos para modificar propiedades

Funcionalidades:
  ✅ Verifica estado actual de asignación
  ✅ Asigna usuario como agente universal
  ✅ Proporciona estadísticas detalladas
  ✅ Valida funcionalidad de agenda

Ejemplos:
  # Ejecutar configuración
  node scripts/setup-universal-agent.js

  # Ver ayuda
  node scripts/setup-universal-agent.js --help
`);
}

// Manejo de argumentos
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  showHelp();
  process.exit(0);
}

// Ejecutar script
if (require.main === module) {
  setupUniversalAgent()
    .then(() => {
      console.log("\n🎉 Script completado exitosamente!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("\n💥 Error fatal:", error);
      process.exit(1);
    });
}

module.exports = { setupUniversalAgent };
