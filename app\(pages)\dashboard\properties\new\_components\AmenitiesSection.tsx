"use client";

import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";

interface AmenitiesSectionProps {
  selectedAmenities: string[];
  onAmenitiesChange: (amenities: string[]) => void;
}

export default function AmenitiesSection({ selectedAmenities, onAmenitiesChange }: AmenitiesSectionProps) {
  const amenitiesGrouped = useQuery(api.amenities.getAmenitiesGroupedByCategory);

  const handleAmenityToggle = (amenityName: string, checked: boolean) => {
    if (checked) {
      onAmenitiesChange([...selectedAmenities, amenityName]);
    } else {
      onAmenitiesChange(selectedAmenities.filter(a => a !== amenityName));
    }
  };

  const getCategoryLabel = (category: string) => {
    const labels: Record<string, string> = {
      servicios: "Servicios",
      comodidades: "Comodidades",
      ubicacion: "Ubicación",
      vistas: "Vistas"
    };
    return labels[category] || category;
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      servicios: "bg-blue-100 text-blue-800",
      comodidades: "bg-green-100 text-green-800", 
      ubicacion: "bg-purple-100 text-purple-800",
      vistas: "bg-orange-100 text-orange-800"
    };
    return colors[category] || "bg-gray-100 text-gray-800";
  };

  if (!amenitiesGrouped) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Amenidades</CardTitle>
          <CardDescription>
            Selecciona las amenidades que tiene tu propiedad
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {[1, 2, 3, 4].map((i: any) => (
              <div key={i} className="space-y-3">
                <Skeleton className="h-6 w-24" />
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {[1, 2, 3, 4, 5, 6].map((j: any) => (
                    <Skeleton key={j} className="h-10 w-full" />
                  ))}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Amenidades</CardTitle>
        <CardDescription>
          Selecciona las amenidades que tiene tu propiedad
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {Object.entries(amenitiesGrouped).map(([category, amenities]) => (
            <div key={category} className="space-y-3">
              <div className="flex items-center gap-2">
                <Badge className={getCategoryColor(category)}>
                  {getCategoryLabel(category)}
                </Badge>
                <span className="text-sm text-muted-foreground">
                  ({(amenities as any[])?.length || 0} opciones)
                </span>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {(amenities as any[])?.map((amenity: any) => (
                  <div 
                    key={amenity._id} 
                    className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <Checkbox 
                      id={amenity._id}
                      checked={selectedAmenities.includes(amenity.name)}
                      onCheckedChange={(checked) => handleAmenityToggle(amenity.name, checked as boolean)}
                    />
                    <div className="flex-1">
                      <Label 
                        htmlFor={amenity._id} 
                        className="text-sm font-medium leading-none cursor-pointer"
                      >
                        {amenity.name}
                      </Label>
                      {amenity.description && (
                        <p className="text-xs text-muted-foreground mt-1">
                          {amenity.description}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {selectedAmenities.length > 0 && (
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">
              Amenidades seleccionadas ({selectedAmenities.length})
            </h4>
            <div className="flex flex-wrap gap-2">
              {selectedAmenities.map((amenity: any) => (
                <Badge key={amenity} variant="secondary" className="bg-blue-100 text-blue-800">
                  {amenity}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 