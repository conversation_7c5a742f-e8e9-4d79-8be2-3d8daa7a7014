/**
 * SCRIPT DE COMPARACIÓN: SISTEMA SIMPLE VS MULTI-COMPONENTE
 * 
 * Ejecuta las mismas consultas en ambos sistemas y compara:
 * - Tiempo de respuesta
 * - Precisión de resultados  
 * - Calidad semántica
 * - Número de resultados
 */

const fetch = require('node-fetch');

// Configuración
const API_BASE_URL = process.env.API_BASE_URL || 'https://inmo-nine.vercel.app';
const API_KEY = process.env.NEXT_PUBLIC_RAG_API_KEY || 'demo-rag-key-2025';

// Mostrar configuración
console.log('🔧 Configuración:');
console.log(`   API URL: ${API_BASE_URL}`);
console.log(`   API Key: ${API_KEY.substring(0, 8)}...`);
console.log('');
const ITERATIONS_PER_QUERY = 3; // Ejecutar cada consulta 3 veces para promediar

// Consultas de prueba organizadas por categoría
const TEST_QUERIES = {
  basicas: [
    "apartamento zona 14",
    "casa en venta", 
    "alquiler zona 10",
    "comprar apartamento",
    "propiedad guatemala"
  ],
  especificas: [
    "comprar apto z14",
    "apartamento 3 habitaciones zona 14", 
    "casa venta zona 15 piscina",
    "alquiler 2 habitaciones zona viva",
    "apartamento lujo zona 14 parqueo"
  ],
  abreviaciones: [
    "apto z10",
    "casa z15", 
    "depto zona 14",
    "apt venta",
    "prop alquiler"
  ],
  sinonimos: [
    "arrendar apartamento",
    "conseguir casa",
    "invertir propiedad", 
    "rentar depto",
    "adquirir apartamento"
  ],
  edge_cases: [
    "apartamento",
    "zona 14", 
    "venta",
    "guatemala casa apartamento",
    "z14 apto 3 hab piscina parqueo"
  ]
};

// Configuraciones de sistemas a probar
const SYSTEM_CONFIGS = {
  simple: {
    name: "Sistema Simple",
    config: {
      searchType: "simple",
      adaptiveThreshold: true,
      intentDetection: true
    }
  },
  multicomponent: {
    name: "Sistema Multi-componente", 
    config: {
      searchType: "semantic",
      useSemanticWeights: true,
      includeBreakdown: true
    }
  }
};

// Función para ejecutar una búsqueda
async function executeSearch(query, systemConfig) {
  const startTime = Date.now();
  
  try {
    const response = await fetch(`${API_BASE_URL}/api/v1/search`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY,
      },
      body: JSON.stringify({
        query,
        options: {
          limit: 12,
          includeResponse: false, // Solo necesitamos los resultados
          responseLanguage: 'es',
          ...systemConfig
        },
      }),
    });

    const endTime = Date.now();
    const responseTime = endTime - startTime;

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // La API devuelve 'properties' no 'results'
    const properties = data.properties || [];

    return {
      success: true,
      responseTime,
      resultCount: properties.length,
      results: properties,
      avgScore: properties.length > 0
        ? properties.reduce((sum, r) => sum + r.score, 0) / properties.length
        : 0,
      topScore: properties.length > 0 ? properties[0].score : 0,
      top3Results: properties.slice(0, 3)
    };

  } catch (error) {
    return {
      success: false,
      responseTime: Date.now() - startTime,
      resultCount: 0,
      results: [],
      avgScore: 0,
      topScore: 0,
      top3Results: [],
      error: error.message
    };
  }
}

// Función para ejecutar múltiples iteraciones de una consulta
async function executeQueryIterations(query, systemConfig, iterations = ITERATIONS_PER_QUERY) {
  console.log(`  Ejecutando "${query}" ${iterations} veces...`);
  
  const results = [];
  for (let i = 0; i < iterations; i++) {
    const result = await executeSearch(query, systemConfig);
    results.push(result);
    
    // Pequeña pausa entre iteraciones
    if (i < iterations - 1) {
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }
  
  // Calcular promedios
  const successfulResults = results.filter(r => r.success);
  const avgMetrics = {
    successRate: (successfulResults.length / results.length) * 100,
    avgResponseTime: successfulResults.reduce((sum, r) => sum + r.responseTime, 0) / successfulResults.length || 0,
    avgResultCount: successfulResults.reduce((sum, r) => sum + r.resultCount, 0) / successfulResults.length || 0,
    avgScore: successfulResults.reduce((sum, r) => sum + r.avgScore, 0) / successfulResults.length || 0,
    avgTopScore: successfulResults.reduce((sum, r) => sum + r.topScore, 0) / successfulResults.length || 0,
    iterations: results.length,
    errors: results.filter(r => !r.success).map(r => r.error)
  };
  
  return {
    query,
    iterations: results,
    metrics: avgMetrics,
    sampleResults: successfulResults.length > 0 ? successfulResults[0].top3Results : []
  };
}

// Función principal de comparación
async function runComparison() {
  console.log('🧪 INICIANDO COMPARACIÓN: SISTEMA SIMPLE VS MULTI-COMPONENTE');
  console.log('=' .repeat(70));
  
  const results = {
    simple: {},
    multicomponent: {},
    comparison: {}
  };
  
  // Ejecutar pruebas para cada sistema
  for (const [systemKey, systemInfo] of Object.entries(SYSTEM_CONFIGS)) {
    console.log(`\n🔍 PROBANDO: ${systemInfo.name}`);
    console.log('-'.repeat(50));
    
    results[systemKey] = {};
    
    for (const [category, queries] of Object.entries(TEST_QUERIES)) {
      console.log(`\n📂 Categoría: ${category.toUpperCase()}`);
      results[systemKey][category] = {};
      
      for (const query of queries) {
        const queryResult = await executeQueryIterations(query, systemInfo.config);
        results[systemKey][category][query] = queryResult;
        
        // Log resultado inmediato
        const metrics = queryResult.metrics;
        console.log(`    ✅ "${query}": ${metrics.avgResultCount.toFixed(1)} resultados, ${metrics.avgResponseTime.toFixed(0)}ms, score: ${metrics.avgScore.toFixed(3)}`);
      }
    }
  }
  
  // Generar comparación
  console.log('\n📊 GENERANDO ANÁLISIS COMPARATIVO...');
  generateComparison(results);
  
  return results;
}

// Función para generar análisis comparativo
function generateComparison(results) {
  console.log('\n' + '='.repeat(70));
  console.log('📊 ANÁLISIS COMPARATIVO FINAL');
  console.log('='.repeat(70));
  
  // Calcular métricas globales
  const globalMetrics = {};
  
  for (const [systemKey, systemResults] of Object.entries(results)) {
    if (systemKey === 'comparison') continue;
    
    let totalQueries = 0;
    let totalTime = 0;
    let totalResults = 0;
    let totalScore = 0;
    let successfulQueries = 0;
    
    for (const [category, categoryResults] of Object.entries(systemResults)) {
      for (const [query, queryResult] of Object.entries(categoryResults)) {
        totalQueries++;
        totalTime += queryResult.metrics.avgResponseTime;
        totalResults += queryResult.metrics.avgResultCount;
        totalScore += queryResult.metrics.avgScore;
        if (queryResult.metrics.avgResultCount > 0) successfulQueries++;
      }
    }
    
    globalMetrics[systemKey] = {
      totalQueries,
      avgResponseTime: totalTime / totalQueries,
      avgResultCount: totalResults / totalQueries,
      avgScore: totalScore / totalQueries,
      successRate: (successfulQueries / totalQueries) * 100
    };
  }
  
  // Mostrar comparación
  const simple = globalMetrics.simple;
  const multi = globalMetrics.multicomponent;
  
  console.log('\n🏆 MÉTRICAS GLOBALES:');
  console.log(`┌─────────────────────┬─────────────────┬─────────────────┬─────────────┐`);
  console.log(`│ Métrica             │ Sistema Simple  │ Multi-componente│ Ganador     │`);
  console.log(`├─────────────────────┼─────────────────┼─────────────────┼─────────────┤`);
  console.log(`│ Tiempo Promedio     │ ${simple.avgResponseTime.toFixed(0).padStart(10)}ms    │ ${multi.avgResponseTime.toFixed(0).padStart(10)}ms    │ ${simple.avgResponseTime < multi.avgResponseTime ? 'Simple 🏆' : 'Multi 🏆'.padStart(11)}  │`);
  console.log(`│ Resultados Promedio │ ${simple.avgResultCount.toFixed(1).padStart(13)}   │ ${multi.avgResultCount.toFixed(1).padStart(13)}   │ ${simple.avgResultCount > multi.avgResultCount ? 'Simple 🏆' : 'Multi 🏆'.padStart(11)}  │`);
  console.log(`│ Score Promedio      │ ${simple.avgScore.toFixed(3).padStart(13)}   │ ${multi.avgScore.toFixed(3).padStart(13)}   │ ${simple.avgScore > multi.avgScore ? 'Simple 🏆' : 'Multi 🏆'.padStart(11)}  │`);
  console.log(`│ Tasa de Éxito       │ ${simple.successRate.toFixed(1).padStart(11)}%    │ ${multi.successRate.toFixed(1).padStart(11)}%    │ ${simple.successRate > multi.successRate ? 'Simple 🏆' : 'Multi 🏆'.padStart(11)}  │`);
  console.log(`└─────────────────────┴─────────────────┴─────────────────┴─────────────┘`);
  
  // Calcular diferencias porcentuales
  const timeDiff = ((multi.avgResponseTime - simple.avgResponseTime) / simple.avgResponseTime * 100);
  const resultDiff = ((simple.avgResultCount - multi.avgResultCount) / multi.avgResultCount * 100);
  const scoreDiff = ((simple.avgScore - multi.avgScore) / multi.avgScore * 100);
  
  console.log('\n📈 DIFERENCIAS PORCENTUALES:');
  console.log(`⏱️  Sistema Simple es ${Math.abs(timeDiff).toFixed(1)}% ${timeDiff < 0 ? 'más lento' : 'más rápido'}`);
  console.log(`📊 Sistema Simple encuentra ${Math.abs(resultDiff).toFixed(1)}% ${resultDiff < 0 ? 'menos' : 'más'} resultados`);
  console.log(`🎯 Sistema Simple tiene ${Math.abs(scoreDiff).toFixed(1)}% ${scoreDiff < 0 ? 'menor' : 'mayor'} score promedio`);
  
  console.log('\n🎯 RECOMENDACIÓN:');
  if (simple.avgResponseTime < multi.avgResponseTime && Math.abs(scoreDiff) < 5) {
    console.log('✅ USAR SISTEMA SIMPLE: Más rápido con calidad similar');
  } else if (multi.avgScore > simple.avgScore && scoreDiff > 10) {
    console.log('✅ USAR SISTEMA MULTI-COMPONENTE: Mejor calidad justifica la lentitud');
  } else {
    console.log('⚖️  AMBOS SISTEMAS SON VIABLES: Decidir según prioridades (velocidad vs precisión)');
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  runComparison()
    .then(() => {
      console.log('\n✅ Comparación completada');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Error en comparación:', error);
      process.exit(1);
    });
}

module.exports = { runComparison, executeSearch, TEST_QUERIES, SYSTEM_CONFIGS };
