import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { api } from "./_generated/api";
import { Id } from "./_generated/dataModel";

// === FUNCIONES PARA INTEGRACIÓN CON N8N Y ASISTENTE IA ===

// Verificar disponibilidad para una propiedad específica
export const checkPropertyAvailability = query({
  args: {
    propertyId: v.id("properties"),
    preferredDates: v.array(v.string()),
    duration: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    const property = await ctx.db.get(args.propertyId);
    if (!property) {
      throw new Error("Propiedad no encontrada");
    }

    // 🚨 MEJORADO: Usar agentId si está disponible, sino usar ownerId
    const responsibleUserId = property.agentId || property.ownerId;
    const responsibleUserType = property.agentId ? "agente" : "propietario";

    const availability = await ctx.db
      .query("availability")
      .withIndex("by_user", (q) => q.eq("userId", responsibleUserId))
      .filter((q: any) => q.eq(q.field("isEnabled"), true))
      .collect();

    if (availability.length === 0) {
      return {
        hasAvailability: false,
        message: `El ${responsibleUserType} no ha configurado su disponibilidad.`,
        availableSlots: []
      };
    }

    const allAvailableSlots = [];
    
    for (const dateStr of args.preferredDates) {
      const targetDate = new Date(dateStr);
      const dayOfWeek = targetDate.getDay() as 0 | 1 | 2 | 3 | 4 | 5 | 6;
      const duration = args.duration || 60;

      const dayAvailability = availability.find(a => a.dayOfWeek === dayOfWeek);
      if (!dayAvailability) continue;

      const startOfDay = new Date(targetDate);
      startOfDay.setHours(0, 0, 0, 0);
      const endOfDay = new Date(targetDate);
      endOfDay.setHours(23, 59, 59, 999);

      const existingAppointments = await ctx.db
        .query("appointments")
        .withIndex("by_host_date", (q) => q.eq("hostId", responsibleUserId))
        .filter((q: any) =>
          q.and(
            q.gte(q.field("startTime"), startOfDay.toISOString()),
            q.lte(q.field("startTime"), endOfDay.toISOString()),
            q.neq(q.field("status"), "cancelled")
          )
        )
        .collect();

      const [startHour, startMinute] = dayAvailability.startTime.split(':').map(Number);
      const [endHour, endMinute] = dayAvailability.endTime.split(':').map(Number);
      
      const slotDuration = dayAvailability.slotDuration || duration;
      const breakTime = dayAvailability.breakTime || 0;

      let currentTime = new Date(targetDate);
      currentTime.setHours(startHour, startMinute, 0, 0);
      
      const endTime = new Date(targetDate);
      endTime.setHours(endHour, endMinute, 0, 0);

      while (currentTime < endTime) {
        const slotEnd = new Date(currentTime.getTime() + slotDuration * 60000);
        
        if (slotEnd <= endTime) {
          const hasConflict = existingAppointments.some(appointment => {
            const appointmentStart = new Date(appointment.startTime);
            const appointmentEnd = new Date(appointment.endTime);
            return (currentTime < appointmentEnd && slotEnd > appointmentStart);
          });

          if (!hasConflict) {
            allAvailableSlots.push({
              date: dateStr,
              startTime: currentTime.toISOString(),
              endTime: slotEnd.toISOString(),
              duration: slotDuration,
              dayName: targetDate.toLocaleDateString('es-ES', { weekday: 'long' }),
              timeSlot: `${currentTime.getHours().toString().padStart(2, '0')}:${currentTime.getMinutes().toString().padStart(2, '0')}`
            });
          }
        }

        currentTime = new Date(currentTime.getTime() + (slotDuration + breakTime) * 60000);
      }
    }

    return {
      hasAvailability: true,
      property: {
        title: property.title,
        address: property.address,
        city: property.location?.level2?.name || ''
      },
      owner: property.ownerId, // Mantener para compatibilidad
      responsibleUser: responsibleUserId, // Nuevo campo que indica quién maneja la agenda
      responsibleUserType, // "agente" o "propietario"
      availableSlots: allAvailableSlots,
      message: allAvailableSlots.length > 0
        ? `Encontré ${allAvailableSlots.length} horarios disponibles para visitar esta propiedad.`
        : "No hay horarios disponibles en las fechas solicitadas. ¿Te gustaría revisar otras fechas?"
    };
  },
});

// 🔍 Función auxiliar para verificar duplicados
export const checkAppointmentDuplicates = query({
  args: {
    propertyId: v.id("properties"),
    guestEmail: v.string(),
    requestedStartTime: v.string(),
  },
  handler: async (ctx, args) => {
    // Buscar solicitudes pendientes para la misma propiedad y email
    const exactDuplicates = await ctx.db
      .query("appointmentRequests")
      .filter((q) =>
        q.and(
          q.eq(q.field("propertyId"), args.propertyId),
          q.eq(q.field("guestEmail"), args.guestEmail),
          q.eq(q.field("status"), "pending")
        )
      )
      .collect();

    // Buscar conflictos del mismo día
    const requestedDate = new Date(args.requestedStartTime).toDateString();
    const sameDayRequests = await ctx.db
      .query("appointmentRequests")
      .filter((q) =>
        q.and(
          q.eq(q.field("guestEmail"), args.guestEmail),
          q.or(
            q.eq(q.field("status"), "pending"),
            q.eq(q.field("status"), "confirmed")
          )
        )
      )
      .collect();

    const sameDayConflicts = sameDayRequests.filter(req =>
      new Date(req.requestedStartTime).toDateString() === requestedDate
    );

    return {
      hasExactDuplicate: exactDuplicates.length > 0,
      exactDuplicates,
      hasSameDayConflicts: sameDayConflicts.length > 0,
      sameDayConflicts,
      totalConflicts: exactDuplicates.length + sameDayConflicts.length
    };
  },
});

// Crear solicitud de cita automatizada por el asistente
export const createAIAppointmentRequest = mutation({
  args: {
    propertyId: v.id("properties"),
    guestName: v.string(),
    guestEmail: v.string(),
    guestPhone: v.optional(v.string()),
    requestedStartTime: v.string(),
    requestedEndTime: v.string(),
    message: v.optional(v.string()),
    type: v.union(
      v.literal("property_viewing"),
      v.literal("consultation"),
      v.literal("negotiation"),
      v.literal("document_signing"),
      v.literal("other")
    ),
    meetingType: v.union(
      v.literal("in_person"),
      v.literal("video_call"),
      v.literal("phone_call")
    ),
    source: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    const property = await ctx.db.get(args.propertyId);
    if (!property) {
      throw new Error("Propiedad no encontrada");
    }

    // 🚨 VALIDACIÓN ANTI-DUPLICADOS
    // Buscar solicitudes existentes para la misma propiedad y email
    const existingRequests = await ctx.db
      .query("appointmentRequests")
      .filter((q) =>
        q.and(
          q.eq(q.field("propertyId"), args.propertyId),
          q.eq(q.field("guestEmail"), args.guestEmail),
          q.eq(q.field("status"), "pending")
        )
      )
      .collect();

    // NIVEL 1 - CRÍTICO: Bloquear duplicados absolutos
    if (existingRequests.length > 0) {
      const existingRequest = existingRequests[0];
      const existingDate = new Date(existingRequest.requestedStartTime).toLocaleDateString('es-GT');

      return {
        success: false,
        isDuplicate: true,
        message: `Ya tienes una solicitud de cita pendiente para esta propiedad (${existingDate}). Por favor espera la respuesta del propietario o contacta para modificar la fecha.`,
        existingRequest: {
          id: existingRequest._id,
          requestedDate: existingDate,
          status: existingRequest.status,
          createdAt: existingRequest.createdAt
        }
      };
    }

    // NIVEL 2 - ADVERTENCIA: Verificar mismo día (diferentes propiedades o estados)
    const requestedDate = new Date(args.requestedStartTime).toDateString();
    const sameDayRequests = await ctx.db
      .query("appointmentRequests")
      .filter((q) =>
        q.and(
          q.eq(q.field("guestEmail"), args.guestEmail),
          q.or(
            q.eq(q.field("status"), "pending"),
            q.eq(q.field("status"), "confirmed")
          )
        )
      )
      .collect();

    const sameDayConflicts = sameDayRequests.filter(req =>
      new Date(req.requestedStartTime).toDateString() === requestedDate
    );

    // Obtener información del propietario o agente
    // Primero intentar encontrar por agentId si existe
    let contactPerson = null;

    if (property.agentId) {
      contactPerson = await ctx.db
        .query("users")
        .filter((q) => q.eq(q.field("userId"), property.agentId))
        .first();
    }

    // Si no hay agente o no se encontró, buscar por ownerId
    if (!contactPerson && property.ownerId) {
      contactPerson = await ctx.db
        .query("users")
        .filter((q) => q.eq(q.field("userId"), property.ownerId))
        .first();
    }

    if (!contactPerson) {
      throw new Error(`No se encontró contacto para la propiedad. ownerId: ${property.ownerId}, agentId: ${property.agentId}`);
    }

    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);

    const requestId = await ctx.db.insert("appointmentRequests", {
      hostId: contactPerson.userId, // Usar el userId del contacto encontrado
      guestId: `ai-guest-${Date.now()}`,
      guestName: args.guestName,
      guestEmail: args.guestEmail,
      guestPhone: args.guestPhone,
      propertyId: args.propertyId,
      requestedStartTime: args.requestedStartTime,
      requestedEndTime: args.requestedEndTime,
      message: args.message,
      type: args.type,
      meetingType: args.meetingType,
      status: "pending",
      createdAt: new Date().toISOString(),
      expiresAt: expiresAt.toISOString(),
    });

    // 📧 ENVIAR NOTIFICACIONES POR EMAIL
    // Ejecutamos las notificaciones de forma inmediata pero sin bloquear
    ctx.scheduler.runAfter(0, api.emails.notifyAppointmentRequest, {
      ownerEmail: contactPerson.email,
      ownerName: contactPerson.name || (property.agentId ? "Agente" : "Propietario"),
      guestEmail: args.guestEmail,
      guestName: args.guestName,
      guestPhone: args.guestPhone,
      propertyTitle: property.title,
      propertyAddress: `${property.address}${property.location?.level2?.name ? `, ${property.location.level2.name}` : ''}`,
      propertyPrice: `${property.currency} ${property.price?.toLocaleString()}`,
      requestedStartTime: args.requestedStartTime,
      requestedEndTime: args.requestedEndTime,
      meetingType: args.meetingType,
      message: args.message,
      dashboardUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/appointments`,
    }).catch((error) => {
      console.error("Error programando notificaciones:", error);
      // No fallar la solicitud si falla el email
    });

    // Preparar mensaje de respuesta con advertencias si hay conflictos
    let responseMessage = "Solicitud de cita creada exitosamente. El propietario será notificado por email.";

    if (sameDayConflicts.length > 0) {
      responseMessage += ` ⚠️ Nota: Ya tienes ${sameDayConflicts.length} cita(s) programada(s) para el mismo día.`;
    }

    return {
      success: true,
      requestId,
      message: responseMessage,
      expiresAt: expiresAt.toISOString(),
      warnings: sameDayConflicts.length > 0 ? {
        sameDayConflicts: sameDayConflicts.length,
        conflictDetails: sameDayConflicts.map(req => ({
          propertyTitle: req.propertyId ? "Propiedad diferente" : "Sin propiedad",
          requestedTime: new Date(req.requestedStartTime).toLocaleTimeString('es-GT', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
          }),
          status: req.status
        }))
      } : null
    };
  },
});

// Función de diagnóstico para revisar datos de propiedades y usuarios
export const diagnosePropertyUserData = query({
  args: {
    propertyId: v.optional(v.id("properties")),
    limit: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 5;

    if (args.propertyId) {
      // Diagnóstico de una propiedad específica
      const property = await ctx.db.get(args.propertyId);
      if (!property) {
        return { error: "Propiedad no encontrada" };
      }

      const ownerUser = property.ownerId ? await ctx.db
        .query("users")
        .filter((q) => q.eq(q.field("userId"), property.ownerId))
        .first() : null;

      const agentUser = property.agentId ? await ctx.db
        .query("users")
        .filter((q) => q.eq(q.field("userId"), property.agentId))
        .first() : null;

      return {
        property: {
          id: property._id,
          title: property.title,
          ownerId: property.ownerId,
          agentId: property.agentId
        },
        ownerUser: ownerUser ? {
          userId: ownerUser.userId,
          email: ownerUser.email,
          name: ownerUser.name,
          role: ownerUser.role
        } : null,
        agentUser: agentUser ? {
          userId: agentUser.userId,
          email: agentUser.email,
          name: agentUser.name,
          role: agentUser.role
        } : null,
        recommendation: agentUser ? "Usar agentUser como contacto" : ownerUser ? "Usar ownerUser como contacto" : "❌ No hay contacto válido"
      };
    } else {
      // Diagnóstico general de varias propiedades
      const properties = await ctx.db.query("properties").take(limit);
      const results = [];

      for (const property of properties) {
        const ownerUser = property.ownerId ? await ctx.db
          .query("users")
          .filter((q) => q.eq(q.field("userId"), property.ownerId))
          .first() : null;

        const agentUser = property.agentId ? await ctx.db
          .query("users")
          .filter((q) => q.eq(q.field("userId"), property.agentId))
          .first() : null;

        results.push({
          propertyId: property._id,
          title: property.title,
          ownerId: property.ownerId,
          agentId: property.agentId,
          hasValidOwner: !!ownerUser,
          hasValidAgent: !!agentUser,
          contactAvailable: !!(agentUser || ownerUser)
        });
      }

      return {
        totalChecked: results.length,
        propertiesWithValidContact: results.filter(r => r.contactAvailable).length,
        propertiesWithoutContact: results.filter(r => !r.contactAvailable).length,
        details: results
      };
    }
  },
});

// Obtener contexto completo de una propiedad para el asistente
export const getPropertyContextForAI = query({
  args: { propertyId: v.id("properties") },
  handler: async (ctx, args) => {
    const property = await ctx.db.get(args.propertyId);
    if (!property) {
      throw new Error("Propiedad no encontrada");
    }

    const owner = await ctx.db
      .query("users")
      .withIndex("by_token", (q) => q.eq("tokenIdentifier", property.ownerId))
      .first();

    const availability = await ctx.db
      .query("availability")
      .withIndex("by_user", (q) => q.eq("userId", property.ownerId))
      .filter((q: any) => q.eq(q.field("isEnabled"), true))
      .collect();

    const recentRequests = await ctx.db
      .query("appointmentRequests")
      .withIndex("by_property", (q) => q.eq("propertyId", args.propertyId))
      .filter((q: any) => q.eq(q.field("status"), "pending"))
      .order("desc")
      .take(3);

    const upcomingAppointments = await ctx.db
      .query("appointments")
      .withIndex("by_property", (q) => q.eq("propertyId", args.propertyId))
      .filter((q: any) => 
        q.and(
          q.gte(q.field("startTime"), new Date().toISOString()),
          q.neq(q.field("status"), "cancelled")
        )
      )
      .order("asc")
      .take(5);

    return {
      property: {
        id: property._id,
        title: property.title,
        address: property.address,
        city: property.location?.level2?.name || '',
        state: property.location?.level1?.name || '',
        type: property.type,
        status: property.status,
        price: property.price,
        currency: property.currency,
        bedrooms: property.bedrooms,
        bathrooms: property.bathrooms,
        area: property.area,
        amenities: property.amenities
      },
      owner: owner ? {
        name: owner.name || "Propietario",
        role: owner.role || "seller",
        company: owner.company,
        email: owner.email,
        phone: owner.phone
      } : null,
      availability: {
        configured: availability.length > 0,
        schedule: availability.map((a: any) => ({
          dayOfWeek: a.dayOfWeek,
          startTime: a.startTime,
          endTime: a.endTime,
          slotDuration: a.slotDuration || 60
        }))
      },
      activity: {
        pendingRequests: recentRequests.length,
        upcomingAppointments: upcomingAppointments.length,
        lastRequestDate: recentRequests[0]?.createdAt,
        nextAppointmentDate: upcomingAppointments[0]?.startTime
      }
    };
  },
});

// Consultar estado de citas para el asistente IA
export const consultarEstadoCita = query({
  args: {
    // Criterios de búsqueda flexibles
    guestName: v.optional(v.string()),
    guestEmail: v.optional(v.string()),
    guestPhone: v.optional(v.string()),
    propertyId: v.optional(v.id("properties")),
    fecha: v.optional(v.string()), // YYYY-MM-DD
    chatId: v.optional(v.string()) // Para buscar por conversación de WhatsApp
  },
  handler: async (ctx, args) => {
    // Validar que al menos un criterio de búsqueda esté presente
    const hasCriteria = args.guestName || args.guestEmail || args.guestPhone ||
                       args.propertyId || args.fecha || args.chatId;

    if (!hasCriteria) {
      return {
        success: false,
        message: "Necesito al menos un criterio de búsqueda: nombre, email, teléfono, propiedad o fecha.",
        appointments: [],
        requests: []
      };
    }

    let appointments = [];
    let requests = [];

    try {
      // Buscar en citas confirmadas (appointments)
      let appointmentsQuery = ctx.db.query("appointments");

      if (args.guestEmail) {
        appointmentsQuery = appointmentsQuery.filter((q: any) =>
          q.eq(q.field("guestEmail"), args.guestEmail)
        );
      } else if (args.guestName) {
        appointmentsQuery = appointmentsQuery.filter((q: any) =>
          q.eq(q.field("guestName"), args.guestName)
        );
      } else if (args.guestPhone) {
        appointmentsQuery = appointmentsQuery.filter((q: any) =>
          q.eq(q.field("guestPhone"), args.guestPhone)
        );
      } else if (args.propertyId) {
        appointmentsQuery = appointmentsQuery.filter((q: any) =>
          q.eq(q.field("propertyId"), args.propertyId)
        );
      }

      if (args.fecha) {
        const startOfDay = new Date(args.fecha);
        startOfDay.setHours(0, 0, 0, 0);
        const endOfDay = new Date(args.fecha);
        endOfDay.setHours(23, 59, 59, 999);

        appointmentsQuery = appointmentsQuery.filter((q: any) =>
          q.and(
            q.gte(q.field("startTime"), startOfDay.toISOString()),
            q.lte(q.field("startTime"), endOfDay.toISOString())
          )
        );
      }

      appointments = await appointmentsQuery.order("desc").take(10);

      // Buscar en solicitudes pendientes (appointmentRequests)
      let requestsQuery = ctx.db.query("appointmentRequests");

      if (args.guestEmail) {
        requestsQuery = requestsQuery.filter((q: any) =>
          q.eq(q.field("guestEmail"), args.guestEmail)
        );
      } else if (args.guestName) {
        requestsQuery = requestsQuery.filter((q: any) =>
          q.eq(q.field("guestName"), args.guestName)
        );
      } else if (args.guestPhone) {
        requestsQuery = requestsQuery.filter((q: any) =>
          q.eq(q.field("guestPhone"), args.guestPhone)
        );
      } else if (args.propertyId) {
        requestsQuery = requestsQuery.filter((q: any) =>
          q.eq(q.field("propertyId"), args.propertyId)
        );
      }

      requests = await requestsQuery.order("desc").take(10);

      // Si se busca por chatId, buscar en conversaciones para obtener email/nombre
      if (args.chatId && appointments.length === 0 && requests.length === 0) {
        const conversations = await ctx.db
          .query("conversations")
          .filter((q: any) => q.eq(q.field("chatId"), args.chatId))
          .filter((q: any) => q.eq(q.field("messageType"), "user"))
          .order("desc")
          .take(5);

        // Extraer información del usuario de las conversaciones
        const userInfo = conversations.length > 0 ? {
          userName: conversations[0].userName,
          chatId: args.chatId
        } : null;

        if (userInfo) {
          // Buscar citas por nombre de usuario
          appointments = await ctx.db
            .query("appointments")
            .filter((q: any) => q.eq(q.field("guestName"), userInfo.userName))
            .order("desc")
            .take(10);

          requests = await ctx.db
            .query("appointmentRequests")
            .filter((q: any) => q.eq(q.field("guestName"), userInfo.userName))
            .order("desc")
            .take(10);
        }
      }

      // Enriquecer con datos de propiedades
      const enrichedAppointments = await Promise.all(
        appointments.map(async (appointment) => {
          let property = null;
          if (appointment.propertyId) {
            property = await ctx.db.get(appointment.propertyId);
          }
          return {
            ...appointment,
            property: property ? {
              title: property.title,
              address: property.address,
              city: property.location?.level2?.name || ''
            } : null
          };
        })
      );

      const enrichedRequests = await Promise.all(
        requests.map(async (request) => {
          let property = null;
          if (request.propertyId) {
            property = await ctx.db.get(request.propertyId);
          }
          return {
            ...request,
            property: property ? {
              title: property.title,
              address: property.address,
              city: property.location?.level2?.name || ''
            } : null
          };
        })
      );

      const totalFound = enrichedAppointments.length + enrichedRequests.length;

      return {
        success: true,
        message: totalFound > 0
          ? `Encontré ${totalFound} cita(s) relacionada(s).`
          : "No encontré citas con esos criterios de búsqueda.",
        appointments: enrichedAppointments,
        requests: enrichedRequests,
        searchCriteria: args
      };

    } catch (error) {
      console.error("Error consultando citas:", error);
      return {
        success: false,
        message: "Error interno al consultar las citas. Intente nuevamente.",
        appointments: [],
        requests: []
      };
    }
  },
});