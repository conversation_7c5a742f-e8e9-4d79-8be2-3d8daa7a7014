/**
 * Script para inicializar el sistema RAG completo
 * Ejecutar con: node scripts/initialize-rag.js
 */

const https = require('https');

// Configuración
const QDRANT_URL = 'https://apps-qdrant.yo7wfj.easypanel.host';
const QDRANT_API_KEY = '2e02390117857f6ad7810d2064c45716f219bf7bd68ff1a0ad4210c5278ea14d';
const COLLECTION_NAME = 'properties';

async function initializeRAG() {
  console.log('🚀 Inicializando Sistema RAG...');
  console.log('================================');

  try {
    // 1. Verificar conexión
    console.log('1️⃣ Verificando conexión con Qdrant...');
    await makeRequest('/');
    console.log('   ✅ Conexión exitosa');

    // 2. Verificar si la colección existe
    console.log('2️⃣ Verificando colección de propiedades...');
    try {
      const collectionInfo = await makeRequest(`/collections/${COLLECTION_NAME}`);
      console.log('   ✅ Colección "properties" ya existe');
      console.log(`   📊 Vectores: ${collectionInfo.result.vectors_count || 0}`);
      console.log(`   📊 Puntos: ${collectionInfo.result.points_count || 0}`);
    } catch (error) {
      if (error.message.includes('404')) {
        console.log('   📝 Colección no existe, creando...');
        await createPropertiesCollection();
        console.log('   ✅ Colección "properties" creada exitosamente');
      } else {
        throw error;
      }
    }

    // 3. Verificar configuración de la colección
    console.log('3️⃣ Verificando configuración de la colección...');
    const collectionInfo = await makeRequest(`/collections/${COLLECTION_NAME}`);
    const config = collectionInfo.result.config;
    
    console.log(`   📐 Tamaño de vectores: ${config.params.vectors.size}`);
    console.log(`   📏 Distancia: ${config.params.vectors.distance}`);
    console.log('   ✅ Configuración correcta');

    // 4. Probar inserción de vector de prueba
    console.log('4️⃣ Probando inserción de vector...');
    const testVector = {
      points: [{
        id: Date.now(),
        vector: Array(1536).fill(0).map(() => Math.random() - 0.5),
        payload: {
          test: true,
          title: 'Propiedad de prueba',
          type: 'test'
        }
      }]
    };

    await makeRequest(`/collections/${COLLECTION_NAME}/points`, 'PUT', testVector);
    console.log('   ✅ Vector de prueba insertado');

    // 5. Probar búsqueda
    console.log('5️⃣ Probando búsqueda vectorial...');
    const searchQuery = {
      vector: Array(1536).fill(0).map(() => Math.random() - 0.5),
      limit: 1,
      with_payload: true
    };

    const searchResult = await makeRequest(`/collections/${COLLECTION_NAME}/points/search`, 'POST', searchQuery);
    console.log(`   ✅ Búsqueda exitosa, ${searchResult.result.length} resultados`);

    // 6. Limpiar vector de prueba
    console.log('6️⃣ Limpiando datos de prueba...');
    const deleteQuery = {
      filter: {
        must: [{
          key: 'test',
          match: { value: true }
        }]
      }
    };

    await makeRequest(`/collections/${COLLECTION_NAME}/points/delete`, 'POST', deleteQuery);
    console.log('   ✅ Datos de prueba eliminados');

    console.log('');
    console.log('🎉 ¡Sistema RAG inicializado exitosamente!');
    console.log('✅ Qdrant está configurado y funcionando');
    console.log('✅ Colección "properties" está lista');
    console.log('✅ Inserción y búsqueda funcionan correctamente');
    console.log('');
    console.log('📋 Próximos pasos:');
    console.log('1. Ejecutar indexación de propiedades existentes');
    console.log('2. Probar búsquedas semánticas desde el frontend');
    console.log('3. Validar que las nuevas propiedades se indexen automáticamente');

  } catch (error) {
    console.error('❌ Error durante la inicialización:', error.message);
    console.log('');
    console.log('🔧 Verifica:');
    console.log('1. Que Qdrant esté ejecutándose en EasyPanel');
    console.log('2. Que las variables de entorno estén configuradas');
    console.log('3. Que la API key sea correcta');
  }
}

async function createPropertiesCollection() {
  const collectionConfig = {
    vectors: {
      size: 1536,
      distance: "Cosine"
    },
    optimizers_config: {
      default_segment_number: 2
    },
    replication_factor: 1
  };

  await makeRequest(`/collections/${COLLECTION_NAME}`, 'PUT', collectionConfig);
}

function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(QDRANT_URL + path);
    
    const options = {
      hostname: url.hostname,
      port: url.port || 443,
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'api-key': QDRANT_API_KEY
      }
    };

    const req = https.request(options, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(parsed);
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${parsed.status?.error || responseData}`));
          }
        } catch (e) {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve({ success: true });
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${responseData}`));
          }
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// Ejecutar inicialización
initializeRAG();
