// Configuración centralizada de modelos de AI
export const AI_MODELS = {
  // Modelos para búsqueda semántica
  EMBEDDINGS: {
    model: "text-embedding-3-small",
    provider: "openai"
  },

  // Modelos para extracción de criterios
  CRITERIA_EXTRACTION: {
    model: "gpt-4o",  // Cambiado de gpt-4o-mini a gpt-4o para mejor precisión
    provider: "openai",
    temperature: 0.1,
    maxTokens: 500
  },

  // Modelos para el agente principal
  MAIN_AGENT: {
    model: "gpt-4o",
    provider: "openai",
    temperature: 0.7,
    maxTokens: 2000
  },

  // Modelos para verificación de respuestas
  RESPONSE_FORMATTER: {
    model: "gpt-4o-mini",
    provider: "openai",
    temperature: 0.1,
    maxTokens: 1000
  }
} as const;

// Función para obtener configuración específica de embeddings
export const getEmbeddingConfig = () => {
  const envOverride = process.env.AI_MODEL_EMBEDDINGS;
  return {
    ...AI_MODELS.EMBEDDINGS,
    model: envOverride || AI_MODELS.EMBEDDINGS.model
  };
};

// Función para obtener configuración de extracción de criterios
export const getCriteriaExtractionConfig = () => {
  const envOverride = process.env.AI_MODEL_CRITERIA_EXTRACTION;
  return {
    ...AI_MODELS.CRITERIA_EXTRACTION,
    model: envOverride || AI_MODELS.CRITERIA_EXTRACTION.model
  };
};

// Función para obtener configuración del agente principal
export const getMainAgentConfig = () => {
  const envOverride = process.env.AI_MODEL_MAIN_AGENT;
  return {
    ...AI_MODELS.MAIN_AGENT,
    model: envOverride || AI_MODELS.MAIN_AGENT.model
  };
};

// Función para obtener configuración del formateador de respuestas
export const getResponseFormatterConfig = () => {
  const envOverride = process.env.AI_MODEL_RESPONSE_FORMATTER;
  return {
    ...AI_MODELS.RESPONSE_FORMATTER,
    model: envOverride || AI_MODELS.RESPONSE_FORMATTER.model
  };
};
