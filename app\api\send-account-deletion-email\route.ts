import { NextRequest, NextResponse } from 'next/server';
import { api } from '@/convex/_generated/api';
import { fetchAction } from 'convex/nextjs';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userEmail, userName, deletionDate, totalDeleted, deletedCount } = body;

    // Validar datos requeridos
    if (!userEmail || !userName || !deletionDate) {
      return NextResponse.json(
        { error: 'Faltan datos requeridos' },
        { status: 400 }
      );
    }

    // Enviar email usando la action de Convex
    const result = await fetchAction(api.emails.notifyAccountDeleted, {
      userEmail,
      userName,
      deletionDate,
      totalDeleted: totalDeleted || 0,
      deletedCount: deletedCount || {
        properties: 0,
        favorites: 0,
        messages: 0,
        appointments: 0,
        transactions: 0,
      },
    });

    if (result.success) {
      return NextResponse.json({ 
        success: true, 
        message: 'Email de confirmación enviado exitosamente' 
      });
    } else {
      console.error('Error enviando email:', result.error);
      return NextResponse.json(
        { error: 'Error enviando email de confirmación' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error en API de eliminación de cuenta:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
