"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useLocationCascade } from "@/hooks/useLocationCascade";
import { MapPin, X } from 'lucide-react';
import { Button } from "@/components/ui/button";

interface LocationFilterProps {
  value?: {
    country?: string;
    level1?: string;
    level2?: string;
    level3?: string;
    level4?: string;
  };
  onChange: (filters: {
    country?: string;
    level1?: string;
    level2?: string;
    level3?: string;
    level4?: string;
  }) => void;
  variant?: "compact" | "full";
  className?: string;
}

export function LocationFilter({
  value = {},
  onChange,
  variant = "full",
  className = ""
}: LocationFilterProps) {
  // Estado para el país seleccionado
  const [selectedCountry, setSelectedCountry] = useState(value.country || "GT");
  
  // Obtener configuración del país
  const countryConfig = useQuery(api.locationConfig.getCountryConfig, {
    countryCode: selectedCountry
  });

  // Obtener todas las configuraciones disponibles
  const allConfigs = useQuery(api.locationConfig.getAllActiveConfigs);

  // Convertir valores string a IDs para el hook de cascada
  const [initialCascadeValues, setInitialCascadeValues] = useState<{
    level1?: Id<"locationData">;
    level2?: Id<"locationData">;
    level3?: Id<"locationData">;
    level4?: Id<"locationData">;
  }>({});

  // Hook para manejar la cascada de ubicaciones
  const {
    selectedLevels,
    level1Options,
    level2Options,
    level3Options,
    level4Options,
    updateLevel,
    isLoading: cascadeLoading,
    clearAll,
    getSelectedNames
  } = useLocationCascade(selectedCountry, initialCascadeValues);

  // Efecto para convertir nombres a IDs cuando se inicializa
  useEffect(() => {
    if (!value.level1 || !level1Options || level1Options.length === 0) return;

    const level1Match = level1Options.find(item => item.name === value.level1);
    if (level1Match && !selectedLevels.level1) {
      updateLevel(1, level1Match._id);
    }
  }, [value.level1, level1Options, selectedLevels.level1, updateLevel]);

  // Efecto para notificar cambios al componente padre
  useEffect(() => {
    const selectedNames = getSelectedNames();
    const newFilters = {
      country: selectedCountry,
      level1: selectedNames.level1 || undefined,
      level2: selectedNames.level2 || undefined,
      level3: selectedNames.level3 || undefined,
      level4: selectedNames.level4 || undefined,
    };

    // Solo notificar si hay cambios reales
    const hasChanges = Object.keys(newFilters).some(key => 
      newFilters[key as keyof typeof newFilters] !== value[key as keyof typeof value]
    );

    if (hasChanges) {
      onChange(newFilters);
    }
  }, [selectedCountry, selectedLevels, getSelectedNames, onChange, value]);

  const handleCountryChange = useCallback((countryCode: string) => {
    setSelectedCountry(countryCode);
    clearAll();
  }, [clearAll]);

  const handleClearAll = useCallback(() => {
    clearAll();
    onChange({
      country: selectedCountry,
      level1: undefined,
      level2: undefined,
      level3: undefined,
      level4: undefined,
    });
  }, [clearAll, onChange, selectedCountry]);

  // Renderizar dropdown de ubicación
  const renderLocationDropdown = (
    level: number,
    options: any[] | undefined,
    parentId?: Id<"locationData">,
    placeholder?: string
  ) => {
    const currentValue = selectedLevels[`level${level}` as keyof typeof selectedLevels];
    const levelName = countryConfig?.hierarchy[`level${level}` as keyof typeof countryConfig.hierarchy]?.name;

    if (!levelName) return null;

    return (
      <div key={`level${level}`}>
        <Label className="text-sm font-medium text-gray-700 mb-1 block">
          {levelName}
        </Label>
        <Select
          value={currentValue || "__all__"}
          onValueChange={(value) => updateLevel(level, value === "__all__" ? null : value as Id<"locationData">)}
          disabled={!options || options.length === 0}
        >
          <SelectTrigger className="h-10">
            <SelectValue placeholder={placeholder || `Seleccionar ${levelName.toLowerCase()}`} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="__all__">Todos los {levelName.toLowerCase()}s</SelectItem>
            {options?.map((option: any) => (
              <SelectItem key={option._id} value={option._id}>
                {option.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    );
  };

  if (!countryConfig) {
    return (
      <div className={`space-y-3 ${className}`}>
        <div className="flex items-center justify-between">
          <Label className="text-sm font-medium text-gray-900 flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            Ubicación
          </Label>
        </div>
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
          <div className="flex items-center gap-2 text-yellow-700">
            <MapPin className="h-4 w-4" />
            <span className="text-sm">
              Configuración de ubicación no disponible para {selectedCountry}.
              <br />
              <span className="text-xs">Contacta al administrador para configurar las ubicaciones.</span>
            </span>
          </div>
        </div>
      </div>
    );
  }

  if (cascadeLoading) {
    return (
      <div className={`flex items-center gap-2 text-gray-500 ${className}`}>
        <MapPin className="h-4 w-4" />
        <span className="text-sm">Cargando ubicaciones...</span>
      </div>
    );
  }

  const hasActiveFilters = Object.values(selectedLevels).some(level => level !== undefined);



  if (variant === "compact") {
    return (
      <div className={`space-y-3 ${className}`}>
        <div className="flex items-center justify-between">
          <Label className="text-sm font-medium text-gray-900 flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            Ubicación
          </Label>
          {hasActiveFilters && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClearAll}
              className="h-6 px-2 text-xs text-gray-500 hover:text-red-600"
            >
              <X className="h-3 w-3 mr-1" />
              Limpiar
            </Button>
          )}
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {/* Nivel 1 - siempre visible */}
          {renderLocationDropdown(1, level1Options, undefined, "Departamento")}
          
          {/* Nivel 2 - solo si hay selección en nivel 1 */}
          {selectedLevels.level1 && renderLocationDropdown(2, level2Options, selectedLevels.level1, "Municipio")}
        </div>
        
        {/* Nivel 3 - solo si hay selección en nivel 2 */}
        {selectedLevels.level2 && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {renderLocationDropdown(3, level3Options, selectedLevels.level2, "Zona")}
            
            {/* Nivel 4 - solo si hay selección en nivel 3 */}
            {selectedLevels.level3 && renderLocationDropdown(4, level4Options, selectedLevels.level3, "Colonia")}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <Label className="text-sm font-medium text-gray-900 flex items-center gap-2">
          <MapPin className="h-4 w-4" />
          Filtros de Ubicación
        </Label>
        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClearAll}
            className="text-gray-500 hover:text-red-600"
          >
            <X className="h-4 w-4 mr-1" />
            Limpiar ubicación
          </Button>
        )}
      </div>

      {/* Selector de País */}
      <div>
        <Label className="text-sm font-medium text-gray-700 mb-1 block">
          País
        </Label>
        <Select value={selectedCountry} onValueChange={handleCountryChange}>
          <SelectTrigger>
            <SelectValue placeholder="Seleccionar país" />
          </SelectTrigger>
          <SelectContent>
            {allConfigs?.map((config: any) => (
              <SelectItem key={config.countryCode} value={config.countryCode}>
                {config.countryName}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Dropdowns jerárquicos */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Nivel 1 - siempre visible */}
        {renderLocationDropdown(1, level1Options)}
        
        {/* Nivel 2 - solo si hay selección en nivel 1 */}
        {selectedLevels.level1 && renderLocationDropdown(2, level2Options, selectedLevels.level1)}
        
        {/* Nivel 3 - solo si hay selección en nivel 2 */}
        {selectedLevels.level2 && renderLocationDropdown(3, level3Options, selectedLevels.level2)}
        
        {/* Nivel 4 - solo si hay selección en nivel 3 */}
        {selectedLevels.level3 && renderLocationDropdown(4, level4Options, selectedLevels.level3)}
      </div>
    </div>
  );
}
