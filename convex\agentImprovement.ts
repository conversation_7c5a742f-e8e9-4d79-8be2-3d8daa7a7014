import { mutation, query, action, internalAction, internalMutation, internalQuery } from "./_generated/server";
import { v } from "convex/values";
import { internal } from "./_generated/api";

// 🤖 SISTEMA DE AUTO-MEJORA BÁSICO - Usando tablas existentes

// Analizar calidad de una conversación específica
export const analyzeConversationQuality = internalAction({
  args: {
    chatId: v.string(),
    userMessage: v.string(),
    agentResponse: v.string(),
    messageId: v.string(),
  },
  handler: async (ctx, args) => {
    console.log("🔍 Analizando calidad de conversación:", args.chatId);

    // Análisis básico de problemas comunes
    const problems = [];
    const suggestions = [];
    let qualityScore = 10;

    // 1. Verificar saludo amable (solo en primeros mensajes)
    const hasGreeting = checkGreeting(args.agentResponse);
    if (!hasGreeting && isFirstMessage(args.userMessage)) {
      problems.push("no_greeting");
      suggestions.push("Agregar saludo amable al inicio");
      qualityScore -= 2;
    }

    // 2. Verificar pregunta venta vs alquiler
    const asksSaleOrRent = checkSaleRentQuestion(args.agentResponse);
    if (!asksSaleOrRent && isPropertyInquiry(args.userMessage)) {
      problems.push("no_sale_rent_question");
      suggestions.push("Preguntar si busca comprar o alquilar");
      qualityScore -= 2;
    }

    // 3. Verificar formato correcto de propiedades
    const usesCorrectFormat = checkPropertyFormat(args.agentResponse);
    if (!usesCorrectFormat && hasPropertyListing(args.agentResponse)) {
      problems.push("incorrect_property_format");
      suggestions.push("Usar formato: 🏠 **[Título]** (ID: [id]) | 📍 [Ubicación] | 💰 $[precio]");
      qualityScore -= 1;
    }

    // 4. Verificar manejo de citas
    const handlesAppointments = checkAppointmentHandling(args.agentResponse, args.userMessage);
    if (!handlesAppointments && showsInterestInViewing(args.userMessage)) {
      problems.push("poor_appointment_handling");
      suggestions.push("Ofrecer múltiples días y horarios para citas");
      qualityScore -= 2;
    }

    // Guardar en tabla conversations con análisis
    await ctx.runMutation(internal.agentImprovement.saveAnalysisToConversations, {
      chatId: args.chatId,
      messageId: args.messageId,
      userMessage: args.userMessage,
      agentResponse: args.agentResponse,
      qualityScore,
      problems,
      suggestions,
    });

    return {
      qualityScore,
      problems,
      suggestions,
      analyzed: true
    };
  },
});

// Guardar análisis en tabla conversations existente
export const saveAnalysisToConversations = internalMutation({
  args: {
    chatId: v.string(),
    messageId: v.string(),
    userMessage: v.string(),
    agentResponse: v.string(),
    qualityScore: v.number(),
    problems: v.array(v.string()),
    suggestions: v.array(v.string()),
  },
  handler: async (ctx, args) => {
    // Guardar mensaje del usuario
    await ctx.db.insert("conversations", {
      chatId: args.chatId,
      userName: "Usuario",
      messageId: args.messageId + "_user",
      messageType: "user",
      content: args.userMessage,
      timestamp: new Date().toISOString(),
      processed: true,
      createdAt: new Date().toISOString(),
    });

    // Guardar respuesta del agente con análisis
    await ctx.db.insert("conversations", {
      chatId: args.chatId,
      userName: "INMO",
      messageId: args.messageId + "_agent",
      messageType: "assistant",
      content: args.agentResponse,
      timestamp: new Date().toISOString(),

      // Campos de análisis (usando campos opcionales existentes)
      interestLevel: args.qualityScore,
      intent: args.problems.join(","),

      processed: true,
      createdAt: new Date().toISOString(),
    });
  },
});

// Construir system message dinámico básico
export const buildDynamicSystemMessage = internalQuery({
  args: {},
  handler: async (ctx): Promise<{
    systemMessage: string;
    rulesCount: number;
    lastUpdated: number;
  }> => {
    // Obtener conversaciones recientes para análisis
    const recentConversations = await ctx.db
      .query("conversations")
      .order("desc")
      .take(50);

    // Analizar problemas frecuentes
    const problemCounts: Record<string, number> = {};

    recentConversations.forEach(conv => {
      if (conv.intent && conv.messageType === "assistant") {
        const problems = conv.intent.split(",").filter(p => p.trim());
        problems.forEach(problem => {
          problemCounts[problem] = (problemCounts[problem] || 0) + 1;
        });
      }
    });

    // Obtener system message activo desde la base de datos
    const activeSystemMessage: any = await ctx.runQuery(internal.systemMessages.getActiveSystemMessageInternal, {});
    let systemMessage: string = activeSystemMessage.content;

    // Agregar reglas auto-generadas para problemas frecuentes
    const frequentProblems = Object.entries(problemCounts)
      .filter(([_, count]) => count >= 3) // Problemas que aparecen 3+ veces
      .sort(([_, a], [__, b]) => b - a)
      .slice(0, 5); // Top 5 problemas

    if (frequentProblems.length > 0) {
      systemMessage += "\n\n### 🤖 REGLAS AUTO-GENERADAS (Problemas detectados frecuentemente):\n\n";

      frequentProblems.forEach(([problem, count], index) => {
        const rule = getRuleForProblem(problem);
        systemMessage += `**${index + 1}. ${rule.description}** (detectado ${count} veces)\n`;
        systemMessage += `- SOLUCIÓN: ${rule.solution}\n\n`;
      });
    }

    return {
      systemMessage,
      rulesCount: frequentProblems.length,
      lastUpdated: Date.now(),
    };
  },
});

// Obtener estadísticas del sistema de mejora
export const getImprovementStats = query({
  args: {},
  handler: async (ctx) => {
    const conversations = await ctx.db
      .query("conversations")
      .filter((q: any) => q.eq(q.field("messageType"), "assistant"))
      .collect();

    const totalAnalyses = conversations.filter(c => c.interestLevel !== undefined).length;
    const avgQuality = totalAnalyses > 0
      ? conversations
          .filter(c => c.interestLevel !== undefined)
          .reduce((sum, c) => sum + (c.interestLevel || 0), 0) / totalAnalyses
      : 0;

    const problemFrequency: Record<string, number> = {};
    conversations.forEach(conv => {
      if (conv.intent) {
        const problems = conv.intent.split(",").filter(p => p.trim());
        problems.forEach(problem => {
          problemFrequency[problem] = (problemFrequency[problem] || 0) + 1;
        });
      }
    });

    return {
      totalAnalyses,
      averageQuality: Math.round(avgQuality * 100) / 100,
      problemFrequency,
      lastAnalysis: conversations.length > 0 ? conversations[0].createdAt : null,
    };
  },
});

// === FUNCIONES AUXILIARES ===

function checkGreeting(response: string): boolean {
  const greetings = ["buenos días", "buenas tardes", "hola", "es un placer", "bienvenido"];
  return greetings.some(greeting => response.toLowerCase().includes(greeting));
}

function isFirstMessage(userMessage: string): boolean {
  // Detectar si es el primer mensaje del usuario
  const firstMessageIndicators = ["hola", "buenos días", "buenas tardes", "necesito", "busco"];
  return firstMessageIndicators.some(indicator => userMessage.toLowerCase().includes(indicator));
}

function checkSaleRentQuestion(response: string): boolean {
  const saleRentQuestions = ["comprar o alquilar", "venta o alquiler", "para compra o renta"];
  return saleRentQuestions.some(question => response.toLowerCase().includes(question));
}

function isPropertyInquiry(userMessage: string): boolean {
  const propertyKeywords = ["casa", "apartamento", "propiedad", "inmueble", "busco"];
  return propertyKeywords.some(keyword => userMessage.toLowerCase().includes(keyword));
}

function checkPropertyFormat(response: string): boolean {
  // Verificar si usa el formato correcto: 🏠 **[Título]** (ID: [id])
  return response.includes("🏠") && response.includes("**") && response.includes("(ID:");
}

function hasPropertyListing(response: string): boolean {
  return response.includes("🏠") || response.includes("propiedad") || response.includes("$");
}

function checkAppointmentHandling(response: string, userMessage: string): boolean {
  if (!showsInterestInViewing(userMessage)) return true;
  
  // Verificar si ofrece múltiples días
  const multipleDays = (response.match(/📅/g) || []).length >= 2;
  return multipleDays;
}

function showsInterestInViewing(userMessage: string): boolean {
  const viewingKeywords = ["visita", "ver", "conocer", "cita", "agendar"];
  return viewingKeywords.some(keyword => userMessage.toLowerCase().includes(keyword));
}

function getRuleForProblem(problem: string) {
  const rules: Record<string, { description: string; solution: string }> = {
    no_greeting: {
      description: "No saluda amablemente al inicio",
      solution: "SIEMPRE iniciar con: '¡Buenos días! Soy INMO de inmova.gt. Es un placer atenderle.'"
    },
    no_sale_rent_question: {
      description: "No pregunta si busca comprar o alquilar",
      solution: "Preguntar inmediatamente: '¿Está buscando una propiedad para comprar o alquilar?'"
    },
    incorrect_property_format: {
      description: "No usa formato correcto para mostrar propiedades",
      solution: "Usar formato: 🏠 **[Título]** (ID: [id]) | 📍 [Ubicación] | 💰 $[precio] | 🛏️ [detalles]"
    },
    poor_appointment_handling: {
      description: "No maneja bien las solicitudes de citas",
      solution: "SIEMPRE mostrar múltiples días con horarios disponibles (mínimo 2-3 días)"
    }
  };

  return rules[problem] || {
    description: "Problema no identificado",
    solution: "Revisar manualmente"
  };
}

// Función eliminada - ahora se usa la tabla systemMessages
