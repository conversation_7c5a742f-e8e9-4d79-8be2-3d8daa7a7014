"use client";

import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo, useRef } from 'react';
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { MapPin, Info, AlertTriangle } from 'lucide-react';
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useLocationCascade, convertSelectionsToLocation } from "@/hooks/useLocationCascade";
import { LocationDropdown, SmartLocationInput } from "./LocationDropdown";
import { Id } from "@/convex/_generated/dataModel";

interface LocationData {
  country: {
    code: string;
    name: string;
  };
  level1?: {
    code?: string;
    name: string;
    type: string;
  };
  level2?: {
    code?: string;
    name: string;
    type: string;
  };
  level3?: {
    code?: string;
    name: string;
    type: string;
  };
  level4?: {
    code?: string;
    name: string;
    type: string;
  };
  level5?: {
    code?: string;
    name: string;
    type: string;
  };
  level6?: {
    code?: string;
    name: string;
    type: string;
  };
}

interface LocationSelectorProps {
  value?: LocationData;
  onChange: (location: LocationData) => void;
  errors?: { [key: string]: string };
  className?: string;
}

export function LocationSelector({ value, onChange, errors, className }: LocationSelectorProps) {
  // Obtener preferencias del usuario
  const userPreferences = useQuery(api.users.getUserLocationPreferences);
  const [selectedCountry, setSelectedCountry] = useState(() => {
    // Usar valor inicial estable para evitar cambios durante el render
    return value?.country?.code || "GT";
  });
  
  // Obtener configuración del país seleccionado
  const countryConfig = useQuery(api.locationConfig.getCountryConfig, { 
    countryCode: selectedCountry 
  });
  
  // Obtener todas las configuraciones disponibles
  const allConfigs = useQuery(api.locationConfig.getAllActiveConfigs);

  // Estado para los valores iniciales del hook de cascada
  const [initialCascadeValues, setInitialCascadeValues] = useState<{
    level1?: Id<"locationData">;
    level2?: Id<"locationData">;
    level3?: Id<"locationData">;
    level4?: Id<"locationData">;
  }>({});

  // Hook para manejar la cascada de ubicaciones (nuevo sistema para Guatemala)
  const {
    selectedLevels,
    level1Options,
    level2Options,
    level3Options,
    level4Options,
    updateLevel,
    isLoading: cascadeLoading,
    clearAll
  } = useLocationCascade(selectedCountry, initialCascadeValues);

  // Estado local para cada nivel (sistema legacy para entrada libre)
  const [levels, setLevels] = useState(() => ({
    level1: value?.level1?.name || "",
    level2: value?.level2?.name || "",
    level3: value?.level3?.name || "",
    level4: value?.level4?.name || "",
    level5: value?.level5?.name || "",
    level6: value?.level6?.name || "",
  }));

  // Efecto para sincronizar valores de texto libre cuando cambie el value prop
  useEffect(() => {
    if (value && selectedCountry === "GT") {
      setLevels(prev => ({
        ...prev,
        level4: value.level4?.name || "",
        level5: value.level5?.name || "",
        level6: value.level6?.name || "",
      }));
    }
  }, [value, selectedCountry]);

  // Estado para mostrar alerta de cambio de país
  const [showCountryChangeAlert, setShowCountryChangeAlert] = useState(false);
  const [hasFilledData, setHasFilledData] = useState(false);

  // Efecto para sincronizar preferencias del usuario (solo una vez)
  useEffect(() => {
    if (userPreferences?.defaultCountry && !value?.country?.code && selectedCountry === "GT") {
      setSelectedCountry(userPreferences.defaultCountry);
    }
  }, [userPreferences?.defaultCountry, value?.country?.code, selectedCountry]);

  // Estado para controlar la inicialización - simplificado
  const [isInitialized, setIsInitialized] = useState(false);

  // Efecto para sincronizar el país seleccionado cuando cambie el value
  useEffect(() => {
    if (value?.country?.code && value.country.code !== selectedCountry) {
      setSelectedCountry(value.country.code);
      setIsInitialized(false);
    }
  }, [value?.country?.code, selectedCountry]);

  // Efecto para inicializar level1 cuando se carga el componente
  useEffect(() => {
    if (!value || selectedCountry !== "GT") return;
    if (!level1Options || level1Options.length === 0) return;
    if (selectedLevels.level1) return; // Ya inicializado

    if (value.level1?.name) {
      const level1Match = level1Options.find(item => item.name === value.level1?.name);
      if (level1Match) {
        updateLevel(1, level1Match._id);
      }
    }
  }, [value, selectedCountry, level1Options, selectedLevels.level1, updateLevel]);

  // Efecto para inicializar level2 cuando level1 está listo
  useEffect(() => {
    if (!value || selectedCountry !== "GT") return;
    if (!selectedLevels.level1 || !level2Options || level2Options.length === 0) return;
    if (selectedLevels.level2) return; // Ya inicializado

    if (value.level2?.name) {
      const level2Match = level2Options.find(item => item.name === value.level2?.name);
      if (level2Match) {
        updateLevel(2, level2Match._id);
      }
    }
  }, [value, selectedCountry, selectedLevels.level1, level2Options, selectedLevels.level2, updateLevel]);

  // Efecto para inicializar level3 cuando level2 está listo
  useEffect(() => {
    if (!value || selectedCountry !== "GT") return;
    if (!selectedLevels.level2 || !level3Options || level3Options.length === 0) return;
    if (selectedLevels.level3) return; // Ya inicializado

    if (value.level3?.name) {
      const level3Match = level3Options.find(item => item.name === value.level3?.name);
      if (level3Match) {
        updateLevel(3, level3Match._id);
      }
    }
  }, [value, selectedCountry, selectedLevels.level2, level3Options, selectedLevels.level3, updateLevel]);

  // Función para construir la ubicación - memoizada para evitar re-renders
  const buildLocation = useMemo(() => {
    if (!countryConfig) return null;

    // Para Guatemala, usar el nuevo sistema de cascada
    if (selectedCountry === "GT") {
      const location = convertSelectionsToLocation(
        selectedLevels,
        selectedCountry,
        countryConfig.countryName,
        {
          level1Options,
          level2Options,
          level3Options,
          level4Options
        }
      );

      // Agregar niveles de texto libre (level4, level5, level6)
      // Para level4 (colonia), solo agregar si level3 (zona) está seleccionado
      if (levels.level4 && levels.level4.trim() && selectedLevels.level3) {
        location.level4 = {
          name: levels.level4.trim(),
          type: "colonia"
        };
      }

      if (levels.level5 && levels.level5.trim()) {
        location.level5 = {
          name: levels.level5.trim(),
          type: countryConfig.hierarchy.level5?.name.toLowerCase() || "nivel5"
        };
      }
      if (levels.level6 && levels.level6.trim()) {
        location.level6 = {
          name: levels.level6.trim(),
          type: countryConfig.hierarchy.level6?.name.toLowerCase() || "nivel6"
        };
      }

      return location;
    }

    // Para otros países, usar el sistema legacy
    const newLocation: LocationData = {
      country: {
        code: selectedCountry,
        name: countryConfig.countryName,
      },
    };

    // Construir niveles dinámicamente
    Object.entries(levels).forEach(([levelKey, levelValue]) => {
      const levelNum = levelKey as keyof typeof levels;
      const hierarchyLevel = countryConfig.hierarchy[levelNum];

      if (levelValue && hierarchyLevel) {
        (newLocation as any)[levelNum] = {
          name: levelValue,
          type: hierarchyLevel.name.toLowerCase(),
        };
      }
    });

    return newLocation;
  }, [selectedCountry, levels, countryConfig, selectedLevels, level1Options, level2Options, level3Options, level4Options]);

  // Efecto para notificar cambios - solo cuando hay cambios reales
  const prevLocationRef = useRef<LocationData | null>(null);

  useEffect(() => {
    if (buildLocation && countryConfig) {
      // Solo notificar si la ubicación realmente cambió
      const locationString = JSON.stringify(buildLocation);
      const prevLocationString = JSON.stringify(prevLocationRef.current);

      if (locationString !== prevLocationString) {
        prevLocationRef.current = buildLocation;
        const timeoutId = setTimeout(() => {
          onChange(buildLocation);
        }, 100);
        return () => clearTimeout(timeoutId);
      }
    }
  }, [buildLocation, countryConfig, onChange]);

  const handleCountryChange = useCallback((countryCode: string) => {
    if (countryCode !== selectedCountry) {
      // Verificar si hay datos llenados
      const hasData = Object.values(levels).some(level => level.trim() !== "") ||
                     Object.values(selectedLevels).some(level => level !== undefined);

      if (hasData) {
        setShowCountryChangeAlert(true);
        setTimeout(() => setShowCountryChangeAlert(false), 5000);
      }

      // Limpiar todos los niveles cuando cambie el país
      setLevels({
        level1: "",
        level2: "",
        level3: "",
        level4: "",
        level5: "",
        level6: "",
      });

      // Limpiar también las selecciones de cascada
      clearAll();

      // Actualizar el país seleccionado
      setSelectedCountry(countryCode);
    }
  }, [levels, selectedLevels, selectedCountry, clearAll]);

  const handleLevelChange = useCallback((levelKey: keyof typeof levels, value: string) => {
    // Marcar que el usuario ha llenado datos
    if (value.trim() !== "") {
      setHasFilledData(true);
    }

    // Limpiar niveles inferiores cuando cambie un nivel superior
    const levelOrder = ['level1', 'level2', 'level3', 'level4', 'level5', 'level6'];
    const currentIndex = levelOrder.indexOf(levelKey);

    const newLevels = { ...levels };
    newLevels[levelKey] = value;

    // Limpiar niveles posteriores
    for (let i = currentIndex + 1; i < levelOrder.length; i++) {
      newLevels[levelOrder[i] as keyof typeof levels] = "";
    }

    setLevels(newLevels);
  }, [levels]);

  // Función para renderizar un nivel dinámicamente
  const renderLevel = (levelKey: keyof typeof levels, hierarchyLevel: any) => {
    if (!hierarchyLevel) return null;

    const levelNumber = parseInt(levelKey.replace('level', ''));

    // Para Guatemala, usar dropdowns para niveles 1, 2 y 3
    if (selectedCountry === "GT" && levelNumber <= 3) {
      const getParentId = () => {
        if (levelNumber === 1) return undefined;
        if (levelNumber === 2) return selectedLevels.level1;
        if (levelNumber === 3) return selectedLevels.level2;
        return undefined;
      };

      const getCurrentValue = () => {
        const currentValue = selectedLevels[`level${levelNumber}` as keyof typeof selectedLevels] || null;
        return currentValue;
      };

      // Para level3 (zona), verificar que level2 esté seleccionado
      const isDisabled = levelNumber === 3 && !selectedLevels.level2;

      return (
        <div key={levelKey}>
          <Label htmlFor={levelKey} className="text-sm font-medium text-gray-900 mb-2 block">
            {hierarchyLevel.name}
            {hierarchyLevel.required && " *"}
          </Label>
          <LocationDropdown
            level={levelNumber}
            countryCode={selectedCountry}
            parentId={getParentId()}
            value={getCurrentValue()}
            onChange={(value) => updateLevel(levelNumber, value)}
            placeholder={`Seleccionar ${hierarchyLevel.name.toLowerCase()}`}
            disabled={isDisabled}
          />
          {isDisabled && (
            <p className="text-sm text-yellow-600 mt-1">
              Primero selecciona un {countryConfig?.hierarchy.level2.name.toLowerCase()}
            </p>
          )}
          {errors?.[levelKey] && (
            <p className="text-sm text-red-600 mt-1">{errors[levelKey]}</p>
          )}
        </div>
      );
    }

    // Para nivel 4+ en Guatemala, usar input libre pero validar jerarquía
    if (selectedCountry === "GT" && levelNumber === 4) {
      // Para level4 (colonia), verificar que level3 esté seleccionado si es requerido
      const isDisabled = !selectedLevels.level3;

      return (
        <div key={levelKey}>
          <Label htmlFor={levelKey} className="text-sm font-medium text-gray-900 mb-2 block">
            {hierarchyLevel.name}
            {hierarchyLevel.required && " *"}
          </Label>
          <Input
            id={levelKey}
            placeholder={isDisabled ? "Primero selecciona una zona" : "Ej: Oakland"}
            value={levels[levelKey]}
            onChange={(e) => handleLevelChange(levelKey, e.target.value)}
            className="mt-1"
            disabled={isDisabled}
          />
          {isDisabled && (
            <p className="text-sm text-yellow-600 mt-1">
              Primero selecciona una {countryConfig?.hierarchy.level3?.name.toLowerCase()}
            </p>
          )}
          {errors?.[levelKey] && (
            <p className="text-sm text-red-600 mt-1">{errors[levelKey]}</p>
          )}
        </div>
      );
    }

    // Para otros países o niveles superiores, usar input libre
    const placeholder = selectedCountry === "GT"
      ? "Ejemplo"
      : "Ejemplo";

    return (
      <div key={levelKey}>
        <Label htmlFor={levelKey} className="text-sm font-medium text-gray-900 mb-2 block">
          {hierarchyLevel.name}
          {hierarchyLevel.required && " *"}
        </Label>
        <Input
          id={levelKey}
          placeholder={`Ej: ${placeholder}`}
          value={levels[levelKey]}
          onChange={(e) => handleLevelChange(levelKey, e.target.value)}
          className="mt-1"
        />
        {errors?.[levelKey] && (
          <p className="text-sm text-red-600 mt-1">{errors[levelKey]}</p>
        )}
      </div>
    );
  };

  if (!countryConfig) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="flex items-center gap-2 text-gray-500">
          <MapPin className="h-4 w-4" />
          <span>Cargando configuración de ubicación...</span>
        </div>
      </div>
    );
  }



  return (
    <div className={`space-y-4 ${className}`}>
      {/* Selector de País */}
      <div>
        <Label htmlFor="country" className="text-sm font-medium text-gray-900 mb-2 block">
          País *
        </Label>
        <Select value={selectedCountry} onValueChange={handleCountryChange}>
          <SelectTrigger className="mt-1">
            <SelectValue placeholder="Seleccionar país" />
          </SelectTrigger>
          <SelectContent>
            {allConfigs?.map((config: any) => (
              <SelectItem key={config.countryCode} value={config.countryCode}>
                {config.countryName}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {errors?.country && (
          <p className="text-sm text-red-600 mt-1">{errors.country}</p>
        )}
      </div>

      {/* Alerta de cambio de país */}
      {showCountryChangeAlert && (
        <Alert className="border-yellow-200 bg-yellow-50">
          <AlertTriangle className="h-4 w-4 text-yellow-600" />
          <AlertDescription className="text-yellow-800">
            <strong>País cambiado:</strong> Los campos de ubicación se han limpiado automáticamente
            para adaptarse a la nueva estructura geográfica.
          </AlertDescription>
        </Alert>
      )}

      {/* Renderizar niveles dinámicamente con lógica de cascada */}
      {/* Nivel 1 - siempre visible */}
      {renderLevel('level1', countryConfig.hierarchy.level1)}

      {/* Nivel 2 - solo si hay selección en nivel 1 */}
      {(selectedCountry !== "GT" || selectedLevels.level1) &&
        renderLevel('level2', countryConfig.hierarchy.level2)}

      {/* Nivel 3 - solo si hay selección en nivel 2 */}
      {(selectedCountry !== "GT" || selectedLevels.level2) &&
        renderLevel('level3', countryConfig.hierarchy.level3)}

      {/* Nivel 4 - solo si hay selección en nivel 3 */}
      {(selectedCountry !== "GT" || selectedLevels.level3) &&
        renderLevel('level4', countryConfig.hierarchy.level4)}

      {/* Nivel 5 - solo si hay selección en nivel 4 o contenido en nivel 4 */}
      {(selectedCountry !== "GT" || selectedLevels.level3 || levels.level4.trim()) &&
        renderLevel('level5', countryConfig.hierarchy.level5)}

      {/* Nivel 6 - solo si hay contenido en nivel 5 */}
      {(selectedCountry !== "GT" || levels.level5.trim()) &&
        renderLevel('level6', countryConfig.hierarchy.level6)}


    </div>
  );
}
