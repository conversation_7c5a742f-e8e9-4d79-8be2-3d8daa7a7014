// Script temporal para poblar datos básicos de Guatemala
// Ejecutar desde la consola del navegador en http://localhost:3000

async function populateGuatemalaData() {
  try {
    console.log("Iniciando población de datos de Guatemala...");
    
    // Llamar a la función de Convex
    const result = await window.convex.mutation("locationData:populateGuatemalaDataTemp", {});
    
    console.log("✅ Datos poblados exitosamente:", result);
    return result;
  } catch (error) {
    console.error("❌ Error al poblar datos:", error);
    
    // Si falla, intentar con la función principal
    try {
      console.log("Intentando con función principal...");
      const result = await window.convex.mutation("locationData:populateGuatemalaData", {});
      console.log("✅ Datos poblados exitosamente:", result);
      return result;
    } catch (error2) {
      console.error("❌ Error con función principal:", error2);
      throw error2;
    }
  }
}

async function checkGuatemalaData() {
  try {
    console.log("Verificando datos de Guatemala...");
    
    const result = await window.convex.query("locationData:checkGuatemalaData", {});
    
    console.log("📊 Estado de datos de Guatemala:", result);
    return result;
  } catch (error) {
    console.error("❌ Error al verificar datos:", error);
    throw error;
  }
}

// Función para verificar departamentos específicamente
async function checkDepartments() {
  try {
    console.log("Verificando departamentos...");
    
    const result = await window.convex.query("locationData:getRootLevels", {
      countryCode: "GT",
      level: 1
    });
    
    console.log("🏛️ Departamentos encontrados:", result);
    return result;
  } catch (error) {
    console.error("❌ Error al verificar departamentos:", error);
    throw error;
  }
}

// Exportar funciones para uso en consola
window.populateGuatemalaData = populateGuatemalaData;
window.checkGuatemalaData = checkGuatemalaData;
window.checkDepartments = checkDepartments;

console.log(`
🚀 Funciones disponibles:
- populateGuatemalaData() - Poblar datos de Guatemala
- checkGuatemalaData() - Verificar estado de datos
- checkDepartments() - Verificar departamentos específicamente

Ejemplo de uso:
await checkGuatemalaData()
await populateGuatemalaData()
await checkDepartments()
`);
