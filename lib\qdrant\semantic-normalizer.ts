/**
 * Normalizador Semántico Inteligente
 *
 * Usa OpenAI para normalizar consultas inmobiliarias con contexto regional dinámico
 * expandiendo abreviaciones y terminología local sin hardcodeo.
 */

import OpenAI from 'openai';
import { ConvexHttpClient } from 'convex/browser';

// Configuración de OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Resultado de normalización
export interface NormalizationResult {
  normalized: string;
  confidence: number;
  reasoning: string;
  hasChanges: boolean;
  processingTime: number;
}

// Contexto regional para normalización
interface CountryContext {
  contextInfo: string;
  patterns: string;
  terminology: string;
}

export class SemanticNormalizer {
  private readonly model: string;
  private readonly timeout: number;
  private convexClient: ConvexHttpClient;

  constructor() {
    this.model = process.env.AI_MODEL_NORMALIZATION || 'gpt-3.5-turbo';
    this.timeout = parseInt(process.env.AI_TIMEOUT_MS || '5000');
    this.convexClient = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);
  }

  /**
   * Normaliza una consulta usando inteligencia semántica
   */
  async normalize(query: string): Promise<NormalizationResult> {
    const startTime = Date.now();

    try {
      console.log(`🔧 Normalizando consulta: "${query}"`);

      // ENFOQUE PURAMENTE SEMÁNTICO: Siempre normalizar
      // OpenAI decide inteligentemente si cambiar o mantener la consulta

      // Generar prompt para normalización semántica con contexto regional
      const prompt = await this.buildNormalizationPrompt(query);

      // Llamar a OpenAI
      const response = await openai.chat.completions.create({
        model: this.model,
        messages: [
          {
            role: 'system',
            content: 'Eres un experto en búsquedas inmobiliarias. Tu trabajo es normalizar consultas usando comprensión semántica y contexto regional apropiado.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.1, // Muy determinístico
        max_tokens: 200
      });

      const result = this.parseNormalizationResponse(response.choices[0]?.message?.content || '', query);
      
      console.log(`✅ Normalización completada: "${query}" → "${result.normalized}" (confianza: ${result.confidence})`);
      
      return {
        ...result,
        processingTime: Date.now() - startTime
      };

    } catch (error) {
      console.error('❌ Error en normalización semántica:', error);
      
      // Fallback: devolver consulta original
      return {
        normalized: query,
        confidence: 0,
        reasoning: `Error en normalización: ${error instanceof Error ? error.message : 'Error desconocido'}`,
        hasChanges: false,
        processingTime: Date.now() - startTime
      };
    }
  }



  /**
   * Construye el prompt para normalización semántica con contexto regional dinámico
   */
  private async buildNormalizationPrompt(query: string): Promise<string> {
    const countryContext = await this.getCurrentCountryContext();

    return `
Eres un experto en búsquedas inmobiliarias. Normaliza esta consulta usando tu comprensión del lenguaje natural y el contexto regional apropiado.

Consulta original: "${query}"
Contexto regional: ${countryContext.contextInfo}

PRINCIPIOS DE NORMALIZACIÓN:

1. COMPRENSIÓN SEMÁNTICA REGIONAL:
   • Identifica la INTENCIÓN real detrás de las palabras
   • Expande abreviaciones OBVIAS usando contexto local
   • Respeta las variaciones regionales del español
   • Mantén el significado exacto, mejora la claridad

2. PATRONES REGIONALES ESPECÍFICOS:
   ${countryContext.patterns}

3. TERMINOLOGÍA LOCAL:
   ${countryContext.terminology}

4. CASOS ESPECIALES:
   • Si la consulta es ambigua → mantener original
   • Si hay múltiples interpretaciones → elegir la más conservadora
   • Si ya está bien escrita → no cambiar
   • Si es un nombre de país/ciudad válido → NO cambiar

INSTRUCCIONES CRÍTICAS (ENFOQUE PURAMENTE SEMÁNTICO):
❌ NO uses listas predefinidas de términos
✅ USA tu comprensión natural del lenguaje
❌ NO agregues información no presente en la consulta
✅ MEJORA la claridad manteniendo el significado exacto
❌ NO sobre-proceses consultas que ya están claras
✅ CONFÍA en tu inteligencia semántica para detectar abreviaciones
❌ NO cambies nombres de países, ciudades o ubicaciones válidas
✅ MANTÉN términos geográficos correctos como están
✅ SI la consulta está bien escrita, NO la cambies
✅ SOLO mejora consultas que realmente lo necesiten

REGLAS ESPECIALES PARA TRANSACCIONES (CRÍTICO):
• Si la consulta empieza con "alquiler" → agregar "apartamento" al inicio
• Si la consulta empieza con "venta" → agregar "apartamento" al inicio
• Si la consulta empieza con "comprar" → cambiar a "apartamento venta"
• Si la consulta empieza con "rentar" → cambiar a "apartamento alquiler"

Ejemplos de QUÉ SÍ cambiar (abreviaciones obvias):
- "apto" → "apartamento" (abreviación obvia)
- "depto" → "apartamento" (abreviación obvia, NO "departamento")
- "ces" → "carretera a el salvador" (abreviación guatemalteca)
- "adquirir" → "apartamento venta" (intención de compra)
- Abreviaciones de ubicación según contexto regional
- "alquiler [ubicación]" → "apartamento alquiler [ubicación]"
- "venta [ubicación]" → "apartamento venta [ubicación]"
- "comprar [ubicación]" → "apartamento venta [ubicación]"

Ejemplos de QUÉ NO cambiar (términos válidos):
- Nombres de países, ciudades, municipios válidos
- Términos ya correctos y completos
- Ubicaciones específicas reconocidas
- Consultas ya bien formadas

Responde ÚNICAMENTE en formato JSON:
{
  "normalized": "consulta normalizada aquí",
  "confidence": 0.95,
  "reasoning": "explicación de la lógica aplicada",
  "semantic_understanding": "intención detectada en la consulta"
}`;
  }

  /**
   * Obtiene el contexto del país actualmente configurado desde la base de datos
   */
  private async getCurrentCountryContext(): Promise<CountryContext> {
    try {
      // Por simplicidad y para evitar problemas de tipos, usar Guatemala como país por defecto
      // En el futuro se puede implementar detección dinámica más sofisticada
      const guatemalaContext = {
        countryCode: 'GT',
        countryName: 'Guatemala',
        hierarchy: {
          level1: { name: "Departamento" },
          level2: { name: "Municipio" },
          level3: { name: "Zona" },
          level4: { name: "Colonia" }
        }
      };

      return this.buildCountryContext(guatemalaContext.countryCode, guatemalaContext);

    } catch (error) {
      console.warn('Error obteniendo contexto de país, usando configuración genérica:', error);
      return this.getGenericSystemContext(); // Fallback a configuración genérica
    }
  }

  /**
   * Construye el contexto específico del país
   */
  private buildCountryContext(countryCode: string, config: any): CountryContext {
    const contexts: Record<string, CountryContext> = {
      'GT': {
        contextInfo: 'Guatemala - zonas numeradas, terminología guatemalteca',
        patterns: `
   UBICACIONES GUATEMALTECAS:
   • "z" + número → "zona" + número (ej: "z14" → "zona 14")
   • Reconoce abreviaciones de zonas numeradas
   • Mantén nombres de colonias específicas`,
        terminology: `
   TERMINOLOGÍA GUATEMALTECA:
   • "arrendar" → "alquilar" (estándar local)
   • "apto" → "apartamento" (abreviación común)
   • "depto" → "apartamento" (abreviación común, NO "departamento")
   • "ces" → "carretera a el salvador" (abreviación conocida)
   • "adquirir" → "apartamento venta" (intención de compra)`
      },
      'MX': {
        contextInfo: 'México - colonias, terminología mexicana',
        patterns: `
   UBICACIONES MEXICANAS:
   • "col" + nombre → "colonia" + nombre (ej: "col roma" → "colonia roma")
   • Reconoce abreviaciones de colonias
   • Mantén nombres de colonias específicas`,
        terminology: `
   TERMINOLOGÍA MEXICANA:
   • "rentar" → "alquilar" (estándar local)
   • "depto" → "departamento" (común)
   • "apto" → "apartamento" (abreviación común)`
      },
      'CO': {
        contextInfo: 'Colombia - barrios, terminología colombiana',
        patterns: `
   UBICACIONES COLOMBIANAS:
   • Reconoce abreviaciones de barrios y sectores
   • Mantén nombres de barrios específicos`,
        terminology: `
   TERMINOLOGÍA COLOMBIANA:
   • "arrendar" → "alquilar" (estándar local)
   • "apto" → "apartamento" (abreviación común)
   • "depto" → "departamento" (sinónimo regional)`
      }
    };

    return contexts[countryCode] || this.getDefaultContext(countryCode, config);
  }

  /**
   * Contexto genérico basado en la configuración del país
   */
  private getDefaultContext(countryCode: string, config: any): CountryContext {
    const levelNames = config?.hierarchy ? Object.values(config.hierarchy).map((level: any) => level.name) : [];

    return {
      contextInfo: `${config?.countryName || countryCode} - estructura: ${levelNames.join(' → ')}`,
      patterns: `
   UBICACIONES LOCALES:
   • Reconoce abreviaciones obvias de ubicaciones
   • Mantén nombres específicos de ${levelNames.join(', ')}`,
      terminology: `
   TERMINOLOGÍA LOCAL:
   • Respeta terminología regional estándar
   • Expande solo abreviaciones obvias`
    };
  }

  /**
   * Contexto genérico del sistema (agnóstico del país)
   */
  private getGenericSystemContext(): CountryContext {
    return {
      contextInfo: 'Sistema inmobiliario genérico - terminología estándar',
      patterns: `
   UBICACIONES GENÉRICAS:
   • Reconoce abreviaciones obvias de ubicaciones
   • Mantén nombres específicos de lugares
   • Expande solo abreviaciones claramente reconocibles`,
      terminology: `
   TERMINOLOGÍA GENÉRICA:
   • "apto" → "apartamento" (abreviación común)
   • "depto" → "departamento" (abreviación común)
   • Respeta terminología regional estándar`
    };
  }

  /**
   * Obtiene el nombre del país basado en el código
   */
  private getCountryName(countryCode: string): string {
    const countryNames: Record<string, string> = {
      'GT': 'Guatemala',
      'MX': 'México',
      'CO': 'Colombia',
      'CR': 'Costa Rica',
      'PA': 'Panamá'
    };
    return countryNames[countryCode] || countryCode;
  }

  /**
   * Obtiene la jerarquía de ubicación del país
   */
  private getCountryHierarchy(countryCode: string): any {
    const hierarchies: Record<string, any> = {
      'GT': {
        level1: { name: "Departamento" },
        level2: { name: "Municipio" },
        level3: { name: "Zona" },
        level4: { name: "Colonia" }
      },
      'MX': {
        level1: { name: "Estado" },
        level2: { name: "Municipio" },
        level3: { name: "Colonia" },
        level4: { name: "Fraccionamiento" }
      },
      'CO': {
        level1: { name: "Departamento" },
        level2: { name: "Municipio" },
        level3: { name: "Barrio" },
        level4: { name: "Sector" }
      }
    };

    return hierarchies[countryCode] || {
      level1: { name: "Región" },
      level2: { name: "Ciudad" },
      level3: { name: "Área" },
      level4: { name: "Sector" }
    };
  }

  /**
   * Parsea la respuesta de OpenAI
   */
  private parseNormalizationResponse(response: string, originalQuery: string): Omit<NormalizationResult, 'processingTime'> {
    try {
      // Limpiar respuesta y extraer JSON
      const cleanResponse = response.trim();
      let jsonMatch = cleanResponse.match(/\{[\s\S]*\}/);
      
      if (!jsonMatch) {
        throw new Error('No se encontró JSON válido en la respuesta');
      }

      const parsed = JSON.parse(jsonMatch[0]);
      
      // Validar estructura
      if (!parsed.normalized || typeof parsed.confidence !== 'number') {
        throw new Error('Estructura JSON inválida');
      }

      // Determinar si hay cambios significativos
      const hasChanges = parsed.normalized.toLowerCase().trim() !==
                        originalQuery.toLowerCase().trim();

      return {
        normalized: parsed.normalized.trim(),
        confidence: Math.max(0, Math.min(1, parsed.confidence)),
        reasoning: parsed.reasoning || 'Normalización completada',
        hasChanges: hasChanges || parsed.confidence > 0.5
      };

    } catch (error) {
      console.error('❌ Error parseando respuesta de normalización:', error);
      
      // Fallback: intentar extraer texto normalizado directamente
      const lines = response.split('\n').filter(line => line.trim());
      const possibleNormalized = lines.find(line => 
        line.includes('"') && !line.includes('confidence') && !line.includes('reasoning')
      );

      if (possibleNormalized) {
        const extracted = possibleNormalized.replace(/['"]/g, '').trim();
        return {
          normalized: extracted,
          confidence: 0.7,
          reasoning: 'Normalización extraída de respuesta no estructurada',
          hasChanges: extracted.toLowerCase().trim() !== originalQuery.toLowerCase().trim()
        };
      }

      // Último fallback: devolver respuesta completa limpia
      return {
        normalized: response.trim(),
        confidence: 0.3,
        reasoning: 'Fallback: respuesta no estructurada',
        hasChanges: response.trim().toLowerCase() !== originalQuery.toLowerCase().trim()
      };
    }
  }

  /**
   * Obtiene estadísticas del normalizador
   */
  getStats(): { model: string; timeout: number } {
    return {
      model: this.model,
      timeout: this.timeout
    };
  }
}
