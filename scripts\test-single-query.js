/**
 * 🔍 SCRIPT DE PRUEBA INDIVIDUAL: Test rápido de boost comercial
 * 
 * Uso:
 * node scripts/test-single-query.js "apartamento zona 14"
 * 
 * O usando la consulta por defecto:
 * node scripts/test-single-query.js
 */

const fetch = require('node-fetch');

// Configuración
const API_BASE_URL = process.env.API_BASE_URL || 'https://inmo-nine.vercel.app';
const API_KEY = process.env.NEXT_PUBLIC_RAG_API_KEY || 'demo-rag-key-2025';

// Consulta desde argumentos o usar default
const query = process.argv[2] || "apartamento zona 14";

console.log('🔍 PRUEBA INDIVIDUAL DE BOOST COMERCIAL');
console.log('=' .repeat(50));
console.log(`🔧 API: ${API_BASE_URL}`);
console.log(`🔑 Key: ${API_KEY.substring(0, 8)}...`);
console.log(`📝 Query: "${query}"`);
console.log('');

async function testSingleQuery() {
  const startTime = Date.now();
  
  try {
    console.log('⏳ Ejecutando búsqueda...');
    
    const response = await fetch(`${API_BASE_URL}/api/v1/search`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY,
      },
      body: JSON.stringify({
        query,
        options: {
          limit: 8,
          includeResponse: false,
          responseLanguage: 'es'
        },
      }),
    });

    const endTime = Date.now();
    const responseTime = endTime - startTime;

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    const properties = data.properties || [];

    console.log(`✅ Respuesta recibida en ${responseTime}ms`);
    console.log(`📊 ${properties.length} resultados encontrados`);
    console.log('');

    if (properties.length === 0) {
      console.log('⚠️  Sin resultados para mostrar');
      return;
    }

    // Mostrar resultados con análisis de boost
    console.log('🎯 RESULTADOS CON BOOST APLICADO:');
    console.log('=' .repeat(50));
    
    let premiumCount = 0;
    let featuredCount = 0;
    let normalCount = 0;

    properties.forEach((prop, index) => {
      const position = index + 1;
      const score = prop.score || 0;
      const originalScore = prop.originalScore || score;
      const boostFactor = prop.boostFactor || 1.0;
      const boostType = prop.boostType || 'normal';
      
      // Detectar tipo por campos (los datos están en prop.property)
      const propData = prop.property || prop;
      let type = 'NORMAL';
      let emoji = '🏠';
      
      if (propData.isPremium || boostType === 'premium') {
        type = 'PREMIUM';
        emoji = '💎';
        premiumCount++;
      } else if (propData.isFeatured || boostType === 'featured') {
        type = 'FEATURED';
        emoji = '⭐';
        featuredCount++;
      } else {
        normalCount++;
      }
      
      // Construir información de ubicación
      const location = propData.location || {};
      const address = [location.level3, location.level4, location.neighborhood].filter(Boolean).join(', ') || 'Sin ubicación';
      
      // Construir título descriptivo
      const title = `${propData.type || 'Propiedad'} ${propData.bedrooms ? `${propData.bedrooms}h` : ''} ${propData.bathrooms ? `${propData.bathrooms}b` : ''}`.trim();
      
      console.log(`${position}. ${emoji} ${type}`);
      console.log(`   📊 Score: ${score.toFixed(3)} ${boostFactor > 1 ? `(${originalScore.toFixed(3)} × ${boostFactor})` : ''}`);
      console.log(`   🏠 ${title}`);
      console.log(`   💰 ${propData.price ? `${propData.currency || 'Q'}${propData.price.toLocaleString()}` : 'Sin precio'}`);
      console.log(`   📍 ${address}`);
      console.log('');
    });

    // Resumen del boost
    console.log('📈 RESUMEN DE BOOST:');
    console.log('-'.repeat(30));
    console.log(`💎 Premium: ${premiumCount} propiedades (×2.0 boost)`);
    console.log(`⭐ Featured: ${featuredCount} propiedades (×1.5 boost)`);
    console.log(`🏠 Normal: ${normalCount} propiedades (×1.0 boost)`);
    console.log(`📊 Total: ${properties.length} resultados`);
    
    // Validación del orden
    console.log('\n✅ VALIDACIÓN:');
    console.log('-'.repeat(20));
    
    const firstPremium = properties.findIndex(p => p.isPremium || p.boostType === 'premium');
    const lastNormal = properties.map((p, i) => ({ ...p, index: i }))
      .filter(p => !p.isPremium && !p.isFeatured && p.boostType !== 'premium' && p.boostType !== 'featured')
      .pop();
    
    if (premiumCount > 0 && firstPremium === 0) {
      console.log('✅ Premium aparecen primero');
    } else if (premiumCount > 0) {
      console.log(`⚠️  Premium no están primero (posición ${firstPremium + 1})`);
    }
    
    if (premiumCount > 0 || featuredCount > 0) {
      console.log(`🎯 Boost comercial: ${premiumCount + featuredCount} propiedades promocionadas aparecen antes que las normales`);
    } else {
      console.log('📝 Solo propiedades normales en los resultados');
    }
    
    console.log('\n🎉 Prueba completada exitosamente!');

  } catch (error) {
    console.error('❌ ERROR:', error.message);
    
    if (error.message.includes('fetch')) {
      console.log('\n💡 POSIBLES SOLUCIONES:');
      console.log('   1. Verifica que el servidor esté corriendo');
      console.log('   2. Verifica la URL de la API');
      console.log('   3. Verifica tu conexión a internet');
    }
    
    if (error.message.includes('401') || error.message.includes('403')) {
      console.log('\n🔑 PROBLEMA DE AUTENTICACIÓN:');
      console.log('   Verifica tu API Key en las variables de entorno');
    }
  }
}

if (require.main === module) {
  testSingleQuery();
} 