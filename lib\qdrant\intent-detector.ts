/**
 * Detector de Intención de Transacción Semántica
 * 
 * Detecta si el usuario quiere comprar/vender vs alquilar/rentar
 * usando análisis semántico con embeddings, sin hardcodeo de palabras clave.
 */

import { generateEmbedding } from './embeddings';
import { getIntentConfig } from './unified-config';

// Tipos de intención de transacción
export type TransactionIntent = 'buy' | 'rent' | 'ambiguous';

// Resultado de detección de intención
export interface IntentDetectionResult {
  intent: TransactionIntent;
  confidence: number;
  buyScore: number;
  rentScore: number;
  reasoning: string;
}

// Embeddings pre-calculados para intenciones (se calcularán una vez al inicializar)
let buyIntentEmbedding: number[] | null = null;
let rentIntentEmbedding: number[] | null = null;

/**
 * Inicializar embeddings de intención (llamar una vez al inicio)
 */
export async function initializeIntentEmbeddings(force: boolean = false): Promise<void> {
  try {
    if (!force && buyIntentEmbedding && rentIntentEmbedding) {
      console.log('✅ Embeddings de intención ya inicializados');
      return;
    }

    console.log('🧠 Inicializando embeddings de intención...');

    // Textos representativos para cada intención (mejorados para Guatemala)
    const buyIntentText = "comprar compra vender venta adquirir adquisición propiedad inversión hipoteca financiamiento escritura propiedad permanente dueño propietario compro quiero comprar busco comprar necesito comprar ando buscando para comprar quiero invertir en inmuebles busco propiedad para comprar";
    const rentIntentText = "alquilar alquiler rentar renta arrendar arriendo temporal mensual inquilino contrato alquiler renta temporal vivir temporalmente alquilo quiero alquilar busco alquilar necesito alquilar ando buscando para alquilar busco para rentar";

    // Generar embeddings
    buyIntentEmbedding = await generateEmbedding(buyIntentText);
    rentIntentEmbedding = await generateEmbedding(rentIntentText);

    console.log('✅ Embeddings de intención inicializados');
  } catch (error) {
    console.error('❌ Error inicializando embeddings de intención:', error);
    throw error;
  }
}

/**
 * Calcular similitud coseno entre dos vectores
 */
function cosineSimilarity(a: number[], b: number[]): number {
  if (a.length !== b.length) {
    throw new Error('Los vectores deben tener la misma longitud');
  }
  
  let dotProduct = 0;
  let normA = 0;
  let normB = 0;
  
  for (let i = 0; i < a.length; i++) {
    dotProduct += a[i] * b[i];
    normA += a[i] * a[i];
    normB += b[i] * b[i];
  }
  
  return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
}

/**
 * Detectar intención de transacción en una consulta
 */
export async function detectTransactionIntent(query: string): Promise<IntentDetectionResult> {
  try {
    // Verificar que los embeddings estén inicializados
    if (!buyIntentEmbedding || !rentIntentEmbedding) {
      console.log('⚠️ Embeddings no inicializados, inicializando ahora...');
      await initializeIntentEmbeddings(true); // Forzar reinicialización con nuevos textos
    }
    
    if (!buyIntentEmbedding || !rentIntentEmbedding) {
      throw new Error('No se pudieron inicializar los embeddings de intención');
    }
    
    // Generar embedding de la consulta
    const queryEmbedding = await generateEmbedding(query);
    
    // Calcular similitudes
    const buyScore = cosineSimilarity(queryEmbedding, buyIntentEmbedding);
    const rentScore = cosineSimilarity(queryEmbedding, rentIntentEmbedding);
    
    // Determinar intención basada en diferencia de scores
    const scoreDifference = Math.abs(buyScore - rentScore);
    const intentConfig = getIntentConfig();
    const confidenceThreshold = intentConfig.confidence; // Umbral centralizado desde configuración unificada
    
    let intent: TransactionIntent;
    let confidence: number;
    let reasoning: string;
    
    if (scoreDifference < confidenceThreshold) {
      // Diferencia muy pequeña = ambiguo
      intent = 'ambiguous';
      confidence = 1 - scoreDifference; // Confianza alta en ambigüedad
      reasoning = `Consulta ambigua: diferencia de scores muy pequeña (${scoreDifference.toFixed(3)}) < umbral (${confidenceThreshold})`;
    } else if (buyScore > rentScore) {
      // Mayor similitud con compra/venta
      intent = 'buy';
      confidence = Math.min(scoreDifference, 1.0); // Normalizar confianza a máximo 1.0
      reasoning = `Intención de compra detectada: score compra (${buyScore.toFixed(3)}) > score alquiler (${rentScore.toFixed(3)}), diferencia: ${scoreDifference.toFixed(3)}`;
    } else {
      // Mayor similitud con alquiler
      intent = 'rent';
      confidence = Math.min(scoreDifference, 1.0); // Normalizar confianza a máximo 1.0
      reasoning = `Intención de alquiler detectada: score alquiler (${rentScore.toFixed(3)}) > score compra (${buyScore.toFixed(3)}), diferencia: ${scoreDifference.toFixed(3)}`;
    }

    // Validación adicional de coherencia intent-query
    const coherenceValidation = validateIntentCoherence(query, intent, confidence);
    if (!coherenceValidation.isCoherent) {
      intent = 'ambiguous';
      confidence = 0.1;
      reasoning = `Validación de coherencia falló: ${coherenceValidation.reason}. ${reasoning}`;
    }
    
    console.log(`🎯 Intención detectada: ${intent} (confianza: ${confidence.toFixed(3)})`);
    console.log(`📊 Scores - Compra: ${buyScore.toFixed(3)}, Alquiler: ${rentScore.toFixed(3)}`);
    
    return {
      intent,
      confidence,
      buyScore,
      rentScore,
      reasoning,
    };
    
  } catch (error) {
    console.error('❌ Error detectando intención:', error);
    
    // Fallback: retornar ambiguo en caso de error
    return {
      intent: 'ambiguous',
      confidence: 0,
      buyScore: 0,
      rentScore: 0,
      reasoning: `Error en detección: ${error instanceof Error ? error.message : 'Error desconocido'}`,
    };
  }
}

/**
 * Ajustar scores de resultados basado en intención detectada
 */
export function adjustScoresByIntent(
  results: Array<{ score: number; payload: { status: string } }>,
  intentResult: IntentDetectionResult
): Array<{ score: number; payload: { status: string }; originalScore: number; intentAdjustment: string }> {

  if (intentResult.intent === 'ambiguous') {
    // No ajustar scores para consultas ambiguas
    return results.map(result => ({
      ...result,
      originalScore: result.score,
      intentAdjustment: 'Sin ajuste (consulta ambigua)',
    }));
  }

  // Boost proporcional a la confianza usando configuración centralizada
  const intentConfig = getIntentConfig();
  const boostRange = intentConfig.boost.max - intentConfig.boost.min;
  const boostFactor = intentConfig.boost.min + (intentResult.confidence * boostRange);

  // Filtrar y procesar solo resultados que coinciden con la intención
  const matchingResults: Array<any> = [];
  let filteredCount = 0;

  results.forEach(result => {
    const originalScore = result.score;
    const isForSale = result.payload.status === 'for_sale';
    const isForRent = result.payload.status === 'for_rent';

    // Solo incluir propiedades que coinciden con la intención detectada
    if ((intentResult.intent === 'buy' && isForSale) ||
        (intentResult.intent === 'rent' && isForRent)) {

      // Aplicar boost a las propiedades que coinciden
      const newScore = originalScore * boostFactor;
      const adjustment = intentResult.intent === 'buy'
        ? `Boost ×${boostFactor} (venta + intención compra)`
        : `Boost ×${boostFactor} (alquiler + intención alquiler)`;

      matchingResults.push({
        ...result,
        score: newScore,
        originalScore,
        intentAdjustment: adjustment,
      });
    } else {
      // Contar propiedades filtradas para logging
      filteredCount++;
    }
  });

  // Ordenar por score ajustado
  matchingResults.sort((a, b) => b.score - a.score);

  // Solo devolver resultados que coinciden con la intención detectada
  console.log(`🎯 Filtrado por intención: ${matchingResults.length} propiedades coincidentes, ${filteredCount} filtradas`);

  return matchingResults;
}

/**
 * Verificar si los embeddings están inicializados
 */
export function areIntentEmbeddingsInitialized(): boolean {
  return buyIntentEmbedding !== null && rentIntentEmbedding !== null;
}

/**
 * Validar coherencia entre la consulta y la intención detectada
 */
function validateIntentCoherence(
  query: string,
  intent: TransactionIntent,
  confidence: number
): { isCoherent: boolean; reason: string } {
  const queryLower = query.toLowerCase();

  // Palabras que indican claramente intención de compra
  const buyKeywords = ['comprar', 'compra', 'venta', 'vender', 'adquirir', 'invertir', 'inversión'];
  // Palabras que indican claramente intención de alquiler
  const rentKeywords = ['alquilar', 'alquiler', 'rentar', 'renta', 'arrendar', 'arriendo'];

  const hasBuyKeywords = buyKeywords.some(keyword => queryLower.includes(keyword));
  const hasRentKeywords = rentKeywords.some(keyword => queryLower.includes(keyword));

  // Si hay palabras clave contradictorias, marcar como incoherente
  if (hasBuyKeywords && hasRentKeywords) {
    return {
      isCoherent: false,
      reason: 'Consulta contiene palabras clave contradictorias (compra y alquiler)'
    };
  }

  // Si la intención detectada contradice las palabras clave explícitas
  if (intent === 'buy' && hasRentKeywords && !hasBuyKeywords) {
    return {
      isCoherent: false,
      reason: 'Intención "compra" detectada pero consulta contiene palabras de alquiler'
    };
  }

  if (intent === 'rent' && hasBuyKeywords && !hasRentKeywords) {
    return {
      isCoherent: false,
      reason: 'Intención "alquiler" detectada pero consulta contiene palabras de compra'
    };
  }

  // Si la confianza es muy baja para una intención específica, marcar como incoherente
  if (intent !== 'ambiguous' && confidence < 0.3) {
    return {
      isCoherent: false,
      reason: `Confianza muy baja (${confidence.toFixed(3)}) para intención específica`
    };
  }

  return { isCoherent: true, reason: 'Validación de coherencia exitosa' };
}
