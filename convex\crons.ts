import { cronJobs } from "convex/server";
import { api } from "./_generated/api";

// 🕐 CRON JOBS AUTOMATIZADOS

const crons = cronJobs();

// ⏰ Ejecutar cada hora para verificar vencimientos
crons.interval(
  "check-expirations",
  { minutes: 60 }, // Cada hora
  api.featuredProperties.processExpirations,
  {}
);

// 🧹 Ejecutar cada día a las 3 AM para procesar vencimientos
crons.cron(
  "daily-cleanup",
  "0 3 * * *", // Cada día a las 3:00 AM
  api.featuredProperties.processExpirations,
  {}
);

// 📧 Ejecutar cada hora para verificar recordatorios de citas
crons.interval(
  "check-appointment-reminders",
  { minutes: 60 }, // Cada hora
  api.appointments.processAppointmentReminders,
  {}
);

// 📧 Ejecutar diariamente para verificar suscripciones y trials por vencer
crons.cron(
  "check-subscription-expirations",
  "0 9 * * *", // Cada día a las 9:00 AM
  api.subscriptions.processSubscriptionReminders,
  {}
);

// 🔄 NUEVO: Ejecutar cada 6 horas para reset automático de trials expirados y créditos agotados
crons.interval(
  "automatic-resets",
  { hours: 6 }, // Cada 6 horas
  api.subscriptions.processAutomaticResets,
  {}
);

// 📊 Los stats se pueden consultar mediante queries, no necesitan cron job

export default crons;