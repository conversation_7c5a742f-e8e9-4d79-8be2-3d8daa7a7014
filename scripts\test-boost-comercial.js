/**
 * 🎯 SCRIPT DE PRUEBA: BOOST COMERCIAL PREMIUM/FEATURED
 * 
 * Verifica que las propiedades premium y featured aparezcan correctamente
 * priorizadas en los resultados de búsqueda según el boost implementado:
 * - Premium: ×2.0 boost (aparecen primero)
 * - Featured: ×1.5 boost (aparecen segundo)
 * - Normal: ×1.0 (aparecen después)
 */

const fetch = require('node-fetch');

// Configuración
const API_BASE_URL = process.env.API_BASE_URL || 'https://inmo-nine.vercel.app';
const API_KEY = process.env.NEXT_PUBLIC_RAG_API_KEY || 'demo-rag-key-2025';

// Consultas específicas para probar el boost
const TEST_QUERIES = [
  "apartamento zona 14",
  "comprar apto z14", 
  "casa en venta zona 10",
  "apartamento 3 habitaciones",
  "propiedad zona viva",
  "alquiler apartamento guatemala",
  "casa zona 15 piscina",
  "departamento en venta"
];

console.log('🎯 PRUEBA DE BOOST COMERCIAL PREMIUM/FEATURED');
console.log('=' .repeat(60));
console.log(`🔧 API URL: ${API_BASE_URL}`);
console.log(`🔑 API Key: ${API_KEY.substring(0, 8)}...`);
console.log('');

// Función para ejecutar una búsqueda
async function executeSearch(query) {
  console.log(`🔍 Buscando: "${query}"`);
  const startTime = Date.now();
  
  try {
    const response = await fetch(`${API_BASE_URL}/api/v1/search`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY,
      },
      body: JSON.stringify({
        query,
        options: {
          limit: 8,
          includeResponse: false,
          responseLanguage: 'es'
        },
      }),
    });

    const endTime = Date.now();
    const responseTime = endTime - startTime;

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    const properties = data.properties || [];

    return {
      success: true,
      responseTime,
      query,
      results: properties.map((prop, index) => {
        // Los datos están anidados en prop.property
        const propData = prop.property || prop;
        const location = propData.location || {};
        const address = [location.level3, location.level4, location.neighborhood].filter(Boolean).join(', ') || 'Sin ubicación';
        const title = `${propData.type || 'Propiedad'} ${propData.bedrooms ? `${propData.bedrooms}h` : ''} ${propData.bathrooms ? `${propData.bathrooms}b` : ''}`.trim();
        
                 // Detectar el tipo de boost correctamente
         let boostType = 'normal';
         if (propData.isPremium) {
           boostType = 'premium';
         } else if (propData.isFeatured) {
           boostType = 'featured';
         }
         
         return {
          position: index + 1,
          id: prop.id,
          title: title,
          score: prop.score,
          originalScore: prop.originalScore || prop.score,
          boostFactor: prop.boostFactor || 1.0,
          boostType: boostType,
          isPremium: propData.isPremium || false,
          isFeatured: propData.isFeatured || false,
          price: propData.price,
          location: address
        };
      })
    };

  } catch (error) {
    return {
      success: false,
      responseTime: Date.now() - startTime,
      query,
      error: error.message,
      results: []
    };
  }
}

// Función para analizar el boost en los resultados
function analyzeBoost(results) {
  console.log('\n📊 ANÁLISIS DE BOOST:');
  console.log('-'.repeat(50));
  
  let premiumCount = 0;
  let featuredCount = 0;
  let normalCount = 0;
  let premiumFirst = true;
  let featuredAfterPremium = true;
  
  results.forEach((result, index) => {
    const type = result.boostType.toUpperCase();
    const boost = result.boostFactor;
    const originalScore = result.originalScore;
    const finalScore = result.score;
    
    console.log(`${result.position}. ${type} - Score: ${finalScore.toFixed(3)} (${originalScore.toFixed(3)} × ${boost})`);
    console.log(`   🏠 ${result.title}`);
    console.log(`   💰 ${result.price ? `USD${result.price.toLocaleString()}` : 'Sin precio'}`);
    console.log(`   📍 ${result.location || 'Sin ubicación'}`);
    console.log('');
    
    // Contar tipos
    if (result.boostType === 'premium') {
      premiumCount++;
      // Verificar si premium está en las primeras posiciones
      if (index >= premiumCount + featuredCount) {
        premiumFirst = false;
      }
    } else if (result.boostType === 'featured') {
      featuredCount++;
      // Verificar si featured está después de premium
      if (index < premiumCount) {
        featuredAfterPremium = false;
      }
    } else {
      normalCount++;
    }
  });
  
  return {
    premiumCount,
    featuredCount,
    normalCount,
    premiumFirst,
    featuredAfterPremium,
    totalResults: results.length
  };
}

// Función para validar el orden correcto
function validateBoostOrder(analysis) {
  console.log('✅ VALIDACIÓN DE ORDEN:');
  console.log('-'.repeat(30));
  
  const validations = [];
  
  // 1. Premium aparecen primero
  if (analysis.premiumCount > 0) {
    if (analysis.premiumFirst) {
      console.log('✅ Premium aparecen en primeras posiciones');
      validations.push(true);
    } else {
      console.log('❌ Premium NO aparecen en primeras posiciones');
      validations.push(false);
    }
  }
  
  // 2. Featured aparecen después de Premium pero antes de Normal
  if (analysis.featuredCount > 0) {
    if (analysis.featuredAfterPremium) {
      console.log('✅ Featured aparecen después de Premium');
      validations.push(true);
    } else {
      console.log('❌ Featured NO aparecen en orden correcto');
      validations.push(false);
    }
  }
  
  // 3. Resumen de distribución
  console.log(`\n📈 DISTRIBUCIÓN:`);
  console.log(`   💎 Premium: ${analysis.premiumCount} propiedades`);
  console.log(`   ⭐ Featured: ${analysis.featuredCount} propiedades`);
  console.log(`   🏠 Normal: ${analysis.normalCount} propiedades`);
  console.log(`   📊 Total: ${analysis.totalResults} resultados`);
  
  const allValid = validations.every(v => v);
  return {
    isValid: allValid,
    validations: validations.length,
    passed: validations.filter(v => v).length
  };
}

// Función principal
async function runBoostTest() {
  let totalTests = 0;
  let passedTests = 0;
  let failedQueries = [];
  
  for (const query of TEST_QUERIES) {
    console.log('\n' + '='.repeat(60));
    
    const searchResult = await executeSearch(query);
    
    if (!searchResult.success) {
      console.log(`❌ ERROR: ${searchResult.error}`);
      failedQueries.push({ query, error: searchResult.error });
      continue;
    }
    
    console.log(`⏱️  Tiempo: ${searchResult.responseTime}ms`);
    console.log(`📊 Resultados: ${searchResult.results.length}`);
    
    if (searchResult.results.length === 0) {
      console.log('⚠️  Sin resultados para analizar');
      continue;
    }
    
    const analysis = analyzeBoost(searchResult.results);
    const validation = validateBoostOrder(analysis);
    
    totalTests++;
    if (validation.isValid) {
      passedTests++;
      console.log('🎉 PRUEBA EXITOSA: Boost funcionando correctamente');
    } else {
      console.log('🚨 PRUEBA FALLIDA: Boost no funciona como esperado');
      failedQueries.push({ 
        query, 
        issue: `Validación ${validation.passed}/${validation.validations}` 
      });
    }
    
    // Pausa entre consultas
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // Resumen final
  console.log('\n' + '='.repeat(60));
  console.log('🏆 RESUMEN FINAL DEL BOOST TEST');
  console.log('='.repeat(60));
  console.log(`📊 Tests ejecutados: ${totalTests}`);
  console.log(`✅ Tests exitosos: ${passedTests}`);
  console.log(`❌ Tests fallidos: ${totalTests - passedTests}`);
  console.log(`📈 Tasa de éxito: ${totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : 0}%`);
  
  if (failedQueries.length > 0) {
    console.log('\n🚨 CONSULTAS CON PROBLEMAS:');
    failedQueries.forEach((item, index) => {
      console.log(`${index + 1}. "${item.query}" - ${item.error || item.issue}`);
    });
  }
  
  // Recomendación
  console.log('\n💡 RECOMENDACIÓN:');
  if (passedTests === totalTests && totalTests > 0) {
    console.log('✅ Sistema de boost funcionando PERFECTAMENTE');
    console.log('   Las propiedades premium/featured aparecen correctamente priorizadas');
  } else if (passedTests > totalTests * 0.8) {
    console.log('⚠️  Sistema de boost funcionando MAYORMENTE');
    console.log('   Revisar consultas fallidas para optimizar');
  } else {
    console.log('🚨 Sistema de boost tiene PROBLEMAS');
    console.log('   Revisar implementación del boost en simple-search.ts');
  }
}

// Ejecutar el test
if (require.main === module) {
  runBoostTest().catch(console.error);
}

module.exports = { runBoostTest, executeSearch, analyzeBoost }; 