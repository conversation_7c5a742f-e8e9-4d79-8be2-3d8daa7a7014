/**
 * Advanced Ranking Optimizer - Optimizador Avanzado de Ranking
 * 
 * Sistema que optimiza los algoritmos de ranking para priorizar mejor
 * los resultados más relevantes basado en múltiples factores.
 * 
 * Día 7 - Optimización de Precisión Semántica
 */

import { SemanticRelevanceAnalyzer, RelevanceAnalysisResult } from './semantic-relevance-analyzer';
import { PropertyPayload } from './config';
import { DecomposedQuery, DecompositionResult } from './semantic-decomposer';
import { SemanticWeights } from './unified-config';

// Tipos para optimización de ranking
export interface RankingFactors {
  semanticRelevance: number;      // Relevancia semántica
  locationPrecision: number;      // Precisión de ubicación
  featureMatch: number;          // Coincidencia de características
  priceAlignment: number;        // Alineación de precio
  popularityScore: number;       // Score de popularidad
  freshnessScore: number;        // Score de frescura/actualidad
  qualityScore: number;          // Score de calidad de datos
  userPreferenceScore: number;   // Score de preferencias del usuario
}

export interface OptimizedRankingResult {
  propertyId: string;
  originalScore: number;
  optimizedScore: number;
  rankingFactors: RankingFactors;
  rankingBoosts: string[];
  rankingPenalties: string[];
  finalRank: number;
  confidence: number;
  explanation: string;
}

export interface RankingOptimizationOptions {
  emphasizeLocation: boolean;
  emphasizePrice: boolean;
  emphasizeFeatures: boolean;
  userContext?: {
    searchHistory?: string[];
    preferences?: string[];
    budget?: { min: number; max: number };
  };
  guatemalanContext: boolean;
  diversityBoost: boolean;
}

/**
 * Optimizador avanzado que mejora el ranking de resultados
 * usando múltiples factores de relevancia y contexto
 */
export class AdvancedRankingOptimizer {
  private relevanceAnalyzer: SemanticRelevanceAnalyzer;

  constructor() {
    this.relevanceAnalyzer = new SemanticRelevanceAnalyzer();
  }

  /**
   * Optimiza el ranking de un conjunto de resultados
   */
  async optimizeRanking(
    results: Array<{
      id: string;
      score: number;
      payload: PropertyPayload;
    }>,
    query: string,
    decomposedQuery: DecompositionResult,
    weights: SemanticWeights,
    options: RankingOptimizationOptions = {
      emphasizeLocation: true,
      emphasizePrice: false,
      emphasizeFeatures: false,
      guatemalanContext: true,
      diversityBoost: false,
    }
  ): Promise<OptimizedRankingResult[]> {
    console.log(`🎯 Optimizando ranking para ${results.length} resultados`);

    // 1. Analizar relevancia de cada resultado
    const relevanceAnalyses = await Promise.all(
      results.map(result => 
        this.relevanceAnalyzer.analyzeRelevance({
          query,
          decomposedQuery,
          property: result.payload,
          originalScore: result.score,
          userContext: options.userContext,
        })
      )
    );

    // 2. Calcular factores de ranking para cada resultado
    const rankingResults = await Promise.all(
      results.map(async (result, index) => {
        const relevanceAnalysis = relevanceAnalyses[index];
        return this.calculateOptimizedRanking(
          result,
          relevanceAnalysis,
          weights,
          options
        );
      })
    );

    // 3. Aplicar diversidad si está habilitada
    let finalResults = rankingResults;
    if (options.diversityBoost) {
      finalResults = this.applyDiversityBoost(rankingResults);
    }

    // 4. Ordenar por score optimizado y asignar ranks finales
    finalResults.sort((a, b) => b.optimizedScore - a.optimizedScore);
    finalResults.forEach((result, index) => {
      result.finalRank = index + 1;
    });

    console.log(`✅ Ranking optimizado: ${finalResults.length} resultados procesados`);
    return finalResults;
  }

  /**
   * Calcula ranking optimizado para un resultado individual
   */
  private async calculateOptimizedRanking(
    result: { id: string; score: number; payload: PropertyPayload },
    relevanceAnalysis: RelevanceAnalysisResult,
    weights: SemanticWeights,
    options: RankingOptimizationOptions
  ): Promise<OptimizedRankingResult> {
    // 1. Calcular factores de ranking
    const rankingFactors = this.calculateRankingFactors(result, relevanceAnalysis, options);

    // 2. Calcular score optimizado
    const { optimizedScore, boosts, penalties } = this.calculateOptimizedScore(
      result.score,
      rankingFactors,
      weights,
      options
    );

    // 3. Generar explicación
    const explanation = this.generateRankingExplanation(rankingFactors, boosts, penalties);

    return {
      propertyId: result.id,
      originalScore: result.score,
      optimizedScore,
      rankingFactors,
      rankingBoosts: boosts,
      rankingPenalties: penalties,
      finalRank: 0, // Se asignará después del ordenamiento
      confidence: relevanceAnalysis.confidence,
      explanation,
    };
  }

  /**
   * Calcula todos los factores de ranking
   */
  private calculateRankingFactors(
    result: { id: string; score: number; payload: PropertyPayload },
    relevanceAnalysis: RelevanceAnalysisResult,
    options: RankingOptimizationOptions
  ): RankingFactors {
    return {
      semanticRelevance: relevanceAnalysis.metrics.overallRelevance,
      locationPrecision: relevanceAnalysis.metrics.locationAccuracy,
      featureMatch: relevanceAnalysis.metrics.featureAlignment,
      priceAlignment: relevanceAnalysis.metrics.priceRelevance,
      popularityScore: this.calculatePopularityScore(result.payload),
      freshnessScore: this.calculateFreshnessScore(result.payload),
      qualityScore: this.calculateQualityScore(result.payload),
      userPreferenceScore: this.calculateUserPreferenceScore(result.payload, options.userContext),
    };
  }

  /**
   * Calcula score optimizado aplicando factores y ajustes
   */
  private calculateOptimizedScore(
    originalScore: number,
    factors: RankingFactors,
    weights: SemanticWeights,
    options: RankingOptimizationOptions
  ): { optimizedScore: number; boosts: string[]; penalties: string[] } {
    let score = originalScore;
    const boosts: string[] = [];
    const penalties: string[] = [];

    // 1. Aplicar relevancia semántica (factor principal)
    const semanticMultiplier = 0.7 + (factors.semanticRelevance * 0.6); // 0.7 - 1.3
    score *= semanticMultiplier;
    if (factors.semanticRelevance >= 0.8) {
      boosts.push(`Alta relevancia semántica (+${((semanticMultiplier - 1) * 100).toFixed(0)}%)`);
    } else if (factors.semanticRelevance <= 0.3) {
      penalties.push(`Baja relevancia semántica (${((1 - semanticMultiplier) * 100).toFixed(0)}%)`);
    }

    // 2. Aplicar precisión de ubicación (si está enfatizada)
    if (options.emphasizeLocation && weights.location > 0.4) {
      const locationMultiplier = 0.8 + (factors.locationPrecision * 0.4); // 0.8 - 1.2
      score *= locationMultiplier;
      if (factors.locationPrecision >= 0.8) {
        boosts.push(`Ubicación muy precisa (+${((locationMultiplier - 1) * 100).toFixed(0)}%)`);
      } else if (factors.locationPrecision <= 0.3) {
        penalties.push(`Ubicación imprecisa (-${((1 - locationMultiplier) * 100).toFixed(0)}%)`);
      }
    }

    // 3. Aplicar coincidencia de características (si está enfatizada)
    if (options.emphasizeFeatures && weights.amenities > 0.3) {
      const featureMultiplier = 0.9 + (factors.featureMatch * 0.2); // 0.9 - 1.1
      score *= featureMultiplier;
      if (factors.featureMatch >= 0.8) {
        boosts.push(`Características alineadas (+${((featureMultiplier - 1) * 100).toFixed(0)}%)`);
      }
    }

    // 4. Aplicar alineación de precio (si está enfatizada)
    if (options.emphasizePrice && weights.price > 0.1) {
      const priceMultiplier = 0.85 + (factors.priceAlignment * 0.3); // 0.85 - 1.15
      score *= priceMultiplier;
      if (factors.priceAlignment >= 0.8) {
        boosts.push(`Precio dentro del rango (+${((priceMultiplier - 1) * 100).toFixed(0)}%)`);
      } else if (factors.priceAlignment <= 0.3) {
        penalties.push(`Precio fuera del rango (-${((1 - priceMultiplier) * 100).toFixed(0)}%)`);
      }
    }

    // 5. Aplicar factores de calidad
    if (factors.qualityScore >= 0.8) {
      score *= 1.05;
      boosts.push('Datos de alta calidad (+5%)');
    } else if (factors.qualityScore <= 0.4) {
      score *= 0.95;
      penalties.push('Datos incompletos (-5%)');
    }

    // 6. Aplicar frescura
    if (factors.freshnessScore >= 0.8) {
      score *= 1.03;
      boosts.push('Información actualizada (+3%)');
    }

    // 7. Aplicar preferencias del usuario
    if (factors.userPreferenceScore >= 0.7) {
      score *= 1.1;
      boosts.push('Coincide con preferencias (+10%)');
    }

    // 8. Contexto guatemalteco
    if (options.guatemalanContext && factors.semanticRelevance >= 0.6) {
      score *= 1.02;
      boosts.push('Contexto guatemalteco (+2%)');
    }

    return {
      optimizedScore: Math.min(score, 1.0), // Clamp máximo en 1.0
      boosts,
      penalties,
    };
  }

  /**
   * Calcula score de popularidad basado en métricas de la propiedad
   */
  private calculatePopularityScore(property: PropertyPayload): number {
    let score = 0.5; // Base neutral

    // Factores que pueden indicar popularidad
    if (property.price && property.area) {
      const pricePerSqm = property.price / property.area;
      // Propiedades con precio/m² razonable pueden ser más populares
      if (pricePerSqm >= 8000 && pricePerSqm <= 25000) { // Rango típico Guatemala
        score += 0.2;
      }
    }

    // Propiedades con más información tienden a ser más populares
    const infoCompleteness = this.calculateInfoCompleteness(property);
    score += infoCompleteness * 0.3;

    return Math.min(score, 1.0);
  }

  /**
   * Calcula score de frescura basado en fechas disponibles
   */
  private calculateFreshnessScore(property: PropertyPayload): number {
    // Por ahora retornar score neutral ya que no tenemos fechas
    // En el futuro se puede implementar basado en created_at, updated_at, etc.
    return 0.7;
  }

  /**
   * Calcula score de calidad de datos
   */
  private calculateQualityScore(property: PropertyPayload): number {
    let score = 0;
    let totalFields = 0;

    // Campos esenciales
    const essentialFields = [
      property.type, property.price, property.level3, 
      property.bedrooms, property.bathrooms, property.area
    ];
    
    essentialFields.forEach(field => {
      totalFields++;
      if (field !== null && field !== undefined && field !== '') {
        score += 1;
      }
    });

    // Campos adicionales que mejoran la calidad
    const additionalFields = [
      property.description, property.level4, property.neighborhood
    ];
    
    additionalFields.forEach(field => {
      totalFields++;
      if (field !== null && field !== undefined && field !== '') {
        score += 0.5;
      }
    });

    return totalFields > 0 ? score / totalFields : 0.5;
  }

  /**
   * Calcula score de preferencias del usuario
   */
  private calculateUserPreferenceScore(
    property: PropertyPayload, 
    userContext?: RankingOptimizationOptions['userContext']
  ): number {
    if (!userContext) return 0.5;

    let score = 0.5;

    // Verificar presupuesto
    if (userContext.budget && property.price) {
      const { min, max } = userContext.budget;
      if (property.price >= min && property.price <= max) {
        score += 0.3;
      } else if (property.price < min * 1.2 || property.price > max * 0.8) {
        score += 0.1; // Cerca del rango
      }
    }

    // Verificar preferencias
    if (userContext.preferences && property.description) {
      const description = property.description.toLowerCase();
      const matchingPreferences = userContext.preferences.filter(pref => 
        description.includes(pref.toLowerCase())
      );
      score += Math.min(0.2, matchingPreferences.length * 0.05);
    }

    return Math.min(score, 1.0);
  }

  /**
   * Calcula completitud de información
   */
  private calculateInfoCompleteness(property: PropertyPayload): number {
    const fields = [
      property.type, property.price, property.level3, property.level4,
      property.bedrooms, property.bathrooms, property.area, property.description
    ];
    
    const completedFields = fields.filter(field => 
      field !== null && field !== undefined && field !== ''
    ).length;
    
    return completedFields / fields.length;
  }

  /**
   * Aplica boost de diversidad para evitar resultados muy similares
   */
  private applyDiversityBoost(results: OptimizedRankingResult[]): OptimizedRankingResult[] {
    // Implementación simple: penalizar propiedades muy similares en ubicación
    const locationGroups = new Map<string, OptimizedRankingResult[]>();
    
    // Agrupar por ubicación
    results.forEach(result => {
      // Usar el ID de la propiedad como clave temporal
      // En implementación real, usar level3 o level4
      const locationKey = result.propertyId.substring(0, 3); // Simplificado
      if (!locationGroups.has(locationKey)) {
        locationGroups.set(locationKey, []);
      }
      locationGroups.get(locationKey)!.push(result);
    });

    // Aplicar penalización a propiedades adicionales en la misma ubicación
    locationGroups.forEach(group => {
      if (group.length > 1) {
        group.slice(1).forEach((result, index) => {
          const penalty = 0.95 - (index * 0.02); // Penalización gradual
          result.optimizedScore *= penalty;
          result.rankingPenalties.push(`Diversidad de ubicación (-${((1 - penalty) * 100).toFixed(0)}%)`);
        });
      }
    });

    return results;
  }

  /**
   * Genera explicación del ranking
   */
  private generateRankingExplanation(
    factors: RankingFactors,
    boosts: string[],
    penalties: string[]
  ): string {
    const parts = [];
    
    parts.push(`Relevancia: ${(factors.semanticRelevance * 100).toFixed(0)}%`);
    
    if (factors.locationPrecision >= 0.7) {
      parts.push(`ubicación precisa`);
    }
    
    if (factors.featureMatch >= 0.7) {
      parts.push(`características alineadas`);
    }
    
    if (boosts.length > 0) {
      parts.push(`boosts: ${boosts.length}`);
    }
    
    if (penalties.length > 0) {
      parts.push(`penalizaciones: ${penalties.length}`);
    }

    return parts.join(', ');
  }
}
