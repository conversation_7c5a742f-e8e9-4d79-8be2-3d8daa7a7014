"use client"
import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { CreditsDisplay } from "@/components/ui/credits-display";
import { useUser, useClerk } from '@clerk/nextjs';
import { UserProfile } from '@clerk/nextjs';
import { useTheme } from 'next-themes';
import { <PERSON>, Palette, Trash2 } from "lucide-react";
import { useState, useEffect } from 'react';
import { toast } from 'sonner';

import { Dialog, DialogContent, DialogTrigger, DialogTitle, DialogHeader } from "@/components/ui/dialog";
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { useRouter } from 'next/navigation';

export default function SettingsPage() {
  const { user } = useUser();
  const { signOut } = useClerk();
  const { theme, setTheme } = useTheme();

  const router = useRouter();
  const deleteUserMutation = useMutation(api.users.deleteUser);
  const updateProfileMutation = useMutation(api.users.updateProfile);
  
  // Estados locales para las configuraciones
  const [twoFactorAuth, setTwoFactorAuth] = useState(false);
  
  // Estados para el formulario de perfil
  const [profileData, setProfileData] = useState({
    name: "",
    email: "",
    bio: ""
  });

  // Cargar datos del usuario al montar el componente
  useEffect(() => {
    if (user) {
      const metadata = user.unsafeMetadata as any || {};
      setProfileData({
        name: (metadata.displayName as string) || user.firstName || "",
        email: user.emailAddresses?.[0]?.emailAddress || "",
        bio: (metadata.bio as string) || ""
      });
    }
  }, [user]);

  // Cargar color del tema guardado (ya no es necesario porque el contexto global lo maneja)
  useEffect(() => {
    // El contexto global ya maneja la carga del color guardado
  }, []);

  // Funciones para manejar cambios
  const handleSaveProfile = async () => {
    if (!user) return;
    
    try {
      // Guardar información personalizada en metadatos de Clerk
      const currentMetadata = user.unsafeMetadata as any || {};
      const newMetadata = {
        ...currentMetadata,
        displayName: profileData.name.trim(),
        bio: profileData.bio.trim(),
        lastUpdated: new Date().toISOString()
      };

      // Solo actualizar si hay cambios reales
      const hasChanges = 
        newMetadata.displayName !== currentMetadata.displayName ||
        newMetadata.bio !== currentMetadata.bio;

      if (hasChanges) {
        // Actualizar metadatos en Clerk
        await user.update({
          unsafeMetadata: newMetadata
        });

        // Actualizar perfil en la base de datos local usando updateProfile
        await updateProfileMutation({
          name: newMetadata.displayName,
          bio: newMetadata.bio
        });

        toast.success("¡Información pública actualizada!");
      } else {
        toast.info("No hay cambios para guardar");
      }
    } catch (error: any) {
      console.error("Error actualizando perfil:", error);
      toast.error("Error al actualizar el perfil. Intenta de nuevo en unos momentos.");
    }
  };

  const handleTwoFactorToggle = (value: boolean) => {
    setTwoFactorAuth(value);
    toast.success(`Autenticación de dos factores ${value ? 'activada' : 'desactivada'}`);
  };



  const handleDeleteAccount = async () => {
    toast("🚨 Confirmar eliminación permanente", {
      description: "Esta acción eliminará TODOS tus datos: propiedades, mensajes, citas, favoritos y toda tu información de Inmova. Tu cuenta de autenticación permanecerá activa y podrás volver a registrarte.",
      action: {
        label: "Solo Eliminar Datos",
        onClick: async () => {
          await performDataDeletion(false);
        },
      },
      cancel: {
        label: "Cancelar",
        onClick: () => {
          toast.dismiss();
        },
      },
      duration: 15000,
    });
  };

  const handleDeleteAccountAndAuth = async () => {
    toast("🚨 ELIMINACIÓN TOTAL - Sin vuelta atrás", {
      description: "Esta acción eliminará TODOS tus datos Y tu cuenta de autenticación. NO podrás recuperar esta cuenta ni volver a usar este email. ¿Estás completamente seguro?",
      action: {
        label: "Sí, Eliminar TODO",
        onClick: async () => {
          await performDataDeletion(true);
        },
      },
      cancel: {
        label: "Cancelar",
        onClick: () => {
          toast.dismiss();
        },
      },
      duration: 20000,
    });
  };

  const performDataDeletion = async (deleteClerkAccount: boolean) => {
    try {
      // 1. Primero eliminar datos de Convex
      const result = await deleteUserMutation();

      if (!result) {
        toast.error("❌ No se recibió respuesta del servidor");
        return;
      }

      if (!result.message) {
        toast.error("❌ Respuesta del servidor incompleta");
        return;
      }

      toast.success(`✅ ${result.message}`);

      // Mostrar detalles de lo que se eliminó
      if (result.totalDeleted > 0) {
        const details = [];
        if (result.deletedCount.properties > 0) details.push(`${result.deletedCount.properties} propiedades`);
        if (result.deletedCount.favorites > 0) details.push(`${result.deletedCount.favorites} favoritos`);
        if (result.deletedCount.messages > 0) details.push(`${result.deletedCount.messages} mensajes`);
        if (result.deletedCount.appointments > 0) details.push(`${result.deletedCount.appointments} citas`);
        if (result.deletedCount.transactions > 0) details.push(`${result.deletedCount.transactions} transacciones`);

        if (details.length > 0) {
          toast.info(`📊 Eliminados: ${details.join(', ')}`);
        }
      }

      // 2. Enviar email de confirmación si se elimina la cuenta completa
      if (deleteClerkAccount && user) {
        try {
          toast.info("📧 Enviando confirmación por email...");

          // Enviar email de confirmación antes de eliminar la cuenta
          const emailResult = await fetch('/api/send-account-deletion-email', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              userEmail: user.emailAddresses?.[0]?.emailAddress,
              userName: profileData.name || user.firstName || 'Usuario',
              deletionDate: new Date().toISOString(),
              totalDeleted: result.totalDeleted,
              deletedCount: result.deletedCount,
            }),
          });

          if (emailResult.ok) {
            toast.success("📧 Email de confirmación enviado");
          } else {
            console.warn("No se pudo enviar el email de confirmación");
          }
        } catch (emailError) {
          console.error("Error enviando email de confirmación:", emailError);
          // No bloquear la eliminación por error de email
        }

        try {
          toast.info("🔄 Eliminando cuenta de autenticación...");
          await user.delete();
          toast.success("✅ Cuenta de autenticación eliminada completamente");
          // No necesitamos signOut() porque la cuenta ya no existe
          window.location.href = "/";
        } catch (clerkError) {
          console.error("Error eliminando cuenta de Clerk:", clerkError);
          toast.error("❌ Error eliminando cuenta de autenticación. Los datos ya fueron eliminados.");
          // Hacer logout normal si falla la eliminación de Clerk
          setTimeout(async () => {
            await signOut();
          }, 2000);
        }
      } else {
        // Solo logout normal si no se elimina la cuenta de Clerk
        setTimeout(async () => {
          toast.info("🔄 Cerrando sesión automáticamente...");
          await signOut();
        }, 3000);
      }
    } catch (error) {
      console.error("❌ Error eliminando cuenta:", error);
      toast.error(`Error al eliminar la cuenta: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  };



  return (
    <div className="flex flex-col gap-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-semibold tracking-tight">Configuración</h1>
          <p className="text-muted-foreground mt-2">Gestiona la configuración de tu cuenta y preferencias</p>
        </div>
        
        <CreditsDisplay variant="compact" />
      </div>

      <Tabs defaultValue="account" className="space-y-6">
        <TabsList>
          <TabsTrigger value="account">Cuenta</TabsTrigger>
          <TabsTrigger value="appearance">Apariencia</TabsTrigger>
          <TabsTrigger value="security">Seguridad</TabsTrigger>
        </TabsList>

        <TabsContent value="account">
          <div className="space-y-6">
            {/* Profile Settings */}
            <Card>
              <CardHeader>
                <CardTitle>Información Pública</CardTitle>
                <CardDescription>Esta información aparecerá en tus propiedades publicadas</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Nombre público</Label>
                    <Input 
                      id="name" 
                      placeholder="Ej: Juan Pérez" 
                      value={profileData.name}
                      onChange={(e) => setProfileData({...profileData, name: e.target.value})}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="bio">Descripción profesional</Label>
                    <Textarea 
                      id="bio" 
                      placeholder="Ej: Agente inmobiliario con 5 años de experiencia en la zona norte..." 
                      value={profileData.bio}
                      onChange={(e) => setProfileData({...profileData, bio: e.target.value})}
                      rows={3}
                    />
                  </div>
                  
                  <Button className="bg-blue-600 hover:bg-blue-700" onClick={handleSaveProfile}>
                    Guardar Información Pública
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Account Settings */}
            <Card>
              <CardHeader>
                <CardTitle>Configuración de Cuenta</CardTitle>
                <CardDescription>Cambiar foto de perfil, email, contraseña y configuración de seguridad</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-4">
                  <Avatar className="h-16 w-16">
                    <AvatarImage src={user?.imageUrl} alt="Usuario" />
                    <AvatarFallback>
                      {(profileData.name || user?.firstName || "U").charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="space-y-2">
                    <p className="text-sm font-medium">{user?.emailAddresses?.[0]?.emailAddress}</p>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button variant="outline">
                          Administrar Cuenta
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-2xl">
                        <DialogHeader>
                          <DialogTitle>Configuración de Cuenta</DialogTitle>
                        </DialogHeader>
                        <UserProfile 
                          routing="hash"
                          appearance={{
                            elements: {
                              rootBox: "w-full",
                              card: "shadow-none border-0"
                            }
                          }}
                        />
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              </CardContent>
            </Card>


          </div>
        </TabsContent>



        <TabsContent value="appearance">
          <Card>
            <CardHeader>
              <CardTitle>Apariencia</CardTitle>
              <CardDescription>Personaliza la apariencia del sistema</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <Palette className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <Label>Modo de Tema</Label>
                      <p className="text-sm text-muted-foreground">Elige entre modo claro u oscuro</p>
                    </div>
                  </div>
                  <Select value={theme} onValueChange={setTheme}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Seleccionar tema" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="light">Claro</SelectItem>
                      <SelectItem value="dark">Oscuro</SelectItem>
                      <SelectItem value="system">Sistema</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security">
          <Card>
            <CardHeader>
              <CardTitle>Seguridad</CardTitle>
              <CardDescription>Configuración de seguridad y privacidad</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <Lock className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <Label>Autenticación de Dos Factores</Label>
                      <p className="text-sm text-muted-foreground">Añade una capa extra de seguridad a tu cuenta</p>
                    </div>
                  </div>
                  <Switch 
                    checked={twoFactorAuth}
                    onCheckedChange={handleTwoFactorToggle}
                  />
                </div>
                <Separator />
                <div className="space-y-4">
                  <Label className="text-red-600">Eliminar Cuenta</Label>

                  {/* Opción 1: Solo eliminar datos */}
                  <div className="border border-orange-200 bg-orange-50 p-4 rounded-lg">
                    <h4 className="font-medium text-orange-900 mb-2">Eliminar Solo Datos</h4>
                    <p className="text-sm text-orange-700 mb-3">
                      Elimina todos tus datos de Inmova (propiedades, favoritos, mensajes, citas, etc.).
                      Tu cuenta de autenticación permanecerá activa y podrás volver a registrarte.
                    </p>
                    <Button
                      variant="outline"
                      onClick={handleDeleteAccount}
                      className="gap-2 border-orange-300 text-orange-700 hover:bg-orange-100"
                    >
                      <Trash2 className="h-4 w-4" />
                      Eliminar Solo Datos
                    </Button>
                  </div>

                  {/* Opción 2: Eliminar todo incluyendo cuenta */}
                  <div className="border border-red-200 bg-red-50 p-4 rounded-lg">
                    <h4 className="font-medium text-red-900 mb-2">Eliminar Cuenta Completa</h4>
                    <p className="text-sm text-red-700 mb-3">
                      <strong>⚠️ PELIGRO:</strong> Elimina todos tus datos Y tu cuenta de autenticación.
                      NO podrás recuperar esta cuenta ni volver a usar este email. Esta acción es irreversible.
                    </p>
                    <Button
                      variant="destructive"
                      onClick={handleDeleteAccountAndAuth}
                      className="gap-2"
                    >
                      <Trash2 className="h-4 w-4" />
                      Eliminar Cuenta Completa
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
