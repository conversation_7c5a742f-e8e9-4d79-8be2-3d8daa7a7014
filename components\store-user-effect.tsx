"use client";

import { useUser } from "@clerk/nextjs";
import { useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { useEffect, useState } from "react";

export default function StoreUserEffect() {
  const { user, isLoaded } = useUser();
  const storeUser = useMutation(api.users.store);
  const [hasStored, setHasStored] = useState(false);

  useEffect(() => {
    // Solo ejecutar si Clerk está cargado, el usuario está autenticado y no hemos guardado ya
    if (isLoaded && user && !hasStored) {
      console.log("Storing user in Convex...");
      storeUser()
        .then(async () => {
          console.log("User stored successfully");
          setHasStored(true);

          // La sincronización de perfil público se maneja ahora a través de updateProfile
          // cuando el usuario actualiza su información en la página de configuración
        })
        .catch((error) => {
          console.error("Error storing user:", error);
          // Reintentar después de un tiempo si falla
          setTimeout(() => setHasStored(false), 5000);
        });
    }
  }, [user, isLoaded, storeUser, hasStored]);

  return null; // Este componente no renderiza nada
}