"use client";

import { useState, useEffect, useCallback } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { useUser } from "@clerk/nextjs";
import { useRouter, use<PERSON>ara<PERSON> } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  ArrowLeft, 
  Upload, 
  MapPin, 
  Home, 
  DollarSign,
  Save,
  Eye,
  AlertCircle,
  Loader2,
  X,
  Info
} from "lucide-react";
import Link from "next/link";
import AmenitiesSection from "../../new/_components/AmenitiesSection";
import { ImageUpload } from "@/components/ui/image-upload";
import { validatePropertyForm, type ValidationError, type PropertyFormData as ValidatedPropertyFormData, getFieldError } from "@/lib/validations";
import { toast } from "sonner";
import { RichTextarea } from "@/components/ui/rich-textarea";
import { FormError } from "@/components/ui/form-error";
import { LocationSelector } from "@/components/forms/LocationSelector";
import { Id } from "@/convex/_generated/dataModel";

interface PropertyFormData {
  title: string;
  description: string;
  price: number;
  currency: string;
  type: string;
  status: string;
  address: string;
  bedrooms: number;
  bathrooms: number;
  area: number;
  builtYear: number;
  parking: number;
  amenities: string[];
  images: string[];
  featured: boolean;
}

export default function EditPropertyPage() {
  const { user } = useUser();
  const router = useRouter();
  const params = useParams();
  const propertyId = params.id as Id<"properties">;
  
  const updateProperty = useMutation(api.properties.updateProperty);
  
  // Obtener la propiedad existente
  const existingProperty = useQuery(api.properties.getPropertyById, { id: propertyId });
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [originalData, setOriginalData] = useState<PropertyFormData | null>(null);
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([]);
  const [locationData, setLocationData] = useState<any>(null);
  const [locationMetadata, setLocationMetadata] = useState({
    customKeywords: [] as string[],
    landmarks: [] as string[],
    accessibility: [] as string[],
    neighborhood: "",
  });
  const [formData, setFormData] = useState<PropertyFormData>({
    title: "",
    description: "",
    price: 0,
    currency: "GTQ",
    type: "",
    status: "for_sale",
    address: "",
    bedrooms: 1,
    bathrooms: 1,
    area: 0,
    builtYear: new Date().getFullYear(),
    parking: 0,
    amenities: [],
    images: [],
    featured: false,
  });

  // Cargar datos de la propiedad cuando esté disponible
  useEffect(() => {
    if (existingProperty) {
      const propertyData = {
        title: existingProperty.title || "",
        description: existingProperty.description || "",
        price: existingProperty.price || 0,
        currency: existingProperty.currency || "GTQ",
        type: existingProperty.type || "",
        status: existingProperty.status || "for_sale",
        address: existingProperty.address || "",
        bedrooms: existingProperty.bedrooms || 1,
        bathrooms: existingProperty.bathrooms || 1,
        area: existingProperty.area || 0,
        builtYear: existingProperty.builtYear || new Date().getFullYear(),
        parking: existingProperty.parking || 0,
        amenities: existingProperty.amenities || [],
        images: existingProperty.images || [],
        featured: existingProperty.featured || false,
      };
      
      setFormData(propertyData);
      setOriginalData(propertyData);

      // Cargar datos de ubicación jerárquica si existen
      if (existingProperty.location) {
        setLocationData(existingProperty.location);
      }

      // Cargar metadatos de ubicación existentes
      if (existingProperty.locationMetadata) {
        setLocationMetadata({
          customKeywords: existingProperty.locationMetadata.searchKeywords || [],
          landmarks: existingProperty.locationMetadata.landmarks || [],
          accessibility: existingProperty.locationMetadata.accessibility || [],
          neighborhood: existingProperty.locationMetadata.neighborhood || "",
        });
      }

      setIsLoading(false);
    } else if (existingProperty === null) {
      // Propiedad no encontrada
      setIsLoading(false);
      toast.error("Propiedad no encontrada");
      router.push("/dashboard/properties");
    }
  }, [existingProperty, router]);

  // Detectar cambios no guardados
  useEffect(() => {
    if (originalData) {
      const hasChanges = JSON.stringify(formData) !== JSON.stringify(originalData);
      setHasUnsavedChanges(hasChanges);
    }
  }, [formData, originalData]);

  // Advertir antes de salir si hay cambios no guardados
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [hasUnsavedChanges]);

  // Verificar permisos
  useEffect(() => {
    if (existingProperty && user?.id) {
      const isOwner = existingProperty.ownerId === user.id;
      const isAgent = existingProperty.agentId === user.id;
      
      if (!isOwner && !isAgent) {
        toast.error("No tienes permisos para editar esta propiedad");
        router.push("/dashboard/properties");
      }
    }
  }, [existingProperty, user?.id, router]);

  const handleInputChange = (field: keyof PropertyFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Limpiar errores del campo cuando el usuario empiece a escribir
    if (validationErrors.length > 0) {
      setValidationErrors(prev => prev.filter(error => error.field !== field));
    }
  };

  const handleLocationChange = useCallback((location: any) => {
    setLocationData(location);
    setHasUnsavedChanges(true);
  }, []);

  const handleSubmit = async (e: React.FormEvent, saveAsDraft = false) => {
    e.preventDefault();
    
    if (!user?.id) {
      toast.error("Debes estar autenticado para editar una propiedad");
      return;
    }

    if (!existingProperty) {
      toast.error("No se pudo cargar la propiedad");
      return;
    }

    // Validar formulario
    const errors = validatePropertyForm(formData as ValidatedPropertyFormData, saveAsDraft);
    if (errors.length > 0) {
      setValidationErrors(errors);
      // Mostrar errores usando toast
      errors.forEach(error => {
        toast.error(error.message);
      });
      return;
    }

    // Limpiar errores si la validación es exitosa
    setValidationErrors([]);
    setIsSubmitting(true);

    try {
      // Generar keywords de búsqueda automáticamente (igual que en createProperty)
      const generateLocationKeywords = (location: any): string[] => {
        const keywords: string[] = [];
        if (location?.country) keywords.push(location.country.name.toLowerCase());
        if (location?.level1) keywords.push(location.level1.name.toLowerCase());
        if (location?.level2) keywords.push(location.level2.name.toLowerCase());
        if (location?.level3) keywords.push(location.level3.name.toLowerCase());
        if (location?.level4) keywords.push(location.level4.name.toLowerCase());
        if (location?.level5) keywords.push(location.level5.name.toLowerCase());
        if (location?.level6) keywords.push(location.level6.name.toLowerCase());

        // Agregar keywords de la dirección
        if (formData.address) {
          const addressWords = formData.address.toLowerCase()
            .split(/[\s,.-]+/)
            .filter(word => word.length > 2);
          keywords.push(...addressWords);
        }

        return [...new Set(keywords)]; // Remover duplicados
      };



      const propertyData = {
        id: propertyId,
        title: formData.title,
        description: formData.description,
        price: Number(formData.price),
        currency: formData.currency,
        type: formData.type as "house" | "apartment" | "office" | "land" | "commercial",
        status: (saveAsDraft ? "draft" : formData.status) as "for_sale" | "for_rent" | "sold" | "rented" | "draft",
        address: formData.address,
        // Nuevo campo jerárquico de ubicación
        location: locationData,
        // Combinar metadatos editables por usuario + auto-generados
        locationMetadata: locationData ? {
          searchKeywords: [
            ...locationMetadata.customKeywords,
            ...generateLocationKeywords(locationData)
          ].filter((keyword, index, array) => array.indexOf(keyword) === index), // Eliminar duplicados
          landmarks: locationMetadata.landmarks,
          accessibility: locationMetadata.accessibility,
          neighborhood: locationMetadata.neighborhood || undefined,
        } : undefined,
        bedrooms: formData.bedrooms ? Number(formData.bedrooms) : undefined,
        bathrooms: formData.bathrooms ? Number(formData.bathrooms) : undefined,
        area: Number(formData.area),
        builtYear: formData.builtYear ? Number(formData.builtYear) : undefined,
        parking: formData.parking ? Number(formData.parking) : undefined,
        amenities: formData.amenities,
        images: formData.images,
        featured: formData.featured,
      };

      const result = await updateProperty(propertyData);

      if (saveAsDraft) {
        toast.success("¡Propiedad guardada como borrador!");
        router.push("/dashboard/properties");
      } else {
        toast.success("¡Propiedad actualizada exitosamente!");
        router.push(`/properties/${propertyId}`);
      }
    } catch (error) {
      console.error("Error updating property:", error);
      toast.error("Error al actualizar la propiedad. Por favor intenta de nuevo.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    if (hasUnsavedChanges) {
      const confirmCancel = confirm(
        "Tienes cambios sin guardar. ¿Estás seguro de que quieres cancelar? Se perderán todos los cambios."
      );
      if (!confirmCancel) return;
    }
    router.push("/dashboard/properties");
  };

  // Estado de carga
  if (isLoading || !existingProperty) {
    return (
      <div className="flex flex-col gap-6 p-6 max-w-4xl mx-auto">
        <div className="flex items-center justify-center py-20">
          <div className="flex items-center gap-3">
            <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
            <span className="text-lg text-gray-600">Cargando propiedad...</span>
          </div>
        </div>
      </div>
    );
  }

  // Si no es el propietario ni el agente
  const isOwner = existingProperty.ownerId === user?.id;
  const isAgent = existingProperty.agentId === user?.id;
  
  if (!isOwner && !isAgent) {
    return (
      <div className="flex flex-col gap-6 p-6 max-w-4xl mx-auto">
        <div className="text-center py-20">
          <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Sin permisos</h2>
          <p className="text-gray-600 mb-6">No tienes permisos para editar esta propiedad</p>
          <Link href="/dashboard/properties">
            <Button>Volver a Mis Propiedades</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-6 p-6 max-w-4xl mx-auto">
      <div className="flex items-center gap-4">
        <Link href="/dashboard/properties" className="flex items-center text-blue-600 hover:text-blue-800">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Volver a Mis Propiedades
        </Link>
      </div>

      <div>
        <h1 className="text-3xl font-semibold tracking-tight">Editar Propiedad</h1>
        <p className="text-muted-foreground mt-2">
          Actualiza la información de tu propiedad.
        </p>
      </div>

      {/* Resumen de errores */}
      {validationErrors.length > 0 && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <AlertCircle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="font-medium text-red-800 mb-2">
                  Hay {validationErrors.length} error{validationErrors.length > 1 ? 'es' : ''} que corregir:
                </h3>
                <ul className="text-sm text-red-700 space-y-1">
                  {validationErrors.map((error: any, index: any) => (
                    <li key={`error-${error.field}-${index}`}>• {error.message}</li>
                  ))}
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <form onSubmit={(e) => handleSubmit(e, false)} className="space-y-8">
        {/* Información Básica */}
        <Card>
          <CardHeader className="flex flex-row items-center gap-3">
            <Home className="h-5 w-5 text-blue-600" />
            <div>
              <CardTitle>Información Básica</CardTitle>
              <CardDescription>
                Datos principales de la propiedad
              </CardDescription>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="md:col-span-2">
                <Label htmlFor="title" className="text-sm font-medium">Título de la propiedad *</Label>
                <Input
                  id="title"
                  data-error="title"
                  placeholder="Ej: Casa moderna con jardín en zona residencial"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  className="mt-1"
                />
                <FormError message={getFieldError(validationErrors, 'title')} />
              </div>

              <div className="md:col-span-2">
                <Label htmlFor="description" className="text-sm font-medium">Descripción *</Label>
                <RichTextarea
                  value={formData.description}
                  onChange={(value) => handleInputChange('description', value)}
                  placeholder="Describe tu propiedad detalladamente. Puedes usar **negritas**, *cursivas*, y hacer saltos de línea para mejor legibilidad."
                  className="mt-1"
                />
                <FormError message={getFieldError(validationErrors, 'description')} />
              </div>

              <div>
                <Label htmlFor="type" className="text-sm font-medium">Tipo de propiedad *</Label>
                <Select value={formData.type} onValueChange={(value) => handleInputChange('type', value)}>
                  <SelectTrigger data-error="type" className="mt-1">
                    <SelectValue placeholder="Selecciona el tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="house">Casa</SelectItem>
                    <SelectItem value="apartment">Apartamento</SelectItem>
                    <SelectItem value="office">Oficina</SelectItem>
                    <SelectItem value="land">Terreno</SelectItem>
                    <SelectItem value="commercial">Comercial</SelectItem>
                  </SelectContent>
                </Select>
                <FormError message={getFieldError(validationErrors, 'type')} />
              </div>

              <div>
                <Label htmlFor="status" className="text-sm font-medium">Estado *</Label>
                <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                  <SelectTrigger data-error="status" className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="for_sale">En Venta</SelectItem>
                    <SelectItem value="for_rent">En Alquiler</SelectItem>
                    <SelectItem value="sold">Vendida</SelectItem>
                    <SelectItem value="rented">Alquilada</SelectItem>
                  </SelectContent>
                </Select>
                <FormError message={getFieldError(validationErrors, 'status')} />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Precio */}
        <Card>
          <CardHeader className="flex flex-row items-center gap-3">
            <DollarSign className="h-5 w-5 text-green-600" />
            <div>
              <CardTitle>Precio</CardTitle>
              <CardDescription>
                Establece el precio de tu propiedad
              </CardDescription>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="md:col-span-2">
                <Label htmlFor="price" className="text-sm font-medium">Precio *</Label>
                <Input
                  id="price"
                  type="number"
                  data-error="price"
                  placeholder="0"
                  value={formData.price || ''}
                  onChange={(e) => handleInputChange('price', e.target.value)}
                  className="mt-1"
                />
                <FormError message={getFieldError(validationErrors, 'price')} />
              </div>

              <div>
                <Label htmlFor="currency" className="text-sm font-medium">Moneda</Label>
                <Select value={formData.currency} onValueChange={(value) => handleInputChange('currency', value)}>
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="GTQ">GTQ (Quetzales)</SelectItem>
                    <SelectItem value="USD">USD (Dólares)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Ubicación */}
        <Card>
          <CardHeader className="flex flex-row items-center gap-3">
            <MapPin className="h-5 w-5 text-red-600" />
            <div>
              <CardTitle>Ubicación</CardTitle>
              <CardDescription>
                Dirección y ubicación de la propiedad
              </CardDescription>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-6">
              <div>
                <Label htmlFor="address" className="text-sm font-medium">Dirección *</Label>
                <Input
                  id="address"
                  data-error="address"
                  placeholder="Ej: 5ta Avenida 12-34, Zona 10"
                  value={formData.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  className="mt-1"
                />
                <FormError message={getFieldError(validationErrors, 'address')} />
              </div>

              {/* Selector de Ubicación Jerárquica */}
              <LocationSelector
                value={locationData}
                onChange={handleLocationChange}
                errors={{}} // TODO: Agregar validación específica para ubicación
              />

              {/* Metadatos de Ubicación Editables */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-semibold text-blue-900 mb-4 flex items-center gap-2">
                  <Info className="h-4 w-4" />
                  Información Adicional de Ubicación
                </h4>
                <p className="text-sm text-blue-800 mb-4">
                  Agrega información específica que ayude a los compradores a encontrar y entender mejor la ubicación de tu propiedad.
                </p>

                <div className="space-y-4">
                  {/* Palabras Clave Personalizadas */}
                  <div>
                    <Label htmlFor="customKeywords" className="text-sm font-medium text-blue-900">
                      Palabras Clave de Búsqueda
                    </Label>
                    <Input
                      id="customKeywords"
                      placeholder="Ej: centro comercial, universidad, metro (separadas por comas)"
                      value={locationMetadata.customKeywords.join(", ")}
                      onChange={(e) => setLocationMetadata(prev => ({
                        ...prev,
                        customKeywords: e.target.value.split(",").map(k => k.trim()).filter(k => k)
                      }))}
                      className="mt-1"
                    />
                    <p className="text-xs text-blue-700 mt-1">
                      Términos que la gente podría usar para buscar propiedades en esta área
                    </p>
                  </div>

                  {/* Puntos de Referencia */}
                  <div>
                    <Label htmlFor="landmarks" className="text-sm font-medium text-blue-900">
                      Puntos de Referencia Cercanos
                    </Label>
                    <Input
                      id="landmarks"
                      placeholder="Ej: CC Pradera, Hospital Roosevelt, Torre del Reformador"
                      value={locationMetadata.landmarks.join(", ")}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        // Solo procesar si hay comas, sino mantener el texto tal como está
                        if (inputValue.includes(',')) {
                          setLocationMetadata(prev => ({
                            ...prev,
                            landmarks: inputValue.split(",").map(l => l.trim()).filter(l => l)
                          }));
                        } else {
                          // Si no hay comas, mantener como un solo elemento
                          setLocationMetadata(prev => ({
                            ...prev,
                            landmarks: inputValue ? [inputValue] : []
                          }));
                        }
                      }}
                      className="mt-1"
                    />
                    <p className="text-xs text-blue-700 mt-1">
                      Lugares conocidos, centros comerciales, hospitales, universidades
                    </p>
                  </div>

                  {/* Accesibilidad */}
                  <div>
                    <Label htmlFor="accessibility" className="text-sm font-medium text-blue-900">
                      Transporte y Accesibilidad
                    </Label>
                    <Input
                      id="accessibility"
                      placeholder="Ej: Transmetro, parada de bus, carretera principal"
                      value={locationMetadata.accessibility.join(", ")}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        // Solo procesar si hay comas, sino mantener el texto tal como está
                        if (inputValue.includes(',')) {
                          setLocationMetadata(prev => ({
                            ...prev,
                            accessibility: inputValue.split(",").map(a => a.trim()).filter(a => a)
                          }));
                        } else {
                          // Si no hay comas, mantener como un solo elemento
                          setLocationMetadata(prev => ({
                            ...prev,
                            accessibility: inputValue ? [inputValue] : []
                          }));
                        }
                      }}
                      className="mt-1"
                    />
                    <p className="text-xs text-blue-700 mt-1">
                      Opciones de transporte público, vías de acceso principales
                    </p>
                  </div>

                  {/* Descripción del Vecindario */}
                  <div>
                    <Label htmlFor="neighborhood" className="text-sm font-medium text-blue-900">
                      Descripción del Vecindario
                    </Label>
                    <Input
                      id="neighborhood"
                      placeholder="Ej: Zona residencial tranquila con colegios y comercios cercanos"
                      value={locationMetadata.neighborhood}
                      onChange={(e) => setLocationMetadata(prev => ({
                        ...prev,
                        neighborhood: e.target.value
                      }))}
                      className="mt-1"
                    />
                    <p className="text-xs text-blue-700 mt-1">
                      Características y ambiente del área donde está ubicada la propiedad
                    </p>
                  </div>
                </div>
              </div>

            </div>
          </CardContent>
        </Card>

        {/* Características */}
        <Card>
          <CardHeader>
            <CardTitle>Características</CardTitle>
            <CardDescription>
              Detalles técnicos de la propiedad
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-2 md:grid-cols-5 gap-6">
              <div>
                <Label htmlFor="bedrooms" className="text-sm font-medium">Habitaciones</Label>
                <Input
                  id="bedrooms"
                  type="number"
                  min="0"
                  value={formData.bedrooms || ''}
                  onChange={(e) => handleInputChange('bedrooms', e.target.value)}
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="bathrooms" className="text-sm font-medium">Baños</Label>
                <Input
                  id="bathrooms"
                  type="number"
                  min="0"
                  step="0.5"
                  value={formData.bathrooms || ''}
                  onChange={(e) => handleInputChange('bathrooms', e.target.value)}
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="area" className="text-sm font-medium">Área (m²) *</Label>
                <Input
                  id="area"
                  type="number"
                  min="0"
                  data-error="area"
                  placeholder="120"
                  value={formData.area || ''}
                  onChange={(e) => handleInputChange('area', e.target.value)}
                  className="mt-1"
                />
                <FormError message={getFieldError(validationErrors, 'area')} />
              </div>

              <div>
                <Label htmlFor="builtYear" className="text-sm font-medium">Año construcción</Label>
                <Input
                  id="builtYear"
                  type="number"
                  min="1900"
                  max={new Date().getFullYear()}
                  value={formData.builtYear || ''}
                  onChange={(e) => handleInputChange('builtYear', e.target.value)}
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="parking" className="text-sm font-medium">Parqueos</Label>
                <Input
                  id="parking"
                  type="number"
                  min="0"
                  value={formData.parking || ''}
                  onChange={(e) => handleInputChange('parking', e.target.value)}
                  className="mt-1"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Amenidades */}
        <Card>
          <CardHeader>
            <CardTitle>Amenidades</CardTitle>
            <CardDescription>
              Selecciona las amenidades que incluye tu propiedad
            </CardDescription>
          </CardHeader>
          <CardContent>
            <AmenitiesSection
              selectedAmenities={formData.amenities}
              onAmenitiesChange={(amenities) => handleInputChange('amenities', amenities)}
            />
          </CardContent>
        </Card>

        {/* Imágenes */}
        <Card>
          <CardHeader>
            <CardTitle>Imágenes</CardTitle>
            <CardDescription>
              Agrega fotos de tu propiedad para atraer más compradores
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ImageUpload
              images={formData.images}
              onImagesChange={(images) => handleInputChange('images', images)}
              maxImages={10}
            />
            <FormError message={getFieldError(validationErrors, 'images')} />
          </CardContent>
        </Card>

        {/* Botones de acción */}
        <div className="flex flex-col gap-4 pt-6">
          {/* Indicador de cambios */}
          {hasUnsavedChanges && (
            <div className="flex items-center gap-2 text-amber-600 bg-amber-50 border border-amber-200 rounded-lg px-4 py-2">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm font-medium">Tienes cambios sin guardar</span>
            </div>
          )}

          {/* Botones principales */}
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="flex-1 bg-blue-600 hover:bg-blue-700 h-11"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Actualizando...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Guardar Cambios
                </>
              )}
            </Button>

            <Button
              type="button"
              variant="outline"
              disabled={isSubmitting}
              onClick={(e) => handleSubmit(e, true)}
              className="flex-1 h-11"
            >
              <Save className="mr-2 h-4 w-4" />
              Guardar como Borrador
            </Button>
          </div>

          {/* Botones secundarios */}
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isSubmitting}
              className="flex-1 border-gray-300 text-gray-700 hover:bg-gray-50 h-11"
            >
              <X className="mr-2 h-4 w-4" />
              Cancelar
            </Button>

            <Button
              type="button"
              variant="outline"
              asChild
              className="flex-1 h-11"
            >
              <Link href={`/properties/${propertyId}`}>
                <Eye className="mr-2 h-4 w-4" />
                Ver Propiedad
              </Link>
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
} 