// Configuración de Stripe para Inmova
export const STRIPE_CONFIG = {
  // URLs base
  apiVersion: "2023-10-16" as const,
  
  // Productos y precios - Solo Premium
  products: {
    premium: {
      name: "Inmova Premium",
      priceId: process.env.STRIPE_PRICE_PREMIUM || "price_premium_dev",
      price: 99,
      currency: "USD",
      interval: "month",
      // ❌ REMOVIDO: trial_period_days (manejado por nuestro sistema)
      features: [
        "Propiedades ilimitadas",
        "300 créditos por mes",
        "Posición premium en home (25 créditos)",
        "Destacados ilimitados",
        "Analytics avanzados",
        "Soporte prioritario 24/7",
        "Sin marca de agua"
        // ❌ REMOVIDO: "15 días de prueba gratis" (manejado internamente)
      ]
    }
  },
  
  // Límites por plan - Solo Free y Premium
  limits: {
    free: {
      properties: 5,
      credits: 10,
      priority: 0,
      features: [
        "5 propiedades máximo",
        "10 créditos por mes",
        "Búsqueda básica"
      ]
    },
    premium: {
      properties: 999999, // Ilimitadas
      credits: 300,
      priority: 2,
      features: [
        "Propiedades ilimitadas",
        "300 créditos por mes",
        "Posición premium en home (25 créditos)",
        "Destacados ilimitados",
        "Analytics avanzados",
        "Soporte prioritario 24/7",
        "Sin marca de agua"
        // ❌ REMOVIDO: "15 días de prueba gratis" (manejado internamente)
      ]
    },
    // Para usuarios en trial
    trial: {
      properties: 999999, // Ilimitadas durante trial
      credits: 300,
      priority: 2,
      features: [
        "Acceso completo por 15 días",
        "Propiedades ilimitadas",
        "300 créditos incluidos",
        "Todas las funciones premium"
      ]
    }
  },
  
  // URLs de redirección
  urls: {
    success: process.env.NEXT_PUBLIC_APP_URL + "/dashboard/finance?success=true&session_id={CHECKOUT_SESSION_ID}",
    cancel: process.env.NEXT_PUBLIC_APP_URL + "/dashboard/finance?canceled=true"
  },

  // Configuración de checkout
  checkout: {
    mode: "subscription" as const,
    payment_method_types: ["card"] as const,
    allow_promotion_codes: true,
    billing_address_collection: "required" as const,
    locale: "es" as const, // ¡ESPAÑOL POR DEFECTO!
    // Branding personalizado
    custom_text: {
      shipping_address: {
        message: "Información de facturación"
      },
      submit: {
        message: "Procesar suscripción"
      }
    }
  }
};

// Helper para obtener información del plan (solo free, premium y trial)
export function getPlanInfo(plan: "free" | "premium" | "trial") {
  const limits = STRIPE_CONFIG.limits[plan];
  const product = (plan === "premium") ? STRIPE_CONFIG.products.premium : null;

  return {
    name: product?.name || (plan === "trial" ? "Trial Premium" : "Plan Gratuito"),
    price: product?.price || 0,
    currency: product?.currency || "USD",
    interval: product?.interval || "month",
    priceDisplay: plan === "trial" ? "Gratis por 15 días" : (product?.price ? formatPrice(product.price) + "/mes" : "Gratis"),
    features: limits.features,
    limits: {
      properties: limits.properties,
      credits: limits.credits,
      priority: limits.priority
    },
    priceId: product?.priceId,
    isProduction: process.env.NODE_ENV === "production"
    // ❌ REMOVIDO: trial_period_days (manejado por nuestro sistema)
  };
}

// Helper para formatear precio
export function formatPrice(amount: number, currency: string = "USD") {
  return new Intl.NumberFormat("es-GT", {
    style: "currency",
    currency: currency,
  }).format(amount);
}

// Helper para verificar si un rol necesita suscripción
export function needsSubscription(role: string): boolean {
  return role === 'seller' || role === 'agent';
}

// Helper para determinar si debe iniciar trial
export function shouldStartTrial(role: string, isNewUser: boolean, hasUsedTrial: boolean): boolean {
  return needsSubscription(role) && isNewUser && !hasUsedTrial;
}

// Helper para obtener límites efectivos (considerando trial)
export function getEffectiveLimits(subscription: any) {
  if (!subscription) {
    return STRIPE_CONFIG.limits.free;
  }

  // Si está en trial activo, usar límites de trial
  if (subscription.isTrialActive) {
    return STRIPE_CONFIG.limits.trial;
  }

  // Usar límites del plan actual
  return STRIPE_CONFIG.limits[subscription.plan as keyof typeof STRIPE_CONFIG.limits] || STRIPE_CONFIG.limits.free;
}