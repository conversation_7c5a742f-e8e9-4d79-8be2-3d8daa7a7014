import { NextRequest, NextResponse } from 'next/server';
import { PropertyRAGEngine, SearchFilters, SearchResult, SearchWithIntentResult } from '@/lib/qdrant/search';
import { simpleSearchEngine } from '@/lib/qdrant/simple-search';
import { validateQdrantConfig } from '@/lib/qdrant/config';
import { IntentDetectionResult } from '@/lib/qdrant/intent-detector';
import { getScoreThreshold } from '@/lib/qdrant/unified-config';
import { api } from '@/convex/_generated/api';
import OpenAI from 'openai';

// Configurar OpenAI para respuestas generadas
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Verificar configuración de OpenAI
if (!process.env.OPENAI_API_KEY) {
  console.warn('⚠️ OPENAI_API_KEY no está configurada. Las respuestas de IA no funcionarán.');
}

// Rate limiting simple en memoria (para producción usar Redis)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

// Validar API Key con rate limiting
function validateApiKey(request: NextRequest): { valid: boolean; rateLimited?: boolean } {
  const apiKey = request.headers.get('X-API-Key');
  const validApiKey = process.env.RAG_API_KEY;
  const publicApiKey = process.env.NEXT_PUBLIC_RAG_API_KEY;

  if (!validApiKey) {
    console.warn('RAG_API_KEY not configured');
    return { valid: false };
  }

  // Verificar si es una API key válida
  const isValidKey = apiKey === validApiKey || apiKey === publicApiKey;
  if (!isValidKey) {
    return { valid: false };
  }

  // Rate limiting solo para la API key pública
  if (apiKey === publicApiKey) {
    const clientIP = request.headers.get('x-forwarded-for') ||
                     request.headers.get('x-real-ip') ||
                     'unknown';

    const now = Date.now();
    const windowMs = 60 * 1000; // 1 minuto
    const maxRequests = 30; // 30 requests por minuto por IP

    const key = `${clientIP}:${apiKey}`;
    const current = rateLimitMap.get(key);

    if (current && current.resetTime > now) {
      if (current.count >= maxRequests) {
        return { valid: false, rateLimited: true };
      }
      current.count++;
    } else {
      rateLimitMap.set(key, { count: 1, resetTime: now + windowMs });
    }

    // Limpiar entradas expiradas cada 100 requests
    if (Math.random() < 0.01) {
      for (const [k, v] of rateLimitMap.entries()) {
        if (v.resetTime <= now) {
          rateLimitMap.delete(k);
        }
      }
    }
  }

  return { valid: true };
}



// Endpoint principal de búsqueda semántica
export async function POST(request: NextRequest) {
  try {
    // Validar API Key con rate limiting
    const validation = validateApiKey(request);
    if (!validation.valid) {
      if (validation.rateLimited) {
        return NextResponse.json(
          { error: 'Rate limit exceeded. Please try again later.' },
          { status: 429 }
        );
      }
      return NextResponse.json(
        { error: 'Invalid or missing API key' },
        { status: 401 }
      );
    }

    // Validar configuración
    validateQdrantConfig();

    // Parsear body
    const body = await request.json();
    const {
      query,
      options = {},
    } = body;

    if (!query || typeof query !== 'string') {
      return NextResponse.json(
        { error: 'Query is required and must be a string' },
        { status: 400 }
      );
    }

    const {
      limit = 12, // ✅ AUMENTADO: Para mostrar más resultados relevantes
      includeResponse = true,
      responseLanguage = 'es',
      filters = {},
      scoreThreshold = getScoreThreshold(), // ✅ UNIFICADO: Usar configuración centralizada
      searchType = 'simple', // ✅ FORZADO: Sistema Simple SIEMPRE (validado empíricamente)
      useSemanticWeights = false, // ❌ FORZADO: Sistema multi-componente DESACTIVADO permanentemente
      includeBreakdown = false, // Incluir breakdown detallado de scores
      enableAdvancedPrecision = false, // 🎯 NUEVO: Sistema de precisión semántica avanzada (Día 7)
      precisionOptions = {}, // Opciones para el sistema de precisión avanzada
      useSimpleSearch = false, // 🚀 NUEVO: Usar búsqueda simple con embeddings estructurados
    } = options;

    console.log(`API Search request: "${query}" (type: ${searchType})`);

    // Marcar tiempo de inicio para métricas
    const searchStartTime = Date.now();

    // Crear instancia del motor RAG
    const ragEngine = new PropertyRAGEngine();

    // 🚀 USAR MOTOR DE BÚSQUEDA SIMPLE OPTIMIZADO (SIEMPRE)
    let searchResults: SearchResult[] = [];
    let semanticResults: any = null;
    let intentResult: IntentDetectionResult | undefined;
    let advancedMetrics: any = null;

    try {
      console.log(`🚀 SIMPLE SEARCH ENGINE: "${query}" (forced optimization)`);

      // Convertir filtros al formato del motor simple
      const simpleFilters = filters ? {
        status: filters.status as 'for_sale' | 'for_rent',
        type: filters.type,
        priceRange: filters.priceRange,
        location: filters.location,
        bedrooms: filters.bedrooms,
        bathrooms: filters.bathrooms,
      } : undefined;

      // Usar el motor simple optimizado SIEMPRE
      const simpleResults = await simpleSearchEngine.search({
        query,
        limit,
        scoreThreshold,
        filters: simpleFilters,
        adaptiveThreshold: true,
        intentDetection: true,
      });

      // Convertir al formato esperado por la API
      searchResults = simpleResults.map(result => ({
        id: result.id,
        score: result.score,
        payload: result.payload,
      }));

    } catch (searchError) {
      console.error('❌ Error en búsqueda simple:', searchError);
      return NextResponse.json(
        {
          error: 'Search service temporarily unavailable',
          details: searchError instanceof Error ? searchError.message : 'Unknown error'
        },
        { status: 503 }
      );
    }

    // ✅ CORREGIDO: Obtener datos completos desde Convex incluyendo imágenes
    const propertyIds = searchResults.map((result: any) => result.payload.propertyId);
    
    // Obtener propiedades completas desde Convex (incluyendo imágenes)
    const { ConvexHttpClient } = await import('convex/browser');
    const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);
    const fullProperties = await Promise.all(
      propertyIds.map(async (id: string) => {
        try {
          return await convex.query(api.properties.getPropertyById, { id: id as any });
        } catch (error) {
          console.warn(`Could not fetch property ${id} from Convex:`, error);
          return null;
        }
      })
    );

    // Preparar respuesta base con datos completos
    const response: any = {
      query,
      searchType: 'simple', // Forzar a simple
      resultsCount: searchResults.length,
      properties: searchResults.map((result: any, index: number) => {
        const fullProperty = fullProperties[index];
        
        return {
          id: result.id,
          score: Math.min(Math.round(result.score * 100) / 100, 1.0), // ✅ NORMALIZAR: Máximo 100% (1.0)
          property: {
            propertyId: result.payload.propertyId,
            title: result.payload.title, // ✅ Título desde Qdrant (indexado)
            type: result.payload.type,
            status: result.payload.status,
            price: result.payload.price,
            currency: result.payload.currency,
            address: result.payload.address, // ✅ Dirección desde Qdrant (indexada)
            bedrooms: result.payload.bedrooms,
            bathrooms: result.payload.bathrooms,
            area: result.payload.area,
            location: {
              country: result.payload.country,
              level1: result.payload.level1,
              level2: result.payload.level2,
              level3: result.payload.level3,
              level4: result.payload.level4,
              neighborhood: result.payload.neighborhood,
            },
            coordinates: result.payload.coordinates,
            amenities: result.payload.amenities,
            images: fullProperty?.images || [], // ✅ CORREGIDO: Imágenes desde Convex
            isActive: result.payload.isActive,
            isFeatured: result.payload.isFeatured,
            isPremium: result.payload.isPremium,
            createdAt: result.payload.createdAt,
            updatedAt: result.payload.updatedAt,
          },
        };
      }),
      timestamp: new Date().toISOString(),
    };

    // Agregar métricas avanzadas si están disponibles
    if (advancedMetrics) {
      response.precisionMetrics = advancedMetrics.precisionMetrics;
      response.relevanceAnalyses = advancedMetrics.relevanceAnalyses;
      response.rankingOptimizations = advancedMetrics.rankingOptimizations;
      response.advancedProcessingTime = advancedMetrics.advancedProcessingTime;
    }

    // Agregar breakdown si se solicita
    if (includeBreakdown) {
      response.breakdown = {
        searchType: 'simple',
        scoreThreshold: scoreThreshold,
        resultsCount: searchResults.length,
        averageScore: searchResults.length > 0 ?
          Math.round((searchResults.reduce((sum, r) => sum + r.score, 0) / searchResults.length) * 100) / 100 : 0,
        topScore: searchResults.length > 0 ? Math.round(searchResults[0].score * 100) / 100 : 0,
        processingTime: Date.now() - searchStartTime,
      };

      // Si hay breakdown en semanticResults, usarlo
      if (semanticResults && typeof semanticResults === 'object' && 'breakdown' in semanticResults) {
        response.breakdown = { ...response.breakdown, ...semanticResults.breakdown };
      }
    }

    // Agregar métricas de precisión avanzada si se solicita
    if (enableAdvancedPrecision) {
      response.precisionMetrics = {
        enabled: true,
        scoreDistribution: {
          high: searchResults.filter(r => r.score >= 0.8).length,
          medium: searchResults.filter(r => r.score >= 0.6 && r.score < 0.8).length,
          low: searchResults.filter(r => r.score < 0.6).length,
        },
        relevanceCategories: searchResults.map((result, index) => ({
          position: index + 1,
          score: Math.min(Math.round(result.score * 100), 100), // ✅ NORMALIZAR: Máximo 100%
          category: result.score >= 0.8 ? 'COINCIDENCIA EXACTA' :
                   result.score >= 0.6 ? 'COINCIDENCIA PARCIAL' :
                   result.score >= 0.4 ? 'ALTERNATIVA RELEVANTE' : 'BAJA RELEVANCIA',
          propertyId: result.payload.propertyId,
        })),
      };
    }

    // Generar respuesta con IA si se solicita
    if (includeResponse && searchResults.length > 0) {
      try {
        console.log('🤖 Generando respuesta de IA...');
        const aiResponse = await generateAIResponse(
          query,
          searchResults,
          responseLanguage,
          intentResult // Pasar información de intención detectada
        );
        response.aiResponse = aiResponse;
        console.log('✅ Respuesta de IA generada exitosamente');
      } catch (error) {
        console.error('❌ Error generating AI response:', error);
        console.error('Error details:', {
          message: error instanceof Error ? error.message : 'Unknown error',
          status: (error as any)?.status,
          code: (error as any)?.code,
        });
        response.aiResponse = {
          text: responseLanguage === 'es'
            ? `Encontré ${searchResults.length} propiedades que coinciden con tu búsqueda "${query}". Las propiedades están ordenadas por relevancia, siendo la primera la más recomendada.`
            : `I found ${searchResults.length} properties matching your search "${query}". Properties are ordered by relevance, with the first being the most recommended.`,
          error: 'Failed to generate detailed response',
        };
      }
    }

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error in search API:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Generar respuesta con IA
async function generateAIResponse(
  query: string,
  searchResults: any[],
  language: string = 'es',
  intentResult?: IntentDetectionResult
): Promise<{ text: string; summary: any }> {
  const topResults = searchResults.slice(0, 3); // Solo los 3 mejores resultados

  // ✅ NORMALIZAR scores para el prompt de IA (máximo 100%)
  const normalizedResults = topResults.map(result => ({
    ...result,
    normalizedScore: Math.min(Math.round(result.score * 100), 100)
  }));

  // Construir información de intención para el prompt
  const intentInfo = intentResult ? `
INTENCIÓN DETECTADA: ${intentResult.intent === 'buy' ? 'COMPRA/VENTA' : intentResult.intent === 'rent' ? 'ALQUILER' : 'AMBIGUA'}
Confianza: ${(intentResult.confidence * 100).toFixed(1)}%
Razonamiento: ${intentResult.reasoning}

IMPORTANTE: El usuario está buscando propiedades para ${intentResult.intent === 'buy' ? 'COMPRAR' : intentResult.intent === 'rent' ? 'ALQUILAR' : 'comprar o alquilar'}.
Enfoca tu respuesta EXCLUSIVAMENTE en esta intención. NO menciones la otra modalidad.
` : '';

  const systemPrompt = language === 'es' ? `
${intentInfo}
Eres un asistente inmobiliario experto que utiliza inteligencia artificial para analizar y recomendar propiedades. Tu respuesta debe ser transparente sobre tu proceso de recomendación Y explicar la relevancia de cada resultado.

ESTRUCTURA OBLIGATORIA DE RESPUESTA:

1. **ANÁLISIS DE RELEVANCIA INICIAL**:
   - Categoriza los resultados encontrados según su relevancia:
     🎯 95-100%: "COINCIDENCIA EXACTA" - Cumple perfectamente todos los criterios
     🔄 80-94%: "COINCIDENCIA PARCIAL" - Cumple la mayoría de criterios con diferencias menores
     💡 60-79%: "ALTERNATIVA RELEVANTE" - Opción interesante con diferencias notables
     ⚠️ <60%: "BAJA RELEVANCIA" - Mencionar limitaciones importantes

2. **RECOMENDACIÓN PRINCIPAL** (Primera propiedad):
   - Identifica explícitamente: "Mi recomendación principal es..."
   - Explica por qué tiene el score más alto: "porque tiene la mayor similitud con tus criterios de búsqueda"
   - Detalla sus características principales
   - Incluye score de relevancia: "Relevancia: X% (COINCIDENCIA EXACTA/PARCIAL/etc.)"

3. **OPCIONES ADICIONALES** (Propiedades 2, 3, etc.):
   - Justifica por qué las incluyes: "También te muestro estas opciones porque..."
   - Explica específicamente las diferencias: "Ubicada en Zona X (cerca de Zona Y solicitada)"
   - Incluye score y categoría para cada una
   - Razones válidas: "tienen características similares", "están en ubicaciones cercanas", "ofrecen alternativas de precio"

4. **TRANSPARENCIA DEL SISTEMA**:
   - Incluye frase: "Te presento múltiples opciones para que puedas comparar y elegir la que mejor se adapte a tus necesidades específicas"
   - Aclara que mostrar múltiples resultados es intencional, no un error
   - Si no hay coincidencias exactas, sugiere refinar la búsqueda

EXPLICACIÓN DE DIFERENCIAS OBLIGATORIA:
Para scores 80-94%, SIEMPRE explicar qué no coincide exactamente:
- "Ubicado en [Zona X] (cerca de [Zona Y] solicitada)"
- "Precio $[X] (ligeramente fuera de tu rango $[Y]-$[Z])"
- "[X] habitaciones (solicitaste [Y] habitaciones)"
- "Tipo [casa/apartamento] (buscabas [apartamento/casa])"

INSTRUCCIONES ADICIONALES:
- Responde en español de manera natural y conversacional
- NUNCA menciones IDs técnicos de propiedades (son irrelevantes para el usuario)
- Identifica propiedades por sus características: tipo, precio, ubicación, habitaciones
- Para cada propiedad, al final de su descripción, incluye SOLO UN enlace: [VER DETALLES](PROPERTY_ID_X) donde X es el número de la propiedad (1, 2, 3, etc.)
- Menciona precios, ubicaciones y características principales
- Si hay propiedades destacadas o premium, mencionalo
- Sé específico pero conciso
- Al final, invita al usuario a contactar para más información o agendar visitas
- Si los scores son bajos (<60%), sugiere refinar criterios de búsqueda
- SIEMPRE incluye el porcentaje de relevancia para cada propiedad
` : `
${intentInfo}
You are an expert real estate assistant that uses artificial intelligence to analyze and recommend properties. Your response must be transparent about your recommendation process.

MANDATORY RESPONSE STRUCTURE:

1. **MAIN RECOMMENDATION** (First property):
   - Explicitly identify: "My main recommendation is..." or "The property that best matches your search is..."
   - Explain why it has the highest score: "because it has the highest similarity to your search criteria" or "because it best matches what you're looking for"
   - Detail its main characteristics

2. **ADDITIONAL OPTIONS** (Properties 2, 3, etc.):
   - Justify why you include them: "I'm also showing you these options because..."
   - Valid reasons: "they have similar characteristics", "they're in nearby locations", "they offer price alternatives", "they have complementary amenities"

3. **SYSTEM TRANSPARENCY**:
   - Include a phrase like: "I present multiple options so you can compare and choose the one that best fits your specific needs"
   - Clarify that showing multiple results is intentional, not an error

ADDITIONAL INSTRUCTIONS:
- Respond in English naturally and conversationally
- NEVER mention technical property IDs (they are irrelevant to users)
- Identify properties by their characteristics: type, price, location, bedrooms
- For each property you mention, include a link using the format: [VIEW PROPERTY](PROPERTY_ID_X) where X is the property number (1, 2, 3, etc.)
- Mention prices, locations, and main characteristics
- If there are featured or premium properties, mention it
- Be specific but concise
- At the end, invite the user to contact for more information or schedule visits
- Offer additional help if appropriate
`;

  const userPrompt = `
Consulta del usuario: "${query}"

ANÁLISIS DE RELEVANCIA: Los resultados están ordenados por score de similitud semántica. Analiza y categoriza cada resultado según su relevancia.

Resultados encontrados (${searchResults.length} propiedades):
${normalizedResults.map((result: any, index: any) => {
  const score = result.normalizedScore; // ✅ USAR score normalizado (máximo 100%)
  const category = score >= 95 ? "EXACTA" : score >= 80 ? "PARCIAL" : score >= 60 ? "RELEVANTE" : "BAJA";

  return `
${index + 1}. ${index === 0 ? '🏆 RECOMENDACIÓN PRINCIPAL' : '📋 OPCIÓN ADICIONAL'}
   - ID de referencia: PROPERTY_ID_${index + 1} (usa este ID para crear enlaces)
   - Tipo: ${result.payload.type} en ${result.payload.status}
   - Precio: ${result.payload.currency} ${result.payload.price?.toLocaleString()}
   - Ubicación: ${[result.payload.level4, result.payload.level3, result.payload.level2].filter(Boolean).join(', ')}
   - Habitaciones: ${result.payload.bedrooms || 'N/A'} | Baños: ${result.payload.bathrooms || 'N/A'}
   - Área: ${result.payload.area} m²
   - Amenidades: ${result.payload.amenities?.join(', ') || 'No especificadas'}
   - 🎯 Score de relevancia: ${score}% (COINCIDENCIA ${category})
   - Destacada: ${result.payload.isFeatured ? 'Sí' : 'No'}
   - Premium: ${result.payload.isPremium ? 'Sí' : 'No'}
`;
}).join('')}

INSTRUCCIONES CRÍTICAS:
1. CATEGORIZA cada resultado según su score de relevancia usando las categorías definidas
2. EXPLICA específicamente por qué cada propiedad es relevante o qué diferencias tiene
3. Para scores <95%, explica qué criterios no coinciden exactamente con la búsqueda
4. Sugiere refinar búsqueda si no hay coincidencias exactas (scores >95%)
5. Sé transparente sobre el proceso de ranking y relevancia
6. INCLUYE el porcentaje de relevancia para cada propiedad en tu respuesta
7. TERMINA con una invitación clara a contactar para más información o agendar visitas

Proporciona una respuesta útil, transparente y personalizada basada en estos resultados.
`;

  console.log('🤖 Llamando a OpenAI con modelo gpt-3.5-turbo...');
  const completion = await openai.chat.completions.create({
    model: 'gpt-3.5-turbo', // Modelo más estable y disponible
    messages: [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userPrompt },
    ],
    max_tokens: 800, // Aumentado para respuestas más detalladas y estructuradas
    temperature: 0.7,
  });
  console.log('✅ Respuesta recibida de OpenAI');

  const aiText = completion.choices[0]?.message?.content || 
    (language === 'es' ? 'No pude generar una respuesta detallada.' : 'Could not generate a detailed response.');

  // Crear resumen estructurado
  const summary = {
    totalResults: searchResults.length,
    priceRange: {
      min: Math.min(...searchResults.map(r => r.payload.price || 0)),
      max: Math.max(...searchResults.map(r => r.payload.price || 0)),
    },
    types: [...new Set(searchResults.map(r => r.payload.type))],
    locations: [...new Set(searchResults.map(r => [r.payload.level4, r.payload.level3, r.payload.level2, r.payload.level1, r.payload.country].filter(Boolean).join(', ')).filter(Boolean))],
    featuredCount: searchResults.filter(r => r.payload.isFeatured).length,
    premiumCount: searchResults.filter(r => r.payload.isPremium).length,
    averageScore: Math.round((searchResults.reduce((sum, r) => sum + r.score, 0) / searchResults.length) * 100),
  };

  return {
    text: aiText,
    summary,
  };
}

// Endpoint GET para documentación
export async function GET() {
  return NextResponse.json({
    endpoint: '/api/v1/search',
    description: 'Semantic search for properties using natural language queries',
    method: 'POST',
    authentication: 'X-API-Key header required',
    parameters: {
      query: 'string (required) - Natural language search query',
      options: {
        limit: 'number (optional, default: 10) - Maximum results to return',
        includeResponse: 'boolean (optional, default: true) - Include AI-generated response',
        responseLanguage: 'string (optional, default: "es") - Response language (es/en)',
        scoreThreshold: 'number (optional, default: 0.25) - Minimum similarity score (optimized)',
        searchType: 'string (optional, default: "semantic") - Search type: semantic, hybrid, filter, simple',
        useSemanticWeights: 'boolean (optional, default: true) - Use semantic weights system for improved location precision',
        useSimpleSearch: 'boolean (optional, default: false) - Use simple embedding search (compatible with structured embeddings)',
        includeBreakdown: 'boolean (optional, default: false) - Include detailed score breakdown in response',
        enableAdvancedPrecision: 'boolean (optional, default: false) - Enable advanced semantic precision system (Day 7)',
        precisionOptions: 'object (optional) - Options for advanced precision system',
        filters: 'object (optional) - Additional filters for search',
      },
    },
    example: {
      query: 'casa moderna con jardín cerca del centro',
      options: {
        limit: 5,
        includeResponse: true,
        responseLanguage: 'es',
        filters: {
          priceRange: { min: 100000, max: 500000 },
          location: { level1: 'Guatemala' },
        },
      },
    },
  });
}
