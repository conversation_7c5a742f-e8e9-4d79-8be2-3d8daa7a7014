"use client";

import { useState, useCallback } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { useUser } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  ArrowLeft, 
  Upload, 
  MapPin, 
  Home, 
  DollarSign,
  Save,
  Eye,
  Info,
  AlertTriangle,
  Crown
} from "lucide-react";
import Link from "next/link";
import AmenitiesSection from "./_components/AmenitiesSection";
import { ImageUpload } from "@/components/ui/image-upload";
import { validatePropertyForm, type ValidationError, type PropertyFormData as ValidatedPropertyFormData } from "@/lib/validations";
import { toast } from "sonner";
import { RichTextarea } from "@/components/ui/rich-textarea";
import { LocationSelector } from "@/components/forms/LocationSelector";

interface LocationData {
  country: {
    code: string;
    name: string;
  };
  level1?: {
    code?: string;
    name: string;
    type: string;
  };
  level2?: {
    code?: string;
    name: string;
    type: string;
  };
  level3?: {
    code?: string;
    name: string;
    type: string;
  };
  level4?: {
    code?: string;
    name: string;
    type: string;
  };
}

interface PropertyFormData {
  title: string;
  description: string;
  price: number;
  currency: string;
  type: string;
  status: string;
  address: string;
  location?: LocationData;
  bedrooms: number;
  bathrooms: number;
  area: number;
  builtYear: number;
  parking: number;
  amenities: string[];
  images: string[];
  featured: boolean;
}

export default function NewPropertyPage() {
  const { user } = useUser();
  const router = useRouter();
  const createProperty = useMutation(api.properties.createProperty);

  const [isSubmitting, setIsSubmitting] = useState(false);
  // Estado separado para ubicación jerárquica (igual que en edición)
  const [locationData, setLocationData] = useState<any>({
    country: { code: "GT", name: "Guatemala" },
  });

  const [locationMetadata, setLocationMetadata] = useState({
    customKeywords: [] as string[],
    landmarks: [] as string[],
    accessibility: [] as string[],
    neighborhood: "",
  });
  const [formData, setFormData] = useState<PropertyFormData>({
    title: "",
    description: "",
    price: 0,
    currency: "GTQ",
    type: "",
    status: "for_sale",
    address: "",
    bedrooms: 1,
    bathrooms: 1,
    area: 0,
    builtYear: new Date().getFullYear(),
    parking: 0,
    amenities: [],
    images: [],
    featured: false,
  });

  // Verificar límites antes de mostrar el formulario
  const limitsCheck = useQuery(api.stripe.checkUsageLimits, {
    action: "create_property"
  });

  // Definir callbacks antes de cualquier return temprano
  const handleInputChange = useCallback((field: keyof PropertyFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  }, []);

  // Función separada para manejar cambios de ubicación (igual que en edición)
  const handleLocationChange = useCallback((location: any) => {
    setLocationData(location);
  }, []);

  // Si no está permitido crear propiedades, mostrar mensaje de upgrade
  if (limitsCheck && !limitsCheck.allowed) {
    return (
      <div className="max-w-2xl mx-auto p-4 md:p-6">
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-800">
              <AlertTriangle className="h-5 w-5" />
              Límite Alcanzado
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-700 mb-4">
              {limitsCheck.reason}
            </p>

            {limitsCheck.upgrade && (
              <div className="flex flex-col sm:flex-row gap-3">
                <Button
                  onClick={() => router.push("/dashboard/finance")}
                  className="bg-blue-600 hover:bg-blue-700 w-full sm:w-auto"
                >
                  <Crown className="h-4 w-4 mr-2" />
                  Ver Planes de Upgrade
                </Button>

                <Button
                  variant="outline"
                  onClick={() => router.push("/dashboard/properties")}
                  className="w-full sm:w-auto"
                >
                  Volver a Mis Propiedades
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  // Si está cargando, mostrar loading
  if (!limitsCheck) {
    return (
      <div className="max-w-2xl mx-auto p-4 md:p-6">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Verificando límites de tu plan...</p>
        </div>
      </div>
    );
  }

  const handleSubmit = async (e: React.FormEvent, saveAsDraft = false) => {
    e.preventDefault();
    
    if (!user?.id) {
      toast.error("Debes estar autenticado para crear una propiedad");
      return;
    }

    // Validar formulario
    const errors = validatePropertyForm(formData as ValidatedPropertyFormData, saveAsDraft);
    if (errors.length > 0) {
      // Mostrar errores usando toast en lugar de en el formulario
      errors.forEach(error => {
        toast.error(error.message);
      });
      return;
    }
    setIsSubmitting(true);

    try {
      // Las imágenes ya están procesadas en formData.images

      // Generar keywords de búsqueda automáticamente
      const generateLocationKeywords = (location: LocationData): string[] => {
        const keywords: string[] = [];
        if (location.country) keywords.push(location.country.name.toLowerCase());
        if (location.level1) keywords.push(location.level1.name.toLowerCase());
        if (location.level2) keywords.push(location.level2.name.toLowerCase());
        if (location.level3) keywords.push(location.level3.name.toLowerCase());
        if (location.level4) keywords.push(location.level4.name.toLowerCase());

        // Agregar keywords de la dirección
        if (formData.address) {
          const addressWords = formData.address.toLowerCase()
            .split(/[\s,.-]+/)
            .filter(word => word.length > 2);
          keywords.push(...addressWords);
        }

        return [...new Set(keywords)]; // Remover duplicados
      };

      const propertyData = {
        title: formData.title,
        description: formData.description,
        price: Number(formData.price),
        currency: formData.currency,
        type: formData.type as "house" | "apartment" | "office" | "land" | "commercial",
        status: (saveAsDraft ? "draft" : formData.status) as "for_sale" | "for_rent" | "sold" | "rented" | "draft",
        address: formData.address,
        location: locationData,
        locationMetadata: locationData ? {
          searchKeywords: [
            ...locationMetadata.customKeywords,
            ...generateLocationKeywords(locationData)
          ].filter((keyword, index, array) => array.indexOf(keyword) === index), // Eliminar duplicados
          landmarks: locationMetadata.landmarks,
          accessibility: locationMetadata.accessibility,
          neighborhood: locationMetadata.neighborhood || undefined,
        } : undefined,
        bedrooms: formData.bedrooms ? Number(formData.bedrooms) : undefined,
        bathrooms: formData.bathrooms ? Number(formData.bathrooms) : undefined,
        area: Number(formData.area),
        builtYear: formData.builtYear ? Number(formData.builtYear) : undefined,
        parking: formData.parking ? Number(formData.parking) : undefined,
        amenities: formData.amenities,
        images: formData.images,
        ownerId: user.id,
      };

      const propertyId = await createProperty(propertyData);

      if (saveAsDraft) {
        toast.success("¡Propiedad guardada como borrador!");
        router.push("/dashboard/properties");
      } else {
        toast.success("¡Propiedad publicada exitosamente!");
        router.push(`/properties/${propertyId}`);
      }
    } catch (error) {
      console.error("Error creating property:", error);
      toast.error("Error al crear la propiedad. Por favor intenta de nuevo.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex flex-col gap-6 p-6 max-w-4xl mx-auto">
      <div className="flex items-center gap-4">
        <Link href="/dashboard/properties" className="flex items-center text-blue-600 hover:text-blue-800">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Volver a Mis Propiedades
        </Link>
      </div>

      <div>
        <h1 className="text-3xl font-semibold tracking-tight">Nueva Propiedad</h1>
        <p className="text-muted-foreground mt-2">
          Completa la información de tu propiedad para publicarla en el marketplace.
        </p>
      </div>



      <form onSubmit={(e) => handleSubmit(e, false)} className="space-y-6" noValidate>
        {/* Información Básica */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Home className="h-5 w-5" />
              Información Básica
            </CardTitle>
            <CardDescription>
              Detalles principales de tu propiedad
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <Label htmlFor="title">Título de la Propiedad *</Label>
                <Input
                  id="title"
                  placeholder="Ej: Hermosa casa con jardín en Las Condes"
                  value={formData.title}
                  onChange={(e) => handleInputChange("title", e.target.value)}
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="type">Tipo de Propiedad *</Label>
                <Select onValueChange={(value) => handleInputChange("type", value)}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Selecciona el tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="house">Casa</SelectItem>
                    <SelectItem value="apartment">Apartamento</SelectItem>
                    <SelectItem value="office">Oficina</SelectItem>
                    <SelectItem value="land">Terreno</SelectItem>
                    <SelectItem value="commercial">Comercial</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="status">Estado de Publicación</Label>
                <Select 
                  defaultValue={formData.status}
                  onValueChange={(value) => handleInputChange("status", value)}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">Borrador</SelectItem>
                    <SelectItem value="for_sale">En Venta</SelectItem>
                    <SelectItem value="for_rent">En Alquiler</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="md:col-span-2">
                <Label htmlFor="description">Descripción *</Label>
                <RichTextarea
                  placeholder="Describe tu propiedad en detalle...

Ejemplo de formato:
• Amplia sala de estar
• Cocina moderna equipada
• 3 dormitorios principales

1. Primer piso: sala, cocina, baño
2. Segundo piso: 3 dormitorios
3. Jardín posterior con piscina"
                  value={formData.description}
                  onChange={(value) => handleInputChange("description", value)}
                  className="mt-1"
                  minHeight="160px"
                  maxLength={2000}
                />
                <div className="text-xs text-gray-500 mt-1">
                  {formData.description.length}/2000 caracteres
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Precio */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Precio
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="md:col-span-2">
                <Label htmlFor="price">Precio *</Label>
                <Input
                  id="price"
                  type="number"
                  placeholder="0"
                  value={formData.price || ""}
                  onChange={(e) => handleInputChange("price", Number(e.target.value))}
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="currency">Moneda</Label>
                <Select 
                  defaultValue={formData.currency}
                  onValueChange={(value) => handleInputChange("currency", value)}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="GTQ">GTQ (Quetzal)</SelectItem>
                    <SelectItem value="USD">USD (Dólar)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Ubicación */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Ubicación
            </CardTitle>
            <CardDescription>
              Especifica la ubicación geográfica de tu propiedad
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Dirección */}
            <div>
              <Label htmlFor="address">Dirección Completa *</Label>
              <Input
                id="address"
                placeholder="Ej: 15 Avenida 12-39, Zona 10"
                value={formData.address}
                onChange={(e) => handleInputChange("address", e.target.value)}
                className="mt-1"
              />
              <p className="text-xs text-gray-500 mt-1">
                Incluye número, calle, avenida y referencias específicas
              </p>
            </div>

            {/* Selector de Ubicación Jerárquica */}
            <LocationSelector
              value={locationData}
              onChange={handleLocationChange}
              errors={{}} // TODO: Agregar validación específica para ubicación
            />

            {/* Metadatos de Ubicación Editables */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-semibold text-blue-900 mb-4 flex items-center gap-2">
                <Info className="h-4 w-4" />
                Información Adicional de Ubicación
              </h4>
              <p className="text-sm text-blue-800 mb-4">
                Agrega información específica que ayude a los compradores a encontrar y entender mejor la ubicación de tu propiedad.
              </p>

              <div className="space-y-4">
                {/* Palabras Clave Personalizadas */}
                <div>
                  <Label htmlFor="customKeywords" className="text-sm font-medium text-blue-900">
                    Palabras Clave de Búsqueda
                  </Label>
                  <Input
                    id="customKeywords"
                    placeholder="Ej: centro comercial, universidad, metro (separadas por comas)"
                    value={locationMetadata.customKeywords.join(", ")}
                    onChange={(e) => setLocationMetadata(prev => ({
                      ...prev,
                      customKeywords: e.target.value.split(",").map(k => k.trim()).filter(k => k)
                    }))}
                    className="mt-1"
                  />
                  <p className="text-xs text-blue-700 mt-1">
                    Términos que la gente podría usar para buscar propiedades en esta área
                  </p>
                </div>

                {/* Puntos de Referencia */}
                <div>
                  <Label htmlFor="landmarks" className="text-sm font-medium text-blue-900">
                    Puntos de Referencia Cercanos
                  </Label>
                  <Input
                    id="landmarks"
                    placeholder="Ej: CC Pradera, Hospital Roosevelt, Torre del Reformador"
                    value={locationMetadata.landmarks.join(", ")}
                    onChange={(e) => {
                      const inputValue = e.target.value;
                      // Solo procesar si hay comas, sino mantener el texto tal como está
                      if (inputValue.includes(',')) {
                        setLocationMetadata(prev => ({
                          ...prev,
                          landmarks: inputValue.split(",").map(l => l.trim()).filter(l => l)
                        }));
                      } else {
                        // Si no hay comas, mantener como un solo elemento
                        setLocationMetadata(prev => ({
                          ...prev,
                          landmarks: inputValue ? [inputValue] : []
                        }));
                      }
                    }}
                    className="mt-1"
                  />
                  <p className="text-xs text-blue-700 mt-1">
                    Lugares conocidos, centros comerciales, hospitales, universidades
                  </p>
                </div>

                {/* Accesibilidad */}
                <div>
                  <Label htmlFor="accessibility" className="text-sm font-medium text-blue-900">
                    Transporte y Accesibilidad
                  </Label>
                  <Input
                    id="accessibility"
                    placeholder="Ej: Transmetro, parada de bus, carretera principal"
                    value={locationMetadata.accessibility.join(", ")}
                    onChange={(e) => {
                      const inputValue = e.target.value;
                      // Solo procesar si hay comas, sino mantener el texto tal como está
                      if (inputValue.includes(',')) {
                        setLocationMetadata(prev => ({
                          ...prev,
                          accessibility: inputValue.split(",").map(a => a.trim()).filter(a => a)
                        }));
                      } else {
                        // Si no hay comas, mantener como un solo elemento
                        setLocationMetadata(prev => ({
                          ...prev,
                          accessibility: inputValue ? [inputValue] : []
                        }));
                      }
                    }}
                    className="mt-1"
                  />
                  <p className="text-xs text-blue-700 mt-1">
                    Opciones de transporte público, vías de acceso principales
                  </p>
                </div>

                {/* Descripción del Vecindario */}
                <div>
                  <Label htmlFor="neighborhood" className="text-sm font-medium text-blue-900">
                    Descripción del Vecindario
                  </Label>
                  <Input
                    id="neighborhood"
                    placeholder="Ej: Zona residencial tranquila con colegios y comercios cercanos"
                    value={locationMetadata.neighborhood}
                    onChange={(e) => setLocationMetadata(prev => ({
                      ...prev,
                      neighborhood: e.target.value
                    }))}
                    className="mt-1"
                  />
                  <p className="text-xs text-blue-700 mt-1">
                    Características y ambiente del área donde está ubicada la propiedad
                  </p>
                </div>
              </div>
            </div>


          </CardContent>
        </Card>

        {/* Características */}
        <Card>
          <CardHeader>
            <CardTitle>Características</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="area">Área Total (m²) *</Label>
                <Input
                  id="area"
                  type="number"
                  placeholder="0"
                  value={formData.area || ""}
                  onChange={(e) => handleInputChange("area", Number(e.target.value))}
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="bedrooms">Habitaciones</Label>
                <Input
                  id="bedrooms"
                  type="number"
                  placeholder="0"
                  value={formData.bedrooms || ""}
                  onChange={(e) => handleInputChange("bedrooms", Number(e.target.value))}
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="bathrooms">Baños</Label>
                <Input
                  id="bathrooms"
                  type="number"
                  placeholder="0"
                  value={formData.bathrooms || ""}
                  onChange={(e) => handleInputChange("bathrooms", Number(e.target.value))}
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="parking">Estacionamientos</Label>
                <Input
                  id="parking"
                  type="number"
                  placeholder="0"
                  value={formData.parking || ""}
                  onChange={(e) => handleInputChange("parking", Number(e.target.value))}
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="builtYear">Año de Construcción</Label>
                <Input
                  id="builtYear"
                  type="number"
                  placeholder="2024"
                  value={formData.builtYear || ""}
                  onChange={(e) => handleInputChange("builtYear", Number(e.target.value))}
                  className="mt-1"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Amenidades */}
        <AmenitiesSection 
          selectedAmenities={formData.amenities}
          onAmenitiesChange={(amenities) => handleInputChange("amenities", amenities)}
        />

        {/* Imágenes */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Imágenes
            </CardTitle>
            <CardDescription>
              Sube imágenes de tu propiedad (máximo 10)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
              <h4 className="font-medium text-blue-900 mb-2">💡 Consejos para mejores fotos:</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Usa buena iluminación natural</li>
                <li>• Incluye fotos del exterior, interior y detalles</li>
                <li>• Evita objetos personales en las fotos</li>
                <li>• La primera imagen será la principal</li>
              </ul>
            </div>
            <ImageUpload
              images={formData.images}
              onImagesChange={(images) => handleInputChange("images", images)}
              maxImages={10}
            />
          </CardContent>
        </Card>

        {/* Botones de Acción */}
        <div className="flex gap-4 justify-end">
          <Button
            type="button"
            variant="outline"
            onClick={(e) => handleSubmit(e as any, true)}
            disabled={isSubmitting}
          >
            <Save className="h-4 w-4 mr-2" />
            Guardar Borrador
          </Button>
          
          <Button
            type="submit"
            disabled={isSubmitting}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Eye className="h-4 w-4 mr-2" />
            {isSubmitting ? "Publicando..." : "Publicar Propiedad"}
          </Button>
        </div>
      </form>
    </div>
  );
} 