import { NextRequest, NextResponse } from "next/server";
import { auth, currentUser } from "@clerk/nextjs/server";
import { stripe } from "@/lib/stripe";
import { STRIPE_CONFIG } from "@/lib/stripe-config";

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: "No autorizado" }, { status: 401 });
    }

    if (!stripe) {
      return NextResponse.json({
        error: "Stripe no está configurado. Contacta al administrador."
      }, { status: 500 });
    }

    const { plan } = await request.json();

    // Obtener información del usuario desde Clerk
    const user = await currentUser();

    if (!user || !user.emailAddresses?.[0]?.emailAddress) {
      return NextResponse.json({
        error: "No se pudo obtener el email del usuario"
      }, { status: 400 });
    }

    const userEmail = user.emailAddresses[0].emailAddress;

    // Validar plan
    if (!["pro", "premium"].includes(plan)) {
      return NextResponse.json({ error: "Plan no válido" }, { status: 400 });
    }

    const product = STRIPE_CONFIG.products[plan as keyof typeof STRIPE_CONFIG.products];

    if (!product.priceId || product.priceId.includes("_dev")) {
      return NextResponse.json({ 
        error: `El precio para ${plan} no está configurado. Necesitas crear un Price en Stripe y configurar STRIPE_PRICE_${plan.toUpperCase()} en las variables de entorno.`,
      }, { status: 400 });
    }

    // Crear sesión de checkout
    const session = await stripe.checkout.sessions.create({
      mode: "subscription",
      payment_method_types: ["card"],
      line_items: [
        {
          price: product.priceId,
          quantity: 1,
        },
      ],
      // ✅ PRE-LLENAR EMAIL DEL USUARIO
      customer_email: userEmail,
      subscription_data: {
        // ❌ REMOVIDO: trial_period_days (manejado por nuestro sistema interno)
        metadata: {
          userId: userId,
          plan: plan,
          userEmail: userEmail, // ✅ Incluir email en metadata también
        },
      },
      success_url: STRIPE_CONFIG.urls.success,
      cancel_url: STRIPE_CONFIG.urls.cancel,
      allow_promotion_codes: true,
      billing_address_collection: "required",
      locale: "es", // Español por defecto
      metadata: {
        userId: userId,
        plan: plan,
        userEmail: userEmail, // ✅ Incluir email en metadata también
      },
    });

    console.log("✅ Sesión de Stripe creada:", session.id);

    return NextResponse.json({ 
      checkoutUrl: session.url,
      sessionId: session.id,
      plan,
      price: product.price,
      currency: product.currency
    });

  } catch (error) {
    console.error("❌ Error creando checkout de Stripe:", error);
    return NextResponse.json(
      { 
        error: "Error interno del servidor",
        details: error instanceof Error ? error.message : "Error desconocido"
      }, 
      { status: 500 }
    );
  }
} 