"use client";

import { useState } from "react";
import { useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircle, AlertCircle, Loader2 } from "lucide-react";

export default function InitCreditsPage() {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const initializeCreditActions = useMutation(api.subscriptions.initializeCreditActions);
  const addMissingCreditActions = useMutation(api.subscriptions.addMissingCreditActions);

  const handleInitialize = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log("Inicializando configuraciones de créditos...");
      const result = await initializeCreditActions({});
      setResult(result);
      console.log("✅ Configuraciones inicializadas:", result);
    } catch (err: any) {
      console.error("❌ Error inicializando:", err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleAddMissing = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log("Agregando configuraciones faltantes...");
      const result = await addMissingCreditActions({});
      setResult(result);
      console.log("✅ Configuraciones agregadas:", result);
    } catch (err: any) {
      console.error("❌ Error agregando:", err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Inicializar Configuraciones de Créditos</h1>
        <p className="text-gray-600 mt-2">
          Esta página permite inicializar las configuraciones de créditos necesarias para el sistema.
        </p>
      </div>

      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Configuraciones de Créditos</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-4">
              <Button 
                onClick={handleInitialize}
                disabled={loading}
                className="flex items-center gap-2"
              >
                {loading ? <Loader2 className="w-4 h-4 animate-spin" /> : <CheckCircle className="w-4 h-4" />}
                Inicializar Configuraciones
              </Button>
              
              <Button 
                onClick={handleAddMissing}
                disabled={loading}
                variant="outline"
                className="flex items-center gap-2"
              >
                {loading ? <Loader2 className="w-4 h-4 animate-spin" /> : <CheckCircle className="w-4 h-4" />}
                Agregar Faltantes
              </Button>
            </div>

            {error && (
              <Alert className="border-red-200 bg-red-50">
                <AlertCircle className="h-4 w-4 text-red-600" />
                <AlertDescription className="text-red-800">
                  <strong>Error:</strong> {error}
                </AlertDescription>
              </Alert>
            )}

            {result && (
              <Alert className="border-green-200 bg-green-50">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <AlertDescription className="text-green-800">
                  <strong>Éxito:</strong> {result.message}
                  {result.count && <div>Configuraciones creadas: {result.count}</div>}
                  {result.inserted && <div>Configuraciones insertadas: {result.inserted}</div>}
                  {result.existing && (
                    <div>Configuraciones existentes: {result.existing.join(", ")}</div>
                  )}
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Configuraciones Requeridas</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-sm text-gray-600 space-y-2">
              <p><strong>respond_to_message:</strong> 2 créditos - Responder a mensaje</p>
              <p><strong>respond_to_appointment:</strong> 3 créditos - Responder a solicitud de cita</p>
              <p><strong>featured_property:</strong> 10 créditos - Destacar propiedad</p>
              <p><strong>premium_home:</strong> 25 créditos - Posición premium</p>
              <p><strong>message_inquiry:</strong> 1 crédito - Consulta general</p>
              <p><strong>message_viewing:</strong> 3 créditos - Solicitud de visita</p>
              <p><strong>message_offer:</strong> 5 créditos - Oferta de compra</p>
              <p><strong>message_negotiation:</strong> 2 créditos - Negociación</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
