/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as admin from "../admin.js";
import type * as agentImprovement from "../agentImprovement.js";
import type * as aiAppointments from "../aiAppointments.js";
import type * as amenities from "../amenities.js";
import type * as appointments from "../appointments.js";
import type * as config_models from "../config/models.js";
import type * as conversations from "../conversations.js";
import type * as conversations_schema from "../conversations_schema.js";
import type * as creditActions from "../creditActions.js";
import type * as crons from "../crons.js";
import type * as emails from "../emails.js";
import type * as featuredProperties from "../featuredProperties.js";
import type * as http from "../http.js";
import type * as knowledgeBase from "../knowledgeBase.js";
import type * as locationConfig from "../locationConfig.js";
import type * as locationData from "../locationData.js";
import type * as messages from "../messages.js";
import type * as properties from "../properties.js";
import type * as rag from "../rag.js";
import type * as seed from "../seed.js";
import type * as stripe from "../stripe.js";
import type * as subscriptions from "../subscriptions.js";
import type * as systemAdmin from "../systemAdmin.js";
import type * as systemMessages from "../systemMessages.js";
import type * as transactions from "../transactions.js";
import type * as users from "../users.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  admin: typeof admin;
  agentImprovement: typeof agentImprovement;
  aiAppointments: typeof aiAppointments;
  amenities: typeof amenities;
  appointments: typeof appointments;
  "config/models": typeof config_models;
  conversations: typeof conversations;
  conversations_schema: typeof conversations_schema;
  creditActions: typeof creditActions;
  crons: typeof crons;
  emails: typeof emails;
  featuredProperties: typeof featuredProperties;
  http: typeof http;
  knowledgeBase: typeof knowledgeBase;
  locationConfig: typeof locationConfig;
  locationData: typeof locationData;
  messages: typeof messages;
  properties: typeof properties;
  rag: typeof rag;
  seed: typeof seed;
  stripe: typeof stripe;
  subscriptions: typeof subscriptions;
  systemAdmin: typeof systemAdmin;
  systemMessages: typeof systemMessages;
  transactions: typeof transactions;
  users: typeof users;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
