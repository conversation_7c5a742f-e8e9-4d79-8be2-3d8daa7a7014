// Script para inicializar configuraciones de créditos
// Ejecutar con: node scripts/init-credit-actions.js

const { ConvexHttpClient } = require("convex/browser");

const client = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL);

async function initializeCreditActions() {
  try {
    console.log("🚀 Inicializando configuraciones de créditos...");
    
    // Llamar a la función de inicialización
    const result = await client.mutation("subscriptions:initializeCreditActions", {});
    
    console.log("✅ Resultado:", result);
    
  } catch (error) {
    console.error("❌ Error inicializando configuraciones:", error);
    
    // Intentar con la función alternativa
    try {
      console.log("🔄 Intentando con función alternativa...");
      const result2 = await client.mutation("subscriptions:addMissingCreditActions", {});
      console.log("✅ Resultado alternativo:", result2);
    } catch (error2) {
      console.error("❌ Error con función alternativa:", error2);
    }
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  initializeCreditActions();
}

module.exports = { initializeCreditActions };
