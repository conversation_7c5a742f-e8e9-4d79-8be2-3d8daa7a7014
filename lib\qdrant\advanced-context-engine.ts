/**
 * Advanced Context Engine - Motor de Contexto Avanzado
 * 
 * Sistema integrado que combina análisis de contexto, desambiguación,
 * memoria conversacional y manejo de casos edge guatemaltecos.
 * 
 * Día 6 - Análisis de Contexto Avanzado
 */

import { ContextAnalyzer, ContextEnhancedQuery } from './context-analyzer';
import { QueryDisambiguator, DisambiguationResult } from './query-disambiguator';
import { ConversationalMemory, ContextualQuery } from './conversational-memory';
import { GuatemalaEdgeCasesHandler, EdgeCaseResult } from './guatemala-edge-cases';

// Tipos para el motor integrado
export interface AdvancedContextResult {
  originalQuery: string;
  processedQuery: string;
  finalQuery: string;
  
  contextAnalysis: ContextEnhancedQuery;
  disambiguation: DisambiguationResult;
  conversationalContext: ContextualQuery;
  edgeCaseHandling: EdgeCaseResult;
  
  appliedEnhancements: {
    contextEnrichment: boolean;
    disambiguation: boolean;
    conversationalMemory: boolean;
    edgeCaseCorrection: boolean;
  };
  
  suggestions: string[];
  warnings: string[];
  confidence: number;
  processingTime: number;
}

export interface ContextEngineConfig {
  enableContextAnalysis: boolean;
  enableDisambiguation: boolean;
  enableConversationalMemory: boolean;
  enableEdgeCaseHandling: boolean;
  sessionId?: string;
  userId?: string;
}

/**
 * Motor de contexto avanzado integrado
 */
export class AdvancedContextEngine {
  private contextAnalyzer: ContextAnalyzer;
  private queryDisambiguator: QueryDisambiguator;
  private conversationalMemory: ConversationalMemory;
  private edgeCaseHandler: GuatemalaEdgeCasesHandler;

  constructor() {
    this.contextAnalyzer = new ContextAnalyzer();
    this.queryDisambiguator = new QueryDisambiguator();
    this.conversationalMemory = new ConversationalMemory();
    this.edgeCaseHandler = new GuatemalaEdgeCasesHandler();
  }

  /**
   * Procesa una consulta con contexto avanzado completo
   */
  async processQuery(
    query: string,
    config: ContextEngineConfig = this.getDefaultConfig()
  ): Promise<AdvancedContextResult> {
    const startTime = Date.now();

    try {
      console.log(`🧠 Procesando consulta con contexto avanzado: "${query}"`);

      let processedQuery = query;
      const suggestions: string[] = [];
      const warnings: string[] = [];
      const appliedEnhancements = {
        contextEnrichment: false,
        disambiguation: false,
        conversationalMemory: false,
        edgeCaseCorrection: false,
      };

      // FASE 1: Manejo de casos edge guatemaltecos
      let edgeCaseHandling: EdgeCaseResult = { hasEdgeCase: false, edgeCases: [], correctedQuery: query, warnings: [], suggestions: [] };
      if (config.enableEdgeCaseHandling) {
        edgeCaseHandling = this.edgeCaseHandler.analyzeGuatemalaEdgeCases(processedQuery);
        if (edgeCaseHandling.hasEdgeCase) {
          processedQuery = edgeCaseHandling.correctedQuery;
          warnings.push(...edgeCaseHandling.warnings);
          suggestions.push(...edgeCaseHandling.suggestions);
          appliedEnhancements.edgeCaseCorrection = true;
          console.log(`🇬🇹 Casos edge corregidos: "${processedQuery}"`);
        }
      }

      // FASE 2: Análisis de contexto
      let contextAnalysis: ContextEnhancedQuery = {
        originalQuery: processedQuery,
        enhancedQuery: processedQuery,
        contextAnalysis: {} as any,
        suggestedFilters: {},
        confidence: 0.5,
        processingTime: 0,
      };
      if (config.enableContextAnalysis) {
        contextAnalysis = await this.contextAnalyzer.analyzeContext(processedQuery);
        if (contextAnalysis.enhancedQuery !== processedQuery) {
          processedQuery = contextAnalysis.enhancedQuery;
          appliedEnhancements.contextEnrichment = true;
          console.log(`🔍 Contexto aplicado: "${processedQuery}"`);
        }
      }

      // FASE 3: Desambiguación
      let disambiguation: DisambiguationResult = {
        originalQuery: processedQuery,
        isAmbiguous: false,
        ambiguityLevel: 'low',
        interpretations: [],
        recommendedInterpretation: {} as any,
        fallbackQuery: processedQuery,
        processingTime: 0,
      };
      if (config.enableDisambiguation) {
        disambiguation = await this.queryDisambiguator.disambiguateQuery(
          processedQuery,
          contextAnalysis.contextAnalysis
        );
        if (disambiguation.isAmbiguous) {
          processedQuery = disambiguation.recommendedInterpretation.interpretation;
          appliedEnhancements.disambiguation = true;
          console.log(`🔀 Desambiguación aplicada: "${processedQuery}"`);
          
          // Agregar sugerencias de interpretaciones alternativas
          disambiguation.interpretations.slice(1, 3).forEach(interp => {
            suggestions.push(`Alternativa: ${interp.interpretation} (${(interp.probability * 100).toFixed(0)}%)`);
          });
        }
      }

      // FASE 4: Memoria conversacional
      let conversationalContext: ContextualQuery = {
        originalQuery: processedQuery,
        enrichedQuery: processedQuery,
        appliedContext: { fromHistory: false, fromPreferences: false, fromFlow: false },
        confidence: 0.5,
      };
      if (config.enableConversationalMemory && config.sessionId) {
        conversationalContext = this.conversationalMemory.enrichQueryWithContext(
          config.sessionId,
          processedQuery,
          contextAnalysis.contextAnalysis,
          disambiguation
        );
        if (conversationalContext.enrichedQuery !== processedQuery) {
          processedQuery = conversationalContext.enrichedQuery;
          appliedEnhancements.conversationalMemory = true;
          console.log(`💭 Memoria conversacional aplicada: "${processedQuery}"`);
        }

        // Agregar sugerencias de refinamiento
        const refinements = this.conversationalMemory.suggestRefinements(config.sessionId);
        suggestions.push(...refinements.slice(0, 3));
      }

      // FASE 5: Calcular confianza final
      const confidence = this.calculateFinalConfidence(
        contextAnalysis,
        disambiguation,
        conversationalContext,
        edgeCaseHandling
      );

      const result: AdvancedContextResult = {
        originalQuery: query,
        processedQuery: query,
        finalQuery: processedQuery,
        contextAnalysis,
        disambiguation,
        conversationalContext,
        edgeCaseHandling,
        appliedEnhancements,
        suggestions: [...new Set(suggestions)], // Remover duplicados
        warnings: [...new Set(warnings)], // Remover duplicados
        confidence,
        processingTime: Date.now() - startTime,
      };

      console.log(`✅ Contexto avanzado procesado en ${result.processingTime}ms (confianza: ${confidence.toFixed(2)})`);
      console.log(`🔧 Mejoras aplicadas: ${Object.values(appliedEnhancements).filter(Boolean).length}/4`);

      return result;

    } catch (error) {
      console.error('Error en motor de contexto avanzado:', error);
      return this.createFallbackResult(query, Date.now() - startTime);
    }
  }

  /**
   * Registra feedback del usuario para mejorar el aprendizaje
   */
  recordFeedback(
    sessionId: string,
    feedback: 'positive' | 'negative' | 'neutral',
    resultsCount: number
  ): void {
    if (!sessionId) return;

    // Obtener la última entrada de la sesión
    const session = this.conversationalMemory.getOrCreateSession(sessionId);
    if (session.entries.length > 0) {
      const lastEntry = session.entries[session.entries.length - 1];
      this.conversationalMemory.recordUserFeedback(sessionId, lastEntry.id, feedback, resultsCount);
    }
  }

  /**
   * Obtiene estadísticas de la sesión
   */
  getSessionStats(sessionId: string) {
    return this.conversationalMemory.getSessionStats(sessionId);
  }

  /**
   * Obtiene información específica de zona guatemalteca
   */
  getZoneInfo(zone: string) {
    return this.edgeCaseHandler.getZoneInfo(zone);
  }

  /**
   * Calcula confianza final del procesamiento
   */
  private calculateFinalConfidence(
    contextAnalysis: ContextEnhancedQuery,
    disambiguation: DisambiguationResult,
    conversationalContext: ContextualQuery,
    edgeCaseHandling: EdgeCaseResult
  ): number {
    let confidence = 0.5; // Base

    // Contribución del análisis de contexto (30%)
    confidence += (contextAnalysis.confidence * 0.3);

    // Contribución de la desambiguación (25%)
    if (disambiguation.isAmbiguous) {
      confidence += (disambiguation.recommendedInterpretation.confidence * 0.25);
    } else {
      confidence += 0.25; // Bonus por no ser ambigua
    }

    // Contribución de la memoria conversacional (25%)
    confidence += (conversationalContext.confidence * 0.25);

    // Contribución del manejo de casos edge (20%)
    if (edgeCaseHandling.hasEdgeCase) {
      const avgEdgeCaseConfidence = edgeCaseHandling.edgeCases.reduce(
        (sum, ec) => sum + ec.confidence, 0
      ) / edgeCaseHandling.edgeCases.length;
      confidence += (avgEdgeCaseConfidence * 0.2);
    } else {
      confidence += 0.2; // Bonus por no tener casos edge
    }

    return Math.min(1.0, confidence);
  }

  /**
   * Crea resultado de fallback
   */
  private createFallbackResult(query: string, processingTime: number): AdvancedContextResult {
    return {
      originalQuery: query,
      processedQuery: query,
      finalQuery: query,
      contextAnalysis: {
        originalQuery: query,
        enhancedQuery: query,
        contextAnalysis: {} as any,
        suggestedFilters: {},
        confidence: 0.1,
        processingTime: 0,
      },
      disambiguation: {
        originalQuery: query,
        isAmbiguous: false,
        ambiguityLevel: 'low',
        interpretations: [],
        recommendedInterpretation: {} as any,
        fallbackQuery: query,
        processingTime: 0,
      },
      conversationalContext: {
        originalQuery: query,
        enrichedQuery: query,
        appliedContext: { fromHistory: false, fromPreferences: false, fromFlow: false },
        confidence: 0.1,
      },
      edgeCaseHandling: {
        hasEdgeCase: false,
        edgeCases: [],
        correctedQuery: query,
        warnings: [],
        suggestions: [],
      },
      appliedEnhancements: {
        contextEnrichment: false,
        disambiguation: false,
        conversationalMemory: false,
        edgeCaseCorrection: false,
      },
      suggestions: [],
      warnings: ['Error en procesamiento de contexto avanzado'],
      confidence: 0.1,
      processingTime,
    };
  }

  /**
   * Obtiene configuración por defecto
   */
  private getDefaultConfig(): ContextEngineConfig {
    return {
      enableContextAnalysis: true,
      enableDisambiguation: true,
      enableConversationalMemory: true,
      enableEdgeCaseHandling: true,
    };
  }

  /**
   * Destructor para limpiar recursos
   */
  destroy(): void {
    this.conversationalMemory.destroy();
  }
}
