"use client";

import { useState, useEffect } from "react";
import { useUser } from "@clerk/nextjs";
import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import {
  ArrowLeft,
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  MapPin,
  AlertTriangle,
  Loader2,
  ChevronRight,
  ChevronDown,
  Info,
  HelpCircle
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  <PERSON>alog<PERSON><PERSON>cription,
  Di<PERSON><PERSON>eader,
  <PERSON><PERSON>Tit<PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@/components/ui/alert";

interface LocationItem {
  _id?: string;
  name: string;
  code?: string;
  level: number;
  parentId?: string;
  countryCode: string;
  children?: LocationItem[];
}

interface AddItemState {
  show: boolean;
  level: number;
  parentId?: string;
  parentName?: string;
}

export default function ManageLocationDataPage() {
  const { user } = useUser();
  const router = useRouter();
  const params = useParams();
  const countryCode = params.countryCode as string;

  // Estados simplificados
  const [editingItem, setEditingItem] = useState<LocationItem | null>(null);
  const [addItemState, setAddItemState] = useState<AddItemState>({
    show: false,
    level: 1
  });
  const [newItem, setNewItem] = useState<LocationItem>({
    name: "",
    code: "",
    level: 1,
    countryCode: countryCode.toUpperCase()
  });
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  // Queries
  const currentUser = useQuery(api.users.getCurrentUser);
  const countryConfig = useQuery(api.locationConfig.getCountryConfig, {
    countryCode: countryCode.toUpperCase()
  });

  // Obtener todos los departamentos (nivel 1)
  const departments = useQuery(api.locationData.getRootLevels, {
    countryCode: countryCode.toUpperCase(),
    level: 1
  });

  // Mutations
  const addLocationData = useMutation(api.locationData.addLocationData);
  const updateLocationData = useMutation(api.locationData.updateLocationData);
  const deleteLocationData = useMutation(api.locationData.deleteLocationData);

  // Funciones auxiliares
  const toggleExpanded = (itemId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  const openAddDialog = (level: number, parentId?: string, parentName?: string) => {
    setAddItemState({
      show: true,
      level,
      parentId,
      parentName
    });
    setNewItem({
      name: "",
      code: "",
      level,
      countryCode: countryCode.toUpperCase()
    });
  };

  const closeAddDialog = () => {
    setAddItemState({ show: false, level: 1 });
    setNewItem({
      name: "",
      code: "",
      level: 1,
      countryCode: countryCode.toUpperCase()
    });
  };

  // Verificar permisos
  if (!currentUser || currentUser.role !== 'admin') {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="max-w-md mx-auto text-center bg-white rounded-lg shadow-lg p-8">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertTriangle className="h-8 w-8 text-red-600" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Acceso Denegado</h1>
          <p className="text-gray-600 mb-6">
            No tienes permisos para administrar datos de ubicación.
          </p>
        </div>
      </div>
    );
  }

  if (!countryConfig) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="max-w-md mx-auto text-center bg-white rounded-lg shadow-lg p-8">
          <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <MapPin className="h-8 w-8 text-yellow-600" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">País No Configurado</h1>
          <p className="text-gray-600 mb-6">
            El país {countryCode.toUpperCase()} no está configurado en el sistema.
          </p>
          <Button onClick={() => router.push("/dashboard/admin/location-config")}>
            Volver a Configuración
          </Button>
        </div>
      </div>
    );
  }

  const handleAddItem = async () => {
    try {
      const levelName = getLevelName(addItemState.level);

      await addLocationData({
        ...newItem,
        level: addItemState.level,
        parentId: addItemState.parentId as Id<"locationData"> | undefined,
        countryCode: countryCode.toUpperCase()
      });

      toast.success(`${levelName} "${newItem.name}" agregado exitosamente`);
      closeAddDialog();
    } catch (error: any) {
      toast.error(`Error: ${error.message}`);
    }
  };

  const handleUpdateItem = async (item: LocationItem) => {
    try {
      if (!item._id) return;

      await updateLocationData({
        id: item._id as Id<"locationData">,
        name: item.name,
        code: item.code
      });

      const levelName = getLevelName(item.level);
      toast.success(`${levelName} "${item.name}" actualizado exitosamente`);
      setEditingItem(null);
    } catch (error: any) {
      toast.error(`Error: ${error.message}`);
    }
  };

  const handleDeleteItem = async (item: LocationItem) => {
    try {
      if (!item._id) return;

      await deleteLocationData({ id: item._id as Id<"locationData"> });
      const levelName = getLevelName(item.level);
      toast.success(`${levelName} "${item.name}" eliminado exitosamente`);
    } catch (error: any) {
      toast.error(`Error: ${error.message}`);
    }
  };

  const getLevelName = (level: number) => {
    if (!countryConfig) return `Nivel ${level}`;
    const levelKey = `level${level}` as keyof typeof countryConfig.hierarchy;
    return countryConfig.hierarchy[levelKey]?.name || `Nivel ${level}`;
  };

  const getLevelConfig = (level: number) => {
    if (!countryConfig) return null;
    const levelKey = `level${level}` as keyof typeof countryConfig.hierarchy;
    return countryConfig.hierarchy[levelKey];
  };

  const maxLevels = countryConfig ? Object.keys(countryConfig.hierarchy).length : 4;

  // Componente para renderizar cada elemento de la jerarquía
  const LocationTreeItem = ({ item, level }: { item: LocationItem; level: number }) => {
    const isExpanded = expandedItems.has(item._id || '');
    const isEditing = editingItem?._id === item._id;
    const levelConfig = getLevelConfig(level);
    const hasChildren = level < maxLevels;

    // Query para obtener hijos si está expandido
    const children = useQuery(
      api.locationData.getChildrenByParent,
      isExpanded && hasChildren && item._id
        ? { parentId: item._id as Id<"locationData">, countryCode: countryCode.toUpperCase() }
        : "skip"
    );

    return (
      <div className="border rounded-lg">
        <div className="flex items-center justify-between p-3 hover:bg-gray-50">
          {isEditing ? (
            <div className="flex-1 flex items-center gap-3">
              <Input
                value={editingItem?.name || ""}
                onChange={(e) => setEditingItem(prev => prev ? { ...prev, name: e.target.value } : null)}
                className="flex-1"
                placeholder={`Nombre del ${getLevelName(level).toLowerCase()}`}
              />
              {levelConfig?.hasCode && (
                <Input
                  value={editingItem?.code || ""}
                  onChange={(e) => setEditingItem(prev => prev ? { ...prev, code: e.target.value } : null)}
                  placeholder="Código"
                  className="w-24"
                />
              )}
              <div className="flex gap-2">
                <Button size="sm" onClick={() => editingItem && handleUpdateItem(editingItem)}>
                  <Save className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="outline" onClick={() => setEditingItem(null)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ) : (
            <>
              <div className="flex items-center gap-3 flex-1">
                {hasChildren && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => item._id && toggleExpanded(item._id)}
                    className="p-1 h-6 w-6"
                  >
                    {isExpanded ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                  </Button>
                )}
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <p className="font-medium">{item.name}</p>
                    <Badge variant="outline" className="text-xs">
                      {getLevelName(level)}
                    </Badge>
                  </div>
                  {item.code && (
                    <p className="text-sm text-gray-500">Código: {item.code}</p>
                  )}
                </div>
              </div>

              <div className="flex gap-2">
                {hasChildren && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => openAddDialog(level + 1, item._id, item.name)}
                    className="text-green-600 hover:text-green-700"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Agregar {getLevelName(level + 1)}
                  </Button>
                )}
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setEditingItem(item)}
                >
                  <Edit className="h-4 w-4" />
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleDeleteItem(item)}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </>
          )}
        </div>

        {/* Hijos expandidos */}
        {isExpanded && hasChildren && children && children.length > 0 && (
          <div className="ml-6 border-l-2 border-gray-200 pl-4 pb-2">
            {children.map((child: any) => (
              <LocationTreeItem key={child._id} item={child} level={level + 1} />
            ))}
          </div>
        )}

        {/* Mensaje si no hay hijos */}
        {isExpanded && hasChildren && children && children.length === 0 && (
          <div className="ml-6 border-l-2 border-gray-200 pl-4 pb-2">
            <div className="text-center py-4 text-gray-500">
              <p className="text-sm">No hay {getLevelName(level + 1).toLowerCase()}s registrados</p>
              <Button
                size="sm"
                variant="outline"
                onClick={() => openAddDialog(level + 1, item._id, item.name)}
                className="mt-2 text-green-600 hover:text-green-700"
              >
                <Plus className="h-4 w-4 mr-1" />
                Agregar primer {getLevelName(level + 1).toLowerCase()}
              </Button>
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="flex flex-col gap-6 p-4 md:p-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          onClick={() => router.push("/dashboard/admin/location-config")}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Volver
        </Button>
        <div>
          <h1 className="text-2xl md:text-3xl font-semibold tracking-tight flex items-center gap-2">
            <MapPin className="h-6 w-6 md:h-8 md:w-8" />
            Administrar {countryConfig?.countryName || countryCode.toUpperCase()}
          </h1>
          <p className="text-muted-foreground mt-2 text-sm md:text-base">
            Gestiona los datos geográficos de manera jerárquica
          </p>
        </div>
      </div>

      {/* Guía de uso */}
      <Alert>
        <HelpCircle className="h-4 w-4" />
        <AlertTitle>Cómo usar esta interfaz</AlertTitle>
        <AlertDescription>
          <ul className="list-disc list-inside space-y-1 mt-2 text-sm">
            <li><strong>Expandir/Contraer:</strong> Haz clic en las flechas para ver elementos hijos</li>
            <li><strong>Agregar elementos:</strong> Usa los botones &quot;Agregar [Nivel]&quot; para crear nuevos elementos</li>
            <li><strong>Jerarquía automática:</strong> Los elementos se organizan automáticamente por niveles</li>
            <li><strong>Edición directa:</strong> Haz clic en &quot;Editar&quot; para modificar nombres y códigos</li>
          </ul>
        </AlertDescription>
      </Alert>

      {/* Estructura Jerárquica */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Estructura Geográfica de {countryConfig?.countryName || countryCode.toUpperCase()}
              </CardTitle>
              <CardDescription>
                Vista jerárquica de todos los niveles geográficos. Expande los elementos para ver y administrar sus hijos.
              </CardDescription>
            </div>
            <Button
              onClick={() => openAddDialog(1)}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Agregar {getLevelName(1)}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {departments && departments.length > 0 ? (
            <div className="space-y-3">
              {departments.map((department: any) => (
                <LocationTreeItem key={department._id} item={department} level={1} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12 text-gray-500">
              <MapPin className="h-16 w-16 mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium mb-2">No hay {getLevelName(1).toLowerCase()}s registrados</h3>
              <p className="text-sm mb-4">
                Comienza agregando el primer {getLevelName(1).toLowerCase()} para estructurar la geografía de {countryConfig?.countryName || countryCode.toUpperCase()}.
              </p>
              <Button
                onClick={() => openAddDialog(1)}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Agregar primer {getLevelName(1).toLowerCase()}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Diálogo para agregar elementos */}
      <Dialog open={addItemState.show} onOpenChange={(open) => !open && closeAddDialog()}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              Agregar {getLevelName(addItemState.level)}
              {addItemState.parentName && (
                <span className="text-sm font-normal text-gray-500 ml-2">
                  en {addItemState.parentName}
                </span>
              )}
            </DialogTitle>
            <DialogDescription>
              Completa la información para agregar un nuevo {getLevelName(addItemState.level).toLowerCase()}
              {addItemState.parentName && ` en ${addItemState.parentName}`}.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="new-name">Nombre *</Label>
              <Input
                id="new-name"
                value={newItem.name}
                onChange={(e) => setNewItem(prev => ({ ...prev, name: e.target.value }))}
                placeholder={`Nombre del ${getLevelName(addItemState.level).toLowerCase()}`}
                autoFocus
              />
            </div>

            {getLevelConfig(addItemState.level)?.hasCode && (
              <div>
                <Label htmlFor="new-code">Código (opcional)</Label>
                <Input
                  id="new-code"
                  value={newItem.code}
                  onChange={(e) => setNewItem(prev => ({ ...prev, code: e.target.value }))}
                  placeholder="Código opcional"
                />
              </div>
            )}

            {addItemState.parentName && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <p className="text-sm text-blue-800">
                  <strong>Se agregará en:</strong> {addItemState.parentName}
                </p>
              </div>
            )}

            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={closeAddDialog}>
                Cancelar
              </Button>
              <Button onClick={handleAddItem} disabled={!newItem.name.trim()}>
                <Save className="h-4 w-4 mr-2" />
                Agregar {getLevelName(addItemState.level)}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
