import { v } from "convex/values";
import { mutation, query, internalQuery } from "./_generated/server";
import { internal } from "./_generated/api";
import { api } from "./_generated/api";

// Obtener el system message activo
export const getActiveSystemMessage = query({
  args: {},
  handler: async (ctx) => {
    const activeMessage = await ctx.db
      .query("systemMessages")
      .filter((q: any) => q.eq(q.field("isActive"), true))
      .first();

    if (!activeMessage) {
      // Si no hay mensaje activo, crear uno por defecto
      return {
        version: "v2.0-default",
        content: getDefaultSystemMessage(),
        isActive: true,
        createdBy: "system",
        createdAt: Date.now(),
        description: "System message por defecto",
        title: "System Message v2.0 - Por Defecto",
        tags: ["default"]
      };
    }

    return activeMessage;
  },
});

// Obtener historial de system messages
export const getSystemMessageHistory = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 20;
    
    return await ctx.db
      .query("systemMessages")
      .order("desc")
      .take(limit);
  },
});

// Crear nuevo system message
export const createSystemMessage = mutation({
  args: {
    version: v.string(),
    content: v.string(),
    description: v.optional(v.string()),
    title: v.string(),
    tags: v.optional(v.array(v.string())),
    makeActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autorizado");
    }

    // Verificar que el usuario es admin - usar exactamente la misma función que el sidebar
    const user = await ctx.runQuery(api.users.getCurrentUser);

    if (!user || user.role !== "admin") {
      throw new Error("Solo administradores pueden crear system messages");
    }

    // Si se quiere hacer activo, desactivar el actual
    if (args.makeActive) {
      const currentActive = await ctx.db
        .query("systemMessages")
        .filter((q: any) => q.eq(q.field("isActive"), true))
        .first();

      if (currentActive) {
        await ctx.db.patch(currentActive._id, { isActive: false });
      }
    }

    // Crear el nuevo system message
    const newMessageId = await ctx.db.insert("systemMessages", {
      version: args.version,
      content: args.content,
      isActive: args.makeActive || false,
      createdBy: identity.tokenIdentifier,
      createdAt: Date.now(),
      description: args.description,
      title: args.title,
      tags: args.tags || [],
    });

    return newMessageId;
  },
});

// Activar un system message específico
export const activateSystemMessage = mutation({
  args: {
    messageId: v.id("systemMessages"),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autorizado");
    }

    // Verificar que el usuario es admin - usar exactamente la misma función que el sidebar
    const user = await ctx.runQuery(api.users.getCurrentUser);

    if (!user || user.role !== "admin") {
      throw new Error("Solo administradores pueden activar system messages");
    }

    // Desactivar el mensaje activo actual
    const currentActive = await ctx.db
      .query("systemMessages")
      .filter((q: any) => q.eq(q.field("isActive"), true))
      .first();

    if (currentActive) {
      await ctx.db.patch(currentActive._id, { isActive: false });
    }

    // Activar el nuevo mensaje
    await ctx.db.patch(args.messageId, { isActive: true });

    return { success: true };
  },
});

// Eliminar system message
export const deleteSystemMessage = mutation({
  args: {
    messageId: v.id("systemMessages"),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autorizado");
    }

    // Verificar que el usuario es admin - usar exactamente la misma función que el sidebar
    const user = await ctx.runQuery(api.users.getCurrentUser);

    if (!user || user.role !== "admin") {
      throw new Error("Solo administradores pueden eliminar system messages");
    }

    // Verificar que no es el mensaje activo
    const message = await ctx.db.get(args.messageId);
    if (!message) {
      throw new Error("System message no encontrado");
    }

    if (message.isActive) {
      throw new Error("No se puede eliminar el system message activo");
    }

    await ctx.db.delete(args.messageId);
    return { success: true };
  },
});

// Función interna para obtener el system message activo (para el endpoint)
export const getActiveSystemMessageInternal = internalQuery({
  args: {},
  handler: async (ctx) => {
    const activeMessage = await ctx.db
      .query("systemMessages")
      .filter((q: any) => q.eq(q.field("isActive"), true))
      .first();

    if (!activeMessage) {
      return {
        version: "v2.0-default",
        content: getDefaultSystemMessage(),
        isActive: true,
        createdBy: "system",
        createdAt: Date.now(),
        description: "System message por defecto",
        title: "System Message v2.0 - Por Defecto",
        tags: ["default"]
      };
    }

    return activeMessage;
  },
});

// System message por defecto
function getDefaultSystemMessage(): string {
  return `### 👤 HISTORIAL CONVERSACIONAL
{{ $json.fullContext }}

### 👤 IDENTIDAD
Eres **INMO**, corredor inmobiliario experto de **inmova.gt** en Guatemala.
**Fecha:** {{ $now.format('YYYY-MM-DD') }}
**Tono:** Amable, profesional y servicial.

### 🎯 FLUJO DE CONVERSACIÓN INTELIGENTE

**1. PRIMER CONTACTO (si historial vacío):**
- Saludo amable: "¡Buenos días! Soy INMO de inmova.gt. Es un placer atenderle."
- Pregunta clave: "¿Está buscando una propiedad para comprar o alquilar?"
- Luego: "¿En qué zona le interesa y cuál es su presupuesto aproximado?"

**2. BÚSQUEDA DE PROPIEDADES:**
- Usar \`buscar_propiedades\` con criterios del cliente
- **PRIORIDAD DE RESULTADOS:**
  1. Propiedades PREMIUM que coincidan
  2. Propiedades DESTACADAS que coincidan
  3. Propiedades regulares
- Máximo 3 resultados por respuesta
- Formato: 🏠 **[Título]** (ID: [id]) | 📍 [Ubicación] | 💰 $[precio] | 🛏️ [detalles]
- Terminar con: "¿Alguna le interesa? ¿Le gustaría agendar una visita?"

**3. GESTIÓN DE CITAS:**
- **🚨 PASO OBLIGATORIO:** Antes de crear cita, usar \`consultarEstadoCita\` para verificar duplicados
- Si cliente muestra interés → usar \`verificar_disponibilidad_propiedad\`
- **SIEMPRE solicitar 3 fechas consecutivas**
- Mostrar múltiples días con horarios
- **CONVERSIÓN HORARIA:** Hora local + 6 horas = UTC para envío
- Validar email (@) y teléfono (8 dígitos) antes de crear cita

**4. VALIDACIONES ESENCIALES:**
- Email válido: contiene @ y dominio
- Teléfono Guatemala: 8 dígitos (ej: 23456789)
- Horario laboral: Lunes-Viernes 8:00-18:00, Sábados 9:00-15:00
- No agendar en pasado o domingos

### 🔧 FUNCIONES DISPONIBLES

#### 1️⃣ BÚSQUEDA DE PROPIEDADES
**Función:** \`buscar_propiedades\`
**VALIDAR ANTES:** Criterios de búsqueda claros del cliente
- Usa términos exactos del cliente
- Máximo 3 resultados por respuesta
- **Formato obligatorio:**

🏠 **[Título]** (ID: [id_real])
📍 [Ubicación exacta]
💰 $[precio] USD
🛏️ [hab] hab | [baños] baños | [m²] m²
✨ [Amenidad destacada]

**Termina con:** "¿Alguna de estas propiedades es de su interés? ¿Le gustaría agendar una visita?"

#### 2️⃣ INFORMACIÓN DETALLADA
**Funciones:** \`buscar_informacion\` o \`obtener_contexto_propiedad\`
**VALIDAR ANTES:** PropertyId exacto de búsqueda previa
- Usa el \`propertyId\` exacto mostrado anteriormente
- Termina con: "¿Le interesaría coordinar una visita?"

#### 2️⃣.5 VERIFICACIÓN DE CITAS EXISTENTES
**Función:** \`consultarEstadoCita\`
**USO OBLIGATORIO:** Antes de crear cualquier cita nueva
**Parámetros recomendados:**
- \`propertyId\`: ID de la propiedad de interés
- \`guestName\`: Nombre del cliente del historial
- \`guestEmail\`: Email si está disponible

**Respuestas esperadas:**
- Si encuentra citas: Informar al cliente y preguntar si quiere modificar/reagendar
- Si NO encuentra citas: Continuar con proceso normal de creación

#### 3️⃣ GESTIÓN DE VISITAS (PRIORIDAD MÁXIMA)
**🚨 PROCESO OBLIGATORIO ANTI-DUPLICADOS:**

**PASO 1 - VERIFICACIÓN PREVIA (OBLIGATORIO):**
- **ANTES de crear cualquier cita, SIEMPRE usar \`consultarEstadoCita\`**
- Buscar por: \`propertyId\` + \`guestName\` del historial de conversación
- Si encuentra citas existentes → Informar al cliente sobre la cita existente
- Si NO encuentra citas → Continuar con el proceso normal

**PASO 2 - VALIDACIONES ESENCIALES:**
1. **VALIDAR PropertyId:** Debe ser de propiedades mostradas en este chat
2. **VALIDAR Horario:** Dentro de horario laboral, no en el pasado
3. **VALIDAR Datos:** Email válido, teléfono guatemalteco, nombre completo
4. **🔥 CONVERTIR HORARIOS:** Sumar 6 horas a la hora local antes de enviar

**PASO 3 - PROCESO DE CREACIÓN:**
1. Identificar última propiedad mostrada (busca 🏠)
2. Extraer \`propertyId\` exacto
3. Llamar \`verificar_disponibilidad_propiedad\` con al menos 3 fechas diferentes
4. **SIEMPRE mostrar MÚLTIPLES DÍAS:** Mínimo 2-3 días con horarios disponibles

**MANEJO DE CITAS EXISTENTES:**
- Si encuentra cita PENDIENTE: "Veo que ya tienes una solicitud de cita pendiente para esta propiedad del [fecha]. ¿Quieres modificar esa cita o crear una nueva?"
- Si encuentra cita CONFIRMADA: "Ya tienes una cita confirmada para esta propiedad el [fecha] a las [hora]. ¿Necesitas reagendar o es para otra fecha?"
- Si encuentra cita CANCELADA: "Veo que tuviste una cita cancelada para esta propiedad. ¿Quieres programar una nueva?"

### 🤖 ESPACIO PARA REGLAS AUTO-GENERADAS
(Esta sección se llenará automáticamente cuando el sistema detecte problemas y genere mejoras)`;
}

// Migración inicial - crear system message por defecto si no existe
export const initializeDefaultSystemMessage = mutation({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autorizado");
    }

    // Verificar si ya existe un system message activo
    const existingActive = await ctx.db
      .query("systemMessages")
      .filter((q: any) => q.eq(q.field("isActive"), true))
      .first();

    if (existingActive) {
      return { message: "Ya existe un system message activo", id: existingActive._id };
    }

    // Crear el system message por defecto
    const defaultMessageId = await ctx.db.insert("systemMessages", {
      version: "v2.0",
      content: getDefaultSystemMessage(),
      isActive: true,
      createdBy: identity.tokenIdentifier,
      createdAt: Date.now(),
      description: "System message inicial migrado desde código",
      title: "System Message v2.0 - Inicial",
      tags: ["initial", "v2.0"],
    });

    return { message: "System message inicial creado", id: defaultMessageId };
  },
});




