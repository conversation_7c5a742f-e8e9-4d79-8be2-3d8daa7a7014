"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CreditsDisplay } from "@/components/ui/credits-display";
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle, DialogFooter, DialogTrigger, DialogDescription } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { api } from "@/convex/_generated/api";
import { useMutation, useQuery, useAction } from "convex/react";
import { toast } from "sonner";
import { useState } from "react";
import React from "react";
import { useUser } from "@clerk/clerk-react";
import { useRouter } from "next/navigation";

import {
  Settings,
  Database,
  CreditCard,
  Star,
  Crown,
  MessageSquare,
  Clock,
  CheckCircle,
  AlertTriangle,
  Shield,
  Lock,
  Edit,
  Save,
  X,
  User,
  Building2,
  UserCircle,
  BarChart3,
  Bot,
  Globe,
  Wrench,
  MapPin,
  Plus,
  List,
  RotateCcw,
  Trash2
} from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function AdminPage() {
  const { user } = useUser();
  const router = useRouter();
  const [loading, setLoading] = useState<string | null>(null);
  const [editingAction, setEditingAction] = useState<any>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editForm, setEditForm] = useState({
    cost: 0,
    duration: 0,
    description: "",
    isActive: true,
  });

  // Trial form states
  const [trialDays, setTrialDays] = useState(15);
  const [premiumPrice, setPremiumPrice] = useState(99);
  const [trialEnabled, setTrialEnabled] = useState(true);
  const [extendTrialForm, setExtendTrialForm] = useState({
    userEmail: "",
    days: 15,
  });

  // Estados para diálogos personalizados
  const [deleteDialog, setDeleteDialog] = useState({ open: false, confirmText: "" });
  const [demoDialog, setDemoDialog] = useState({ open: false, count: "10" });
  const [clearDialog, setClearDialog] = useState({ open: false, confirmText: "" });
  const [initializeDialog, setInitializeDialog] = useState({ open: false, confirmText: "" });

  // ✅ TODOS los hooks al inicio - ANTES de cualquier return
  const currentUser = useQuery(api.users.getCurrentUser);
  
  // Mutations - SIEMPRE en el mismo orden
  const expireServices = useMutation(api.transactions.expireServices);
  const updateCreditAction = useMutation(api.creditActions.updateCreditActionConfig);
  const makeUserAdmin = useMutation(api.users.makeAdmin);
  const reindexQdrant = useAction(api.rag.reindexAllProperties);
  const deleteAllProperties = useAction(api.rag.deleteAllProperties);
  const loadDemoProperties = useAction(api.rag.loadDemoProperties);
  const clearAllConversations = useAction(api.rag.clearAllConversations);
  const cleanupAllCreditConsumptions = useMutation(api.subscriptions.cleanupAllCreditConsumptions);
  // const initializeSystem = useAction(api.systemAdmin.initializeSystem);
  const getSystemStats = useMutation(api.systemAdmin.getSystemStats);

  // Trial mutations
  const updateAdminSetting = useMutation(api.admin.updateSetting);
  const extendUserTrial = useMutation(api.admin.extendUserTrial);

  // Queries - SIEMPRE en el mismo orden
  const creditActions = useQuery(api.creditActions.getAllCreditActions);
  const allTransactions = useQuery(api.transactions.getAllTransactionsWithUsers, { limit: 10, showAll: false });
  const featuredStatus = useQuery(api.featuredProperties.getUserFeaturedStatus);
  const expiringTransactions = useQuery(api.transactions.getExpiringTransactions, { daysAhead: 7 });
  const premiumProperty = useQuery(api.featuredProperties.getCurrentPremiumProperty);

  // Trial queries - Solo ejecutar si está autenticado como admin
  const adminSettings = useQuery(api.admin.getPublicSettings);
  const trialStats = useQuery(
    api.admin.getTrialStats,
    currentUser?.role === 'admin' ? {} : "skip"
  );
  const allUsers = useQuery(
    api.admin.getAllUsers,
    currentUser?.role === 'admin' ? {} : "skip"
  );

  // ✅ TODOS los useEffect al inicio también
  // Sync form values with settings
  React.useEffect(() => {
    if (adminSettings) {
      setTrialDays(adminSettings.trial_days || 15);
      setPremiumPrice(adminSettings.premium_price || 99);
      setTrialEnabled(adminSettings.trial_enabled ?? true);
    }
  }, [adminSettings]);

  // ✅ DESPUÉS de todos los hooks, hacer las verificaciones
  
  // Mostrar loading mientras se verifica el usuario
  if (currentUser === undefined) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-500">Verificando permisos...</p>
        </div>
      </div>
    );
  }

  // Verificar que el usuario existe y es admin
  if (!currentUser || currentUser.role !== 'admin') {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="max-w-md mx-auto text-center bg-white rounded-lg shadow-lg p-8">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Lock className="h-8 w-8 text-red-600" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Acceso Denegado</h1>
          <p className="text-gray-600 mb-6">
            No tienes permisos para acceder al panel de administración. 
            Solo los administradores pueden ver esta página.
          </p>
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-red-600" />
              <span className="text-sm text-red-800 font-medium">
                Rol requerido: Administrador
              </span>
            </div>
            <p className="text-sm text-red-700 mt-1">
              Tu rol actual: {currentUser?.role || 'Sin rol asignado'}
            </p>
          </div>
          <Button 
            onClick={() => window.history.back()} 
            variant="outline"
            className="w-full"
          >
            Volver Atrás
          </Button>
        </div>
      </div>
    );
  }

  // ✅ Handlers y lógica después de las verificaciones

  const handleExpireServices = async () => {
    try {
      setLoading("expire");
      const result = await expireServices();
      toast.success(`✅ Servicios expirados: ${result.processed}`);
    } catch (error: any) {
      toast.error(`❌ Error: ${error.message}`);
    } finally {
      setLoading(null);
    }
  };

  const handleCleanupCredits = async () => {
    toast("🧹 Confirmar limpieza de créditos", {
      description: "Esto eliminará TODOS los registros de créditos consumidos y transacciones. ¿Estás seguro?",
      action: {
        label: "Confirmar Limpieza",
        onClick: async () => {
          try {
            setLoading("cleanup-credits");
            const result = await cleanupAllCreditConsumptions({
              confirmAction: "CONFIRMAR_LIMPIAR_CREDITOS"
            });
            toast.success(`✅ ${result.message} - Eliminados: ${result.totalDeleted} registros`);
          } catch (error: any) {
            toast.error(`❌ Error: ${error.message}`);
          } finally {
            setLoading(null);
          }
        },
      },
      cancel: {
        label: "Cancelar",
        onClick: () => {
          toast.dismiss();
        },
      },
    });
  };

  const handleReindexQdrant = async () => {
    try {
      setLoading("reindex-qdrant");
      const result = await reindexQdrant({ force: false, batchSize: 20 });
      toast.success(`✅ ${result.message}`, {
        description: `${result.count} propiedades reindexadas en Qdrant`,
      });
    } catch (error: any) {
      console.error("Error reindexing Qdrant:", error);
      toast.error(`❌ Error al reindexar Qdrant: ${error.message}`);
    } finally {
      setLoading(null);
    }
  };

  const handleForceReindexQdrant = async () => {
    try {
      setLoading("force-reindex-qdrant");
      const result = await reindexQdrant({ force: true, batchSize: 20 });
      toast.success(`✅ ${result.message}`, {
        description: `${result.count} propiedades completamente reindexadas`,
      });
    } catch (error: any) {
      console.error("Error force reindexing Qdrant:", error);
      toast.error(`❌ Error al reindexar completamente Qdrant: ${error.message}`);
    } finally {
      setLoading(null);
    }
  };

  const handleDeleteAllProperties = () => {
    setDeleteDialog({ open: true, confirmText: "" });
  };

  const confirmDeleteAllProperties = async () => {
    if (deleteDialog.confirmText !== "DELETE_ALL_PROPERTIES_CONFIRM") {
      toast.error("❌ Código de confirmación incorrecto");
      return;
    }

    try {
      setLoading("delete-all-properties");
      setDeleteDialog({ open: false, confirmText: "" });
      const result = await deleteAllProperties({ confirmationCode: deleteDialog.confirmText });
      toast.success(`✅ ${result.message}`, {
        description: `${result.deletedCount} propiedades eliminadas`,
      });
    } catch (error: any) {
      console.error("Error deleting all properties:", error);
      toast.error(`❌ Error al eliminar propiedades: ${error.message}`);
    } finally {
      setLoading(null);
    }
  };

  const handleLoadDemoProperties = () => {
    setDemoDialog({ open: true, count: "10" });
  };

  const confirmLoadDemoProperties = async () => {
    const count = parseInt(demoDialog.count);
    if (isNaN(count) || count < 1 || count > 50) {
      toast.error("❌ Número inválido. Debe ser entre 1 y 50");
      return;
    }

    try {
      setLoading("load-demo-properties");
      setDemoDialog({ open: false, count: "10" });
      const result = await loadDemoProperties({ count });
      toast.success(`✅ ${result.message}`, {
        description: `${result.createdCount} propiedades demo creadas`,
      });
    } catch (error: any) {
      console.error("Error loading demo properties:", error);
      toast.error(`❌ Error al cargar propiedades demo: ${error.message}`);
    } finally {
      setLoading(null);
    }
  };

  const handleClearAllConversations = () => {
    setClearDialog({ open: true, confirmText: "" });
  };

  const confirmClearAllConversations = async () => {
    if (clearDialog.confirmText !== "CLEAR_ALL_CONVERSATIONS_CONFIRM") {
      toast.error("❌ Código de confirmación incorrecto");
      return;
    }

    try {
      setLoading("clear-conversations");
      setClearDialog({ open: false, confirmText: "" });
      const result = await clearAllConversations({ confirmationCode: clearDialog.confirmText });
      toast.success(`✅ ${result.message}`, {
        description: `${result.deletedConversations} conversaciones y ${result.deletedLeads} leads eliminados`,
      });
    } catch (error: any) {
      console.error("Error clearing conversations:", error);
      toast.error(`❌ Error al limpiar conversaciones: ${error.message}`);
    } finally {
      setLoading(null);
    }
  };

  // 🔄 INICIALIZAR SISTEMA
  const handleInitializeSystem = () => {
    setInitializeDialog({ open: true, confirmText: "" });
  };

  const confirmInitializeSystem = async () => {
    if (initializeDialog.confirmText !== "CONFIRMAR_INICIALIZACION") {
      toast.error("❌ Debe escribir exactamente: CONFIRMAR_INICIALIZACION");
      return;
    }

    try {
      setLoading("initialize-system");
      // const result = await initializeSystem({ confirmAction: "CONFIRMAR_INICIALIZACION" });

      toast.success(`✅ Sistema inicializado temporalmente deshabilitado`, {
        description: `Función en mantenimiento`
      });

      // Mostrar errores de Clerk si los hay
      // if (result.clerkDeletionErrors && result.clerkDeletionErrors.length > 0) {
      //   toast.warning("⚠️ Algunos usuarios no se pudieron eliminar de Clerk", {
      //     description: `${result.clerkDeletionErrors.length} errores (ver consola para detalles)`
      //   });
      //   console.warn("❌ Errores de eliminación de Clerk:", result.clerkDeletionErrors);
      // }

      setInitializeDialog({ open: false, confirmText: "" });

      // Mostrar estadísticas detalladas
      // console.log("📊 Estadísticas de inicialización:", result.deletedCounts);

    } catch (error: any) {
      console.error("Error initializing system:", error);
      toast.error(`❌ Error al inicializar sistema: ${error.message}`);
    } finally {
      setLoading(null);
    }
  };

  // 📊 VER ESTADÍSTICAS DEL SISTEMA
  const handleViewSystemStats = async () => {
    try {
      setLoading("system-stats");
      const result = await getSystemStats();

      const statsMessage = Object.entries(result.stats)
        .map(([table, count]) => `${table}: ${count}`)
        .join('\n');

      toast.success("📊 Estadísticas del Sistema", {
        description: statsMessage
      });

      console.log("📊 Estadísticas completas:", result.stats);

    } catch (error: any) {
      console.error("Error getting system stats:", error);
      toast.error(`❌ Error obteniendo estadísticas: ${error.message}`);
    } finally {
      setLoading(null);
    }
  };

  // const handleClearAllFeatured = async () => {
  //   toast("⚠️ Confirmar limpieza de propiedades", {
  //     description: "Esto limpiará TODAS las propiedades destacadas y devolverá los créditos a los usuarios.",
  //     action: {
  //       label: "Confirmar",
  //       onClick: async () => {
  //         try {
  //           setLoading("clearing");
  //           const result = await clearAllFeatured();
  //           toast.success(`✅ ${result.message}`);
  //         } catch (error: any) {
  //           toast.error(`❌ Error: ${error.message}`);
  //         } finally {
  //           setLoading(null);
  //         }
  //       },
  //     },
  //     cancel: {
  //       label: "Cancelar",
  //       onClick: () => {
  //         toast.dismiss();
  //       },
  //     },
  //     duration: 10000,
  //   });
  // };

  // const handleCleanInconsistentData = async () => {
  //   toast("🧹 Confirmar limpieza de datos inconsistentes", {
  //     description: "Esto limpiará referencias a transacciones canceladas y fechas expiradas.",
  //     action: {
  //       label: "Confirmar",
  //       onClick: async () => {
  //         try {
  //           setLoading("cleaning-data");
  //           const result = await cleanInconsistentData();
  //           toast.success(`✅ ${result.message}`);
  //         } catch (error: any) {
  //           toast.error(`❌ Error: ${error.message}`);
  //         } finally {
  //           setLoading(null);
  //         }
  //       },
  //     },
  //     cancel: {
  //       label: "Cancelar",
  //       onClick: () => {
  //         toast.dismiss();
  //       },
  //     },
  //     duration: 10000,
  //   });
  // };

  // const handleResetSubscriptions = async () => {
  //   toast("🔄 Confirmar reset de suscripciones", {
  //     description: "Esto cambiará TODAS las suscripciones a plan gratuito. Solo para testing.",
  //     action: {
  //       label: "Confirmar Reset",
  //       onClick: async () => {
  //         try {
  //           setLoading("reset-subscriptions");
  //           const result = await resetAllSubscriptions();
  //           toast.success(`✅ ${result.message}`);
  //         } catch (error: any) {
  //           toast.error(`❌ Error: ${error.message}`);
  //         } finally {
  //           setLoading(null);
  //         }
  //       },
  //     },
  //     cancel: {
  //       label: "Cancelar",
  //       onClick: () => {
  //         toast.dismiss();
  //       },
  //     },
  //     duration: 10000,
  //   });
  // };

  const handleEditAction = (action: any) => {
    setEditingAction(action);
    setEditForm({
      cost: action.cost,
      duration: action.duration || 0,
      description: action.description,
      isActive: action.isActive,
    });
    setDialogOpen(true);
  };

  const handleSaveEdit = async () => {
    if (!editingAction) return;
    
    try {
      setLoading("edit");
      await updateCreditAction({
        actionId: editingAction._id,
        cost: editForm.cost,
        duration: editForm.duration || undefined,
        description: editForm.description,
        isActive: editForm.isActive,
      });
      
      toast.success("✅ Configuración actualizada exitosamente");
      setEditingAction(null);
      setDialogOpen(false);
    } catch (error: any) {
      toast.error(`❌ Error: ${error.message}`);
    } finally {
      setLoading(null);
    }
  };

  const handleCancelEdit = () => {
    setEditingAction(null);
    setDialogOpen(false);
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case "featured_property":
        return <Star className="h-4 w-4 text-yellow-600" />;
      case "premium_home":
        return <Crown className="h-4 w-4 text-purple-600" />;
      case "message_inquiry":
      case "message_viewing":
      case "message_offer":
      case "message_negotiation":
        return <MessageSquare className="h-4 w-4 text-blue-600" />;
      default:
        return <CreditCard className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return (
          <Badge className="bg-green-100 text-green-800">
            <CheckCircle className="h-3 w-3 mr-1" />
            Activo
          </Badge>
        );
      case "expired":
        return (
          <Badge className="bg-red-100 text-red-800">
            <Clock className="h-3 w-3 mr-1" />
            Expirado
          </Badge>
        );
      case "cancelled":
        return (
          <Badge className="bg-gray-100 text-gray-800">
            Cancelado
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const handleMakeAdmin = async () => {
    if (!currentUser?.email) {
      toast.error("No se puede obtener el email del usuario");
      return;
    }

    try {
      await makeUserAdmin({ email: currentUser.email });
      toast.success("Usuario convertido a administrador");
      // Refrescar la página para actualizar el rol
      window.location.reload();
    } catch (error) {
      console.error("Error:", error);
      toast.error("Error al convertir usuario a admin");
    }
  };

  // Trial handlers
  const handleUpdateTrialDays = async () => {
    try {
      setLoading("trial-days");
      await updateAdminSetting({
        key: "trial_days",
        value: trialDays,
      });
      toast.success(`✅ Días de trial actualizados a ${trialDays}`);
    } catch (error: any) {
      toast.error(`❌ Error: ${error.message}`);
    } finally {
      setLoading(null);
    }
  };

  const handleUpdatePremiumPrice = async () => {
    try {
      setLoading("premium-price");
      await updateAdminSetting({
        key: "premium_price",
        value: premiumPrice,
      });
      toast.success(`✅ Precio premium actualizado a $${premiumPrice}`);
    } catch (error: any) {
      toast.error(`❌ Error: ${error.message}`);
    } finally {
      setLoading(null);
    }
  };

  const handleToggleTrialEnabled = async () => {
    try {
      setLoading("trial-enabled");
      await updateAdminSetting({
        key: "trial_enabled",
        value: !trialEnabled,
      });
      setTrialEnabled(!trialEnabled);
      toast.success(`✅ Trial ${!trialEnabled ? 'habilitado' : 'deshabilitado'}`);
    } catch (error: any) {
      toast.error(`❌ Error: ${error.message}`);
    } finally {
      setLoading(null);
    }
  };

  const handleExtendTrial = async () => {
    if (!extendTrialForm.userEmail.trim()) {
      toast.error("Ingresa un email válido");
      return;
    }

    try {
      setLoading("extend-trial");
      await extendUserTrial({
        userEmail: extendTrialForm.userEmail,
        additionalDays: extendTrialForm.days,
      });
      toast.success(`✅ Trial extendido ${extendTrialForm.days} días para ${extendTrialForm.userEmail}`);
      setExtendTrialForm({ userEmail: "", days: 15 });
    } catch (error: any) {
      toast.error(`❌ Error: ${error.message}`);
    } finally {
      setLoading(null);
    }
  };

  return (
    <div className="flex flex-col gap-6 p-4 md:p-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-semibold tracking-tight flex items-center gap-2">
            <Settings className="h-6 w-6 md:h-8 md:w-8" />
            Panel de Administración
          </h1>
          <p className="text-muted-foreground mt-2 text-sm md:text-base">
            Gestiona el sistema completo y todas las funcionalidades
          </p>
        </div>

        <div className="flex-shrink-0">
          <CreditsDisplay variant="compact" />
        </div>
      </div>

      <Tabs defaultValue="dashboard" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="dashboard" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Dashboard
          </TabsTrigger>
          <TabsTrigger value="config" className="flex items-center gap-2">
            <Globe className="h-4 w-4" />
            Configuración
          </TabsTrigger>
          <TabsTrigger value="tools" className="flex items-center gap-2">
            <Wrench className="h-4 w-4" />
            Herramientas
          </TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard" className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-4">Estado del Sistema</h3>
          </div>

          {/* Estado del Sistema */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <User className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Usuarios</p>
                <p className="text-2xl font-bold">{trialStats?.totalUsers || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Clock className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Trials Activos</p>
                <p className="text-2xl font-bold">{trialStats?.activeTrials || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Crown className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Suscripciones</p>
                <p className="text-2xl font-bold">{trialStats?.paidSubscriptions || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Conversión</p>
                <p className="text-2xl font-bold">{trialStats?.conversionRate || 0}%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
        </TabsContent>

        <TabsContent value="tools" className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-4">Herramientas del Sistema</h3>
          </div>

          {/* Acciones del Sistema */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Acciones del Sistema
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
            <Button
              onClick={handleExpireServices}
              disabled={loading === "expire"}
              variant="outline"
              className="w-full"
            >
              {loading === "expire" ? "Procesando..." : "⏰ Expirar Servicios"}
            </Button>

            <Button
              onClick={handleCleanupCredits}
              disabled={loading === "cleanup-credits"}
              variant="outline"
              className="w-full border-red-200 text-red-700 hover:bg-red-50"
            >
              {loading === "cleanup-credits" ? "Limpiando..." : "🧹 Limpiar Créditos"}
            </Button>

            <Button
              onClick={handleReindexQdrant}
              disabled={loading === "reindex-qdrant"}
              variant="outline"
              className="w-full"
            >
              {loading === "reindex-qdrant" ? "Reindexando..." : "🔄 Reindexar Qdrant"}
            </Button>

            <Button
              onClick={handleForceReindexQdrant}
              disabled={loading === "force-reindex-qdrant"}
              variant="destructive"
              className="w-full"
            >
              {loading === "force-reindex-qdrant" ? "Reindexando..." : "🔥 Reindexar Completo"}
            </Button>

            <Button
              onClick={handleDeleteAllProperties}
              disabled={loading === "delete-all-properties"}
              variant="destructive"
              className="w-full bg-red-600 hover:bg-red-700"
            >
              {loading === "delete-all-properties" ? "Eliminando..." : "🗑️ Eliminar TODAS las Propiedades"}
            </Button>

            <Button
              onClick={handleLoadDemoProperties}
              disabled={loading === "load-demo-properties"}
              variant="outline"
              className="w-full bg-green-50 text-green-700 border-green-200 hover:bg-green-100"
            >
              {loading === "load-demo-properties" ? "Cargando..." : "📦 Cargar Propiedades Demo"}
            </Button>

            <Button
              onClick={handleClearAllConversations}
              disabled={loading === "clear-conversations"}
              variant="destructive"
              className="w-full bg-orange-600 hover:bg-orange-700"
            >
              {loading === "clear-conversations" ? "Limpiando..." : "🗨️ Limpiar Conversaciones"}
            </Button>

            <Button
              onClick={handleInitializeSystem}
              disabled={loading === "initialize-system"}
              variant="destructive"
              className="w-full bg-red-800 hover:bg-red-900 text-white"
            >
              {loading === "initialize-system" ? "Inicializando..." : "🔄 Inicializar Sistema"}
            </Button>

            <Button
              onClick={handleViewSystemStats}
              disabled={loading === "system-stats"}
              variant="outline"
              className="w-full bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100"
            >
              {loading === "system-stats" ? "Cargando..." : "📊 Ver Estadísticas"}
            </Button>

            {/* <Button
              onClick={handleClearAllFeatured}
              disabled={loading === "clearing"}
              variant="outline"
              className="w-full"
            >
              {loading === "clearing" ? "Limpiando..." : "Limpiar Todas las Propiedades Destacadas"}
            </Button> */}

            {/* <Button
              onClick={handleCleanInconsistentData}
              disabled={loading === "cleaning-data"}
              variant="outline"
              className="w-full"
            >
              {loading === "cleaning-data" ? "Limpiando..." : "Limpiar Datos Inconsistentes"}
            </Button> */}

            {/* <Button
              onClick={handleResetSubscriptions}
              disabled={loading === "reset-subscriptions"}
              variant="outline"
              className="w-full bg-red-50 text-red-700 border-red-200 hover:bg-red-100"
            >
              {loading === "reset-subscriptions" ? "Reseteando..." : "🔄 Resetear Suscripciones"}
            </Button> */}
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-900 mb-2">ℹ️ Información:</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• <strong>Expirar Servicios:</strong> Ejecuta manualmente la limpieza de servicios</li>
              <li>• <strong>Limpiar Créditos:</strong> ⚠️ PELIGROSO - Elimina TODOS los registros de créditos consumidos</li>
              <li>• <strong>Reindexar Qdrant:</strong> Actualiza el índice de búsqueda vectorial (incremental)</li>
              <li>• <strong>Reindexar Completo:</strong> Recrea completamente el índice de Qdrant (fuerza)</li>
              <li>• <strong>Eliminar TODAS las Propiedades:</strong> ⚠️ PELIGROSO - Elimina todas las propiedades del sistema</li>
              <li>• <strong>Cargar Propiedades Demo:</strong> Crea propiedades de demostración para pruebas</li>
              <li>• <strong>Limpiar Conversaciones:</strong> ⚠️ PELIGROSO - Elimina todas las conversaciones y leads</li>
              <li>• <strong>Estado en tiempo real:</strong> Se actualiza automáticamente</li>
            </ul>
          </div>
        </CardContent>
      </Card>
        </TabsContent>

        <TabsContent value="config" className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-4">Configuración del Sistema</h3>
          </div>




          {/* Configuración de Trial y Precios */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Configuración de Trial */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Configuración de Trial
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="trial-days">Días de Trial</Label>
              <div className="flex gap-2">
                <Input
                  id="trial-days"
                  type="number"
                  min="1"
                  max="365"
                  value={trialDays}
                  onChange={(e) => setTrialDays(parseInt(e.target.value) || 15)}
                  className="flex-1"
                />
                <Button
                  onClick={handleUpdateTrialDays}
                  disabled={loading === "trial-days"}
                  size="sm"
                >
                  {loading === "trial-days" ? "..." : "Actualizar"}
                </Button>
              </div>
              <p className="text-sm text-muted-foreground">
                Días de trial gratuito para nuevos usuarios seller/agent
              </p>
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="trial-enabled">Trial Habilitado</Label>
                <p className="text-sm text-muted-foreground">
                  Activar/desactivar trial automático
                </p>
              </div>
              <Switch
                id="trial-enabled"
                checked={trialEnabled}
                onCheckedChange={handleToggleTrialEnabled}
                disabled={loading === "trial-enabled"}
              />
            </div>
          </CardContent>
        </Card>

        {/* Configuración de Precios */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Configuración de Precios
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="premium-price">Precio Premium (USD)</Label>
              <div className="flex gap-2">
                <Input
                  id="premium-price"
                  type="number"
                  min="1"
                  value={premiumPrice}
                  onChange={(e) => setPremiumPrice(parseInt(e.target.value) || 99)}
                  className="flex-1"
                />
                <Button
                  onClick={handleUpdatePremiumPrice}
                  disabled={loading === "premium-price"}
                  size="sm"
                >
                  {loading === "premium-price" ? "..." : "Actualizar"}
                </Button>
              </div>
              <p className="text-sm text-muted-foreground">
                Precio mensual de la suscripción premium
              </p>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">💡 Configuración Actual:</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• <strong>Trial:</strong> {trialDays} días {trialEnabled ? '(Activo)' : '(Desactivado)'}</li>
                <li>• <strong>Premium:</strong> ${premiumPrice}/mes</li>
                <li>• <strong>Créditos gratuitos:</strong> 10 por mes</li>
                <li>• <strong>Premium Home:</strong> 25 créditos</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Extender Trial Manual */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserCircle className="h-5 w-5" />
            Extender Trial Manual
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="user-email">Email del Usuario</Label>
              <Input
                id="user-email"
                type="email"
                placeholder="<EMAIL>"
                value={extendTrialForm.userEmail}
                onChange={(e) => setExtendTrialForm({...extendTrialForm, userEmail: e.target.value})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="extend-days">Días Adicionales</Label>
              <Input
                id="extend-days"
                type="number"
                min="1"
                max="365"
                value={extendTrialForm.days}
                onChange={(e) => setExtendTrialForm({...extendTrialForm, days: parseInt(e.target.value) || 15})}
              />
            </div>
            <div className="space-y-2">
              <Label>&nbsp;</Label>
              <Button
                onClick={handleExtendTrial}
                disabled={loading === "extend-trial" || !extendTrialForm.userEmail.trim()}
                className="w-full"
              >
                {loading === "extend-trial" ? "Extendiendo..." : "Extender Trial"}
              </Button>
            </div>
          </div>
          <p className="text-sm text-muted-foreground mt-2">
            Extiende el trial de un usuario específico por casos especiales
          </p>
        </CardContent>
      </Card>

      {/* Configuraciones de Créditos - MEJORADO */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Configuraciones de Créditos
            <div className="text-sm text-gray-500 font-normal">
              Haz clic en el ícono de editar para modificar
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {creditActions && creditActions.length > 0 ? (
            <div className="space-y-3">
              {creditActions.map((action: any) => (
                <div
                  key={action._id}
                  className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border hover:bg-gray-100 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    {getActionIcon(action.action)}
                    <div>
                      <p className="font-medium">{action.description}</p>
                      <p className="text-sm text-gray-600">
                        Acción: {action.action}
                        {action.duration && ` • Duración: ${action.duration} días`}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Badge className="bg-blue-100 text-blue-800">
                      {action.cost} créditos
                    </Badge>
                    <Badge variant={action.isActive ? "default" : "secondary"}>
                      {action.isActive ? "Activo" : "Inactivo"}
                    </Badge>
                    <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
                      <DialogTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditAction(action)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="sm:max-w-[425px]">
                        <DialogHeader>
                          <DialogTitle>Editar Configuración</DialogTitle>
                          <DialogDescription>
                            Modifica los valores para: {editingAction?.description}
                          </DialogDescription>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="cost" className="text-right">
                              Créditos
                            </Label>
                            <Input
                              id="cost"
                              type="number"
                              min="0"
                              value={editForm.cost}
                              onChange={(e) => setEditForm({...editForm, cost: parseInt(e.target.value) || 0})}
                              className="col-span-3"
                            />
                          </div>
                          {editingAction?.duration !== undefined && (
                            <div className="grid grid-cols-4 items-center gap-4">
                              <Label htmlFor="duration" className="text-right">
                                Días
                              </Label>
                              <Input
                                id="duration"
                                type="number"
                                min="0"
                                value={editForm.duration}
                                onChange={(e) => setEditForm({...editForm, duration: parseInt(e.target.value) || 0})}
                                className="col-span-3"
                              />
                            </div>
                          )}
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="description" className="text-right">
                              Descripción
                            </Label>
                            <Input
                              id="description"
                              value={editForm.description}
                              onChange={(e) => setEditForm({...editForm, description: e.target.value})}
                              className="col-span-3"
                            />
                          </div>
                          <div className="flex items-center space-x-2">
                            <Switch
                              id="isActive"
                              checked={editForm.isActive}
                              onCheckedChange={(checked) => setEditForm({...editForm, isActive: checked})}
                            />
                            <Label htmlFor="isActive">Activo</Label>
                          </div>
                        </div>
                        <DialogFooter>
                          <Button
                            type="button"
                            variant="outline"
                            onClick={handleCancelEdit}
                          >
                            <X className="h-4 w-4 mr-2" />
                            Cancelar
                          </Button>
                          <Button 
                            type="submit" 
                            onClick={handleSaveEdit}
                            disabled={loading === "edit"}
                          >
                            <Save className="h-4 w-4 mr-2" />
                            {loading === "edit" ? "Guardando..." : "Guardar"}
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Database className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No hay configuraciones. Ejecuta el seed primero.</p>
            </div>
          )}
        </CardContent>
      </Card>



          {/* Acceso a Configuración de Ubicaciones */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                Configuración de Ubicaciones
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg">
                <div>
                  <h4 className="font-medium text-green-900 mb-1">Gestión de Ubicaciones</h4>
                  <p className="text-sm text-green-700">
                    Configura países, departamentos, municipios y zonas
                  </p>
                </div>
                <Button
                  onClick={() => router.push('/dashboard/admin/location-config')}
                  className="flex items-center gap-2"
                >
                  <Globe className="h-4 w-4" />
                  Abrir Panel
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

      </Tabs>

      {/* Diálogo para eliminar todas las propiedades */}
      <Dialog open={deleteDialog.open} onOpenChange={(open) => setDeleteDialog({ ...deleteDialog, open })}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-red-600">
              <AlertTriangle className="h-5 w-5" />
              ⚠️ PELIGRO: Eliminar TODAS las Propiedades
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              Esta acción eliminará <strong>TODAS</strong> las propiedades del sistema de forma permanente.
            </p>
            <div className="space-y-2">
              <Label htmlFor="delete-confirm">
                Para confirmar, escribe exactamente: <code className="bg-gray-100 px-1 rounded">DELETE_ALL_PROPERTIES_CONFIRM</code>
              </Label>
              <Input
                id="delete-confirm"
                value={deleteDialog.confirmText}
                onChange={(e) => setDeleteDialog({ ...deleteDialog, confirmText: e.target.value })}
                placeholder="Escribe el código de confirmación..."
                className="font-mono"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialog({ open: false, confirmText: "" })}
            >
              Cancelar
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDeleteAllProperties}
              disabled={deleteDialog.confirmText !== "DELETE_ALL_PROPERTIES_CONFIRM"}
            >
              Eliminar TODAS las Propiedades
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo para cargar propiedades demo */}
      <Dialog open={demoDialog.open} onOpenChange={(open) => setDemoDialog({ ...demoDialog, open })}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Cargar Propiedades Demo
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              ¿Cuántas propiedades demo quieres cargar?
            </p>
            <div className="space-y-2">
              <Label htmlFor="demo-count">Número de propiedades (1-50)</Label>
              <Input
                id="demo-count"
                type="number"
                min="1"
                max="50"
                value={demoDialog.count}
                onChange={(e) => setDemoDialog({ ...demoDialog, count: e.target.value })}
                placeholder="10"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDemoDialog({ open: false, count: "10" })}
            >
              Cancelar
            </Button>
            <Button onClick={confirmLoadDemoProperties}>
              Cargar Propiedades Demo
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo para limpiar conversaciones */}
      <Dialog open={clearDialog.open} onOpenChange={(open) => setClearDialog({ ...clearDialog, open })}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-red-600">
              <AlertTriangle className="h-5 w-5" />
              ⚠️ PELIGRO: Limpiar TODAS las Conversaciones
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              Esta acción eliminará <strong>TODAS</strong> las conversaciones y leads del sistema de forma permanente.
            </p>
            <div className="space-y-2">
              <Label htmlFor="clear-confirm">
                Para confirmar, escribe exactamente: <code className="bg-gray-100 px-1 rounded">CLEAR_ALL_CONVERSATIONS_CONFIRM</code>
              </Label>
              <Input
                id="clear-confirm"
                value={clearDialog.confirmText}
                onChange={(e) => setClearDialog({ ...clearDialog, confirmText: e.target.value })}
                placeholder="Escribe el código de confirmación..."
                className="font-mono"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setClearDialog({ open: false, confirmText: "" })}
            >
              Cancelar
            </Button>
            <Button
              variant="destructive"
              onClick={confirmClearAllConversations}
              disabled={clearDialog.confirmText !== "CLEAR_ALL_CONVERSATIONS_CONFIRM"}
            >
              Limpiar TODAS las Conversaciones
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo para inicializar sistema */}
      <Dialog open={initializeDialog.open} onOpenChange={(open) => setInitializeDialog({ ...initializeDialog, open })}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-red-600">
              <RotateCcw className="h-5 w-5" />
              🔄 PELIGRO: Inicializar Sistema
            </DialogTitle>
            <DialogDescription className="text-red-600 font-medium">
              ⚠️ ESTA ACCIÓN ES IRREVERSIBLE ⚠️
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <h4 className="font-medium text-red-900 mb-2">Esta acción eliminará:</h4>
              <ul className="text-sm text-red-800 space-y-1">
                <li>• TODAS las propiedades</li>
                <li>• TODOS los favoritos</li>
                <li>• TODAS las órdenes</li>
                <li>• TODOS los mensajes</li>
                <li>• TODAS las transacciones</li>
                <li>• TODAS las conversaciones</li>
                <li>• TODOS los leads</li>
                <li>• TODAS las citas</li>
                <li>• TODOS los usuarios (excepto admin) - <strong>DE CONVEX Y CLERK</strong></li>
                <li>• TODAS las suscripciones (excepto admin)</li>
              </ul>
            </div>
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h4 className="font-medium text-green-900 mb-2">Se preservarán:</h4>
              <ul className="text-sm text-green-800 space-y-1">
                <li>• Configuraciones de admin (adminSettings)</li>
                <li>• Base de conocimiento (knowledgeBase)</li>
                <li>• Configuraciones de ubicación (locationConfig)</li>
                <li>• Datos de ubicación (locationData)</li>
                <li>• Acciones de crédito (creditActions)</li>
                <li>• Usuario admin y su suscripción</li>
              </ul>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">
                Para confirmar, escriba exactamente: <code className="bg-gray-100 px-1 rounded">CONFIRMAR_INICIALIZACION</code>
              </label>
              <input
                type="text"
                value={initializeDialog.confirmText}
                onChange={(e) => setInitializeDialog({ ...initializeDialog, confirmText: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                placeholder="Escriba la confirmación aquí..."
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setInitializeDialog({ open: false, confirmText: "" })}
            >
              Cancelar
            </Button>
            <Button
              variant="destructive"
              onClick={confirmInitializeSystem}
              disabled={initializeDialog.confirmText !== "CONFIRMAR_INICIALIZACION"}
              className="bg-red-800 hover:bg-red-900"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Inicializar Sistema
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

    </div>
  );
}