"use client";

import { useState, useEffect, Suspense } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { useUser } from "@clerk/nextjs";
import { PropertyCard } from "@/components/marketplace/property-card";
import { AdvancedSearch } from "@/components/marketplace/advanced-search";
import { Button } from "@/components/ui/button";
import { Grid, List, Search } from "lucide-react";
import { Property, PropertyFilters } from "@/types/marketplace";
import { useDebounce } from "use-debounce";
import { useSearchParams, useRouter } from "next/navigation";
import { toast } from "sonner";
import { Pagination } from "@/components/ui/pagination";

function PropertiesPageContent() {
  const { user } = useUser();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [searchData, setSearchData] = useState<PropertyFilters & { searchTerm?: string }>({});
  const [debouncedSearchTerm] = useDebounce(searchData.searchTerm || "", 300);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

  // Estado de paginación
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  // Mutations para favoritos
  const toggleFavorite = useMutation(api.properties.toggleFavorite);

  // Query para obtener favoritos del usuario
  const userFavorites = useQuery(
    api.properties.getUserFavorites,
    user ? { paginationOpts: { numItems: 1000, cursor: null } } : "skip"
  );

  // Inicializar filtros desde URL params
  useEffect(() => {
    const urlFilters: PropertyFilters & { searchTerm?: string } = {};

    // Leer parámetros de la URL
    const search = searchParams.get('search');
    const type = searchParams.get('type');
    const status = searchParams.get('status');
    const minPrice = searchParams.get('minPrice');
    const maxPrice = searchParams.get('maxPrice');
    const amenities = searchParams.get('amenities');

    // Leer parámetros de ubicación
    const country = searchParams.get('country');
    const level1 = searchParams.get('level1');
    const level2 = searchParams.get('level2');
    const level3 = searchParams.get('level3');
    const level4 = searchParams.get('level4');

    // Leer parámetros de paginación
    const page = searchParams.get('page');
    const size = searchParams.get('pageSize');

    if (search) {
      urlFilters.searchTerm = search;
    }

    if (type && ['house', 'apartment', 'office', 'land', 'commercial'].includes(type)) {
      urlFilters.type = [type as 'house' | 'apartment' | 'office' | 'land' | 'commercial'];
    }
    if (status && ['for_sale', 'for_rent', 'sold', 'rented', 'draft'].includes(status)) {
      urlFilters.status = [status as 'for_sale' | 'for_rent' | 'sold' | 'rented' | 'draft'];
    }
    if (minPrice) urlFilters.minPrice = Number(minPrice);
    if (maxPrice) urlFilters.maxPrice = Number(maxPrice);
    if (amenities) urlFilters.amenities = amenities.split(',');

    // Agregar filtros de ubicación
    if (country) urlFilters.locationCountry = country;
    if (level1) urlFilters.locationLevel1 = level1;
    if (level2) urlFilters.locationLevel2 = level2;
    if (level3) urlFilters.locationLevel3 = level3;
    if (level4) urlFilters.locationLevel4 = level4;

    setSearchData(urlFilters);

    // Actualizar estado de paginación
    if (page) setCurrentPage(Number(page));
    if (size) setPageSize(Number(size));
  }, [searchParams]);

  // Queries
  const searchResult = useQuery(api.properties.advancedSearchProperties, {
    searchTerm: debouncedSearchTerm || undefined,
    type: searchData.type?.[0],
    status: searchData.status?.[0],
    minPrice: searchData.minPrice,
    maxPrice: searchData.maxPrice,
    featured: searchData.featured,
    requiredAmenities: searchData.amenities,
    // Filtros de ubicación
    locationCountry: searchData.locationCountry,
    locationLevel1: searchData.locationLevel1,
    locationLevel2: searchData.locationLevel2,
    locationLevel3: searchData.locationLevel3,
    locationLevel4: searchData.locationLevel4,
    // Parámetros de paginación
    page: currentPage,
    pageSize: pageSize,
  });

  // Manejar resultado de búsqueda (puede ser array o objeto con paginación)
  const displayProperties = Array.isArray(searchResult) ? searchResult : searchResult?.properties;
  const paginationInfo = Array.isArray(searchResult) ? null : searchResult?.pagination;

  const handleSearch = (data: PropertyFilters & { searchTerm?: string }) => {
    setSearchData(data);
    setCurrentPage(1); // Resetear a primera página en nueva búsqueda
    updateURL(data, 1, pageSize);
  };

  const clearFilters = () => {
    setSearchData({});
    setCurrentPage(1);
    updateURL({}, 1, pageSize);
  };

  // Función para actualizar URL con parámetros
  const updateURL = (filters: PropertyFilters & { searchTerm?: string }, page: number, size: number) => {
    const params = new URLSearchParams();

    if (filters.searchTerm) params.set('search', filters.searchTerm);
    if (filters.type?.[0]) params.set('type', filters.type[0]);
    if (filters.status?.[0]) params.set('status', filters.status[0]);
    if (filters.minPrice) params.set('minPrice', filters.minPrice.toString());
    if (filters.maxPrice) params.set('maxPrice', filters.maxPrice.toString());
    if (filters.amenities?.length) params.set('amenities', filters.amenities.join(','));

    // Filtros de ubicación
    if (filters.locationCountry) params.set('country', filters.locationCountry);
    if (filters.locationLevel1) params.set('level1', filters.locationLevel1);
    if (filters.locationLevel2) params.set('level2', filters.locationLevel2);
    if (filters.locationLevel3) params.set('level3', filters.locationLevel3);
    if (filters.locationLevel4) params.set('level4', filters.locationLevel4);

    // Parámetros de paginación
    if (page > 1) params.set('page', page.toString());
    if (size !== 20) params.set('pageSize', size.toString());

    router.push(`/properties?${params.toString()}`);
  };

  // Manejar cambio de página
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    updateURL(searchData, page, pageSize);
  };

  // Manejar cambio de tamaño de página
  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1); // Resetear a primera página
    updateURL(searchData, 1, size);
  };

  // Función para verificar si una propiedad es favorita
  const isPropertyFavorite = (propertyId: string): boolean => {
    if (!userFavorites?.page) return false;
    return userFavorites.page.some((fav: any) => fav._id === propertyId);
  };

  // Función para manejar favoritos
  const handleToggleFavorite = async (propertyId: string) => {
    if (!user) {
      // Redirigir a login si no está autenticado
      window.location.href = '/sign-in';
      return;
    }

    try {
      const result = await toggleFavorite({ propertyId: propertyId as any });

      // Mostrar notificación según la acción
      if (result.action === "added") {
        toast.success("❤️ Propiedad agregada a favoritos");
      } else {
        toast.success("💔 Propiedad removida de favoritos");
      }
    } catch (error) {
      console.error("Error toggling favorite:", error);
      toast.error("❌ Error al actualizar favoritos");
    }
  };

  return (
    <div className="w-full">
      <div className="container mx-auto px-4 py-6 md:py-8">
        {/* Header */}
        <div className="mb-6 md:mb-8">
          <h1 className="text-3xl md:text-4xl font-bold mb-2 md:mb-4 text-gray-900">Propiedades</h1>
          <p className="text-gray-600 text-base md:text-lg">
            Encuentra tu propiedad ideal entre {displayProperties?.length || 0} opciones disponibles
          </p>
        </div>

        {/* Search and Filters */}
        <div className="mb-6 md:mb-8">
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4 mb-4">
            <div className="flex-1">
              <AdvancedSearch
                onSearch={handleSearch}
                variant="page"
                showAdvancedByDefault={false}
              />
            </div>
            
            {/* View Mode Toggle */}
            <div className="flex gap-2 justify-center lg:justify-end">
              <Button
                variant={viewMode === "grid" ? "default" : "outline"}
                size="icon"
                onClick={() => setViewMode("grid")}
                className={viewMode === "grid" ? "bg-blue-600 hover:bg-blue-700" : "border-2 border-gray-200 hover:border-blue-500"}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "outline"}
                size="icon"
                onClick={() => setViewMode("list")}
                className={viewMode === "list" ? "bg-blue-600 hover:bg-blue-700" : "border-2 border-gray-200 hover:border-blue-500"}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Results */}
        <div className="space-y-4 md:space-y-6">
          {/* Results Count */}
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-3">
              <p className="text-base md:text-lg font-medium text-gray-700">
                {paginationInfo ?
                  `${paginationInfo.totalItems} propiedades encontradas` :
                  `${displayProperties?.length || 0} propiedades encontradas`
                }
              </p>
            </div>
          </div>

          {/* Properties Grid/List */}
          {displayProperties && displayProperties.length > 0 ? (
            <div className={
              viewMode === "grid"
                ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 lg:gap-8"
                : "space-y-4 md:space-y-6"
            }>
              {displayProperties.map((property: any, index: any) => (
                <PropertyCard
                  key={property._id || `result-${index}`}
                  property={property as Property}
                  onFavorite={handleToggleFavorite}
                  isFavorite={isPropertyFavorite(property._id)}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12 md:py-20 bg-white rounded-2xl shadow-lg">
              <div className="w-16 h-16 md:w-20 md:h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4 md:mb-6">
                <Search className="w-8 h-8 md:w-10 md:h-10 text-gray-400" />
              </div>
              <h3 className="text-lg md:text-xl font-semibold text-gray-900 mb-2">No se encontraron propiedades</h3>
              <p className="text-gray-600 mb-4 md:mb-6 px-4">
                Intenta ajustar tus filtros de búsqueda para encontrar más resultados
              </p>
              <Button onClick={clearFilters} className="bg-blue-600 hover:bg-blue-700 text-white rounded-xl">
                Limpiar todos los filtros
              </Button>
            </div>
          )}

          {/* Paginación */}
          {paginationInfo && paginationInfo.totalPages > 1 && (
            <div className="mt-8">
              <Pagination
                currentPage={paginationInfo.currentPage}
                totalPages={paginationInfo.totalPages}
                totalItems={paginationInfo.totalItems}
                pageSize={paginationInfo.pageSize}
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                className="justify-center"
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Loading fallback component
function PropertiesLoading() {
  return (
    <div className="w-full">
      <div className="container mx-auto px-4 py-6 md:py-8">
        <div className="mb-6 md:mb-8">
          <div className="h-8 bg-gray-200 rounded animate-pulse mb-4"></div>
          <div className="h-4 bg-gray-200 rounded animate-pulse w-2/3"></div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 lg:gap-8">
          {Array.from({ length: 6 }).map((_: any, i: any) => (
            <div key={`loading-skeleton-${i}`} className="bg-gray-200 rounded-lg h-64 animate-pulse"></div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default function PropertiesPage() {
  return (
    <Suspense fallback={<PropertiesLoading />}>
      <PropertiesPageContent />
    </Suspense>
  );
} 