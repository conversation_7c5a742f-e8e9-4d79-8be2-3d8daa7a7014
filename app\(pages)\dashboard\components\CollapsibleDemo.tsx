"use client";

import React from 'react';
import { CollapsibleCard, useCollapsibleState } from "@/components/ui/collapsible-card";
import { CollapsibleMessageCard, useCollapsibleMessages, MessageListControls } from "@/components/ui/collapsible-message-card";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  MessageSquare, 
  Calendar, 
  User, 
  Building2, 
  Mail,
  Clock,
  CheckCircle
} from 'lucide-react';

// Datos de ejemplo
const sampleRequests = [
  {
    _id: "1",
    type: "property_viewing",
    guestName: "<PERSON>",
    requestedStartTime: new Date().toISOString(),
    property: { title: "Casa en Zona 15", city: "Guatemala" },
    status: "pending"
  },
  {
    _id: "2", 
    type: "consultation",
    guestName: "<PERSON>",
    requestedStartTime: new Date().toISOString(),
    property: { title: "Apartamento Zona 10", city: "Guatemala" },
    status: "pending"
  }
];

const sampleMessages = [
  {
    _id: "msg1",
    subject: "Consulta sobre Casa en Zona 15",
    senderName: "<PERSON> López",
    senderEmail: "<EMAIL>",
    message: "Estoy interesada en conocer más detalles sobre esta propiedad...",
    leadType: "inquiry",
    status: "unread",
    createdAt: new Date().toISOString(),
    property: {
      title: "Casa en Zona 15",
      price: 250000,
      currency: "USD",
      city: "Guatemala",
      type: "house",
      images: []
    }
  },
  {
    _id: "msg2",
    subject: "Solicitud de visita",
    senderName: "Roberto Méndez", 
    senderEmail: "<EMAIL>",
    message: "Me gustaría agendar una visita para este apartamento...",
    leadType: "viewing",
    status: "read",
    createdAt: new Date().toISOString(),
    property: {
      title: "Apartamento Zona 10",
      price: 180000,
      currency: "USD", 
      city: "Guatemala",
      type: "apartment",
      images: []
    }
  }
];

export function CollapsibleDemo() {
  // Estado para múltiples secciones colapsables
  const { states, toggle, expandAll, collapseAll, isExpanded } = useCollapsibleState({
    requests: true,
    messages: true,
    stats: false
  });

  // Estado para mensajes individuales colapsables
  const messageIds = sampleMessages.map(m => m._id);
  const {
    expandedStates,
    toggleMessage,
    expandAll: expandAllMessages,
    collapseAll: collapseAllMessages
  } = useCollapsibleMessages(messageIds, false);

  const expandedCount = Object.values(expandedStates).filter(Boolean).length;

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Demo: Componentes Colapsables</h1>
        <div className="flex gap-2">
          <Button onClick={expandAll} variant="outline" size="sm">
            Expandir Secciones
          </Button>
          <Button onClick={collapseAll} variant="outline" size="sm">
            Contraer Secciones
          </Button>
        </div>
      </div>

      {/* Estadísticas colapsables */}
      <CollapsibleCard
        title="Estadísticas del Dashboard"
        icon={CheckCircle}
        defaultExpanded={isExpanded('stats')}
        variant="compact"
      >
        <div className="grid grid-cols-3 gap-4 p-4">
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-blue-600">12</div>
              <div className="text-sm text-gray-600">Mensajes Nuevos</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-green-600">5</div>
              <div className="text-sm text-gray-600">Citas Pendientes</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-purple-600">8</div>
              <div className="text-sm text-gray-600">Propiedades Activas</div>
            </CardContent>
          </Card>
        </div>
      </CollapsibleCard>

      {/* Solicitudes colapsables */}
      <CollapsibleCard
        title="Solicitudes de Cita"
        icon={Calendar}
        showCount={sampleRequests.length}
        defaultExpanded={isExpanded('requests')}
        contentClassName="space-y-3"
      >
        {sampleRequests.map((request: any) => (
          <Card key={request._id} className="border-l-4 border-l-yellow-400">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">{request.guestName}</h4>
                  <p className="text-sm text-gray-600">{request.property.title}</p>
                  <div className="flex items-center gap-2 mt-1">
                    <Clock className="w-3 h-3" />
                    <span className="text-xs text-gray-500">
                      {new Date(request.requestedStartTime).toLocaleDateString()}
                    </span>
                  </div>
                </div>
                <Badge className="bg-yellow-100 text-yellow-800">
                  Pendiente
                </Badge>
              </div>
            </CardContent>
          </Card>
        ))}
      </CollapsibleCard>

      {/* Mensajes con control individual */}
      <CollapsibleCard
        title="Mensajes"
        icon={Mail}
        showCount={sampleMessages.length}
        defaultExpanded={isExpanded('messages')}
      >
        <div className="space-y-4">
          {/* Controles para expandir/contraer todos los mensajes */}
          <MessageListControls
            onExpandAll={expandAllMessages}
            onCollapseAll={collapseAllMessages}
            totalCount={sampleMessages.length}
            expandedCount={expandedCount}
          />

          {/* Lista de mensajes colapsables individualmente */}
          {sampleMessages.map((message: any) => (
            <CollapsibleMessageCard
              key={message._id}
              message={message}
              defaultExpanded={expandedStates[message._id]}
              showPreview={true}
            >
              {/* Contenido completo del mensaje */}
              <Card className="border-l-4 border-l-green-400">
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <div className="w-16 h-16 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0">
                      <div className="w-full h-full flex items-center justify-center">
                        <Building2 className="h-8 w-8 text-gray-400" />
                      </div>
                    </div>

                    <div className="flex-1">
                      <div className="flex items-start justify-between mb-2">
                        <div>
                          <h3 className="font-semibold text-gray-900">{message.subject}</h3>
                          <p className="text-sm text-gray-600">
                            De: <span className="font-medium">{message.senderName}</span> ({message.senderEmail})
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge className={
                            message.leadType === 'inquiry' ? 'bg-blue-100 text-blue-800' :
                            message.leadType === 'viewing' ? 'bg-green-100 text-green-800' :
                            'bg-gray-100 text-gray-800'
                          }>
                            {message.leadType === 'inquiry' ? 'Consulta' : 
                             message.leadType === 'viewing' ? 'Visita' : 'General'}
                          </Badge>
                          {message.status === 'unread' && (
                            <Badge variant="default" className="bg-blue-600">
                              Nuevo
                            </Badge>
                          )}
                        </div>
                      </div>

                      {/* Información de la propiedad */}
                      {message.property && (
                        <div className="bg-gray-50 rounded-lg p-3 mb-3">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium text-gray-900">{message.property.title}</h4>
                            <span className="text-sm font-semibold text-blue-600">
                              ${message.property.price.toLocaleString()} {message.property.currency}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600">{message.property.city} • {message.property.type}</p>
                        </div>
                      )}

                      {/* Mensaje completo */}
                      <p className="text-gray-700 mb-3">{message.message}</p>

                      {/* Acciones */}
                      <div className="flex items-center gap-2">
                        <Button size="sm" variant="outline">
                          Responder
                        </Button>
                        <Button size="sm" variant="ghost">
                          Archivar
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </CollapsibleMessageCard>
          ))}
        </div>
      </CollapsibleCard>
    </div>
  );
}
