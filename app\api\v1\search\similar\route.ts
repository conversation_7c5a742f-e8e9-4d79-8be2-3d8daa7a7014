import { NextRequest, NextResponse } from 'next/server';
import { PropertyRAGEngine } from '@/lib/qdrant/search';
import { validateQdrantConfig } from '@/lib/qdrant/config';

// Validar API Key
function validateApiKey(request: NextRequest): boolean {
  const apiKey = request.headers.get('X-API-Key');
  const validApiKey = process.env.RAG_API_KEY;
  
  if (!validApiKey) {
    console.warn('RAG_API_KEY not configured');
    return false;
  }
  
  return apiKey === validApiKey;
}

// Endpoint para encontrar propiedades similares
export async function POST(request: NextRequest) {
  let propertyId: string | undefined;

  try {
    // Validar API Key
    if (!validateApiKey(request)) {
      return NextResponse.json(
        { error: 'Invalid or missing API key' },
        { status: 401 }
      );
    }

    // Validar configuración
    validateQdrantConfig();

    // Parsear body
    const body = await request.json();
    const {
      propertyId: bodyPropertyId,
      limit = 5,
      scoreThreshold = 0.8,
      excludeOriginal = true,
      includeMetadata = false,
    } = body;

    propertyId = bodyPropertyId;

    if (!propertyId || typeof propertyId !== 'string') {
      return NextResponse.json(
        { error: 'PropertyId is required and must be a string' },
        { status: 400 }
      );
    }

    console.log(`API Similar properties search for: ${propertyId} (limit: ${limit})`);

    // Crear instancia del motor RAG
    const ragEngine = new PropertyRAGEngine();

    // Buscar propiedades similares
    const searchResults = await ragEngine.findSimilarProperties({
      propertyId,
      limit,
      scoreThreshold,
      excludeOriginal,
    });

    // Preparar respuesta
    const properties = searchResults.map(result => {
      const baseProperty = {
        id: result.payload.propertyId,
        type: result.payload.type,
        status: result.payload.status,
        price: result.payload.price,
        currency: result.payload.currency,
        bedrooms: result.payload.bedrooms,
        bathrooms: result.payload.bathrooms,
        area: result.payload.area,
        location: {
          country: result.payload.country,
          level1: result.payload.level1,
          level2: result.payload.level2,
          neighborhood: result.payload.neighborhood,
        },
        coordinates: result.payload.coordinates,
        amenities: result.payload.amenities,
        isActive: result.payload.isActive,
        isFeatured: result.payload.isFeatured,
        isPremium: result.payload.isPremium,
      };

      // Incluir metadata si se solicita
      if (includeMetadata) {
        return {
          ...baseProperty,
          metadata: {
            similarityScore: Math.round(result.score * 100) / 100,
            createdAt: result.payload.createdAt,
            updatedAt: result.payload.updatedAt,
            embeddingText: result.payload.embeddingText?.substring(0, 200) + '...',
          },
        };
      }

      return {
        ...baseProperty,
        similarityScore: Math.round(result.score * 100) / 100,
      };
    });

    // Calcular estadísticas de similitud
    const similarityStats = {
      averageScore: searchResults.length > 0 
        ? Math.round((searchResults.reduce((sum, r) => sum + r.score, 0) / searchResults.length) * 100) / 100
        : 0,
      highestScore: searchResults.length > 0 
        ? Math.round(Math.max(...searchResults.map(r => r.score)) * 100) / 100
        : 0,
      lowestScore: searchResults.length > 0 
        ? Math.round(Math.min(...searchResults.map(r => r.score)) * 100) / 100
        : 0,
    };

    const response = {
      originalPropertyId: propertyId,
      resultsCount: properties.length,
      similarityStats,
      properties,
      searchParameters: {
        limit,
        scoreThreshold,
        excludeOriginal,
      },
      timestamp: new Date().toISOString(),
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error in similar properties API:', error);
    
    // Manejar errores específicos
    if (error instanceof Error && error.message.includes('not found in index')) {
      return NextResponse.json(
        { 
          error: 'Property not found',
          message: `Property ${propertyId} is not indexed or does not exist`,
          timestamp: new Date().toISOString(),
        },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Endpoint GET para documentación
export async function GET() {
  return NextResponse.json({
    endpoint: '/api/v1/search/similar',
    description: 'Find properties similar to a given property using vector similarity',
    method: 'POST',
    authentication: 'X-API-Key header required',
    parameters: {
      propertyId: 'string (required) - ID of the reference property',
      limit: 'number (optional, default: 5) - Maximum similar properties to return',
      scoreThreshold: 'number (optional, default: 0.8) - Minimum similarity score',
      excludeOriginal: 'boolean (optional, default: true) - Exclude the original property from results',
      includeMetadata: 'boolean (optional, default: false) - Include search metadata',
    },
    response: {
      originalPropertyId: 'string - ID of the reference property',
      resultsCount: 'number - Number of similar properties found',
      similarityStats: 'object - Statistics about similarity scores',
      properties: 'array - Array of similar property objects',
      searchParameters: 'object - Parameters used for the search',
      timestamp: 'string - Response timestamp',
    },
    example: {
      request: {
        propertyId: 'property_123',
        limit: 3,
        scoreThreshold: 0.85,
        includeMetadata: true,
      },
      response: {
        originalPropertyId: 'property_123',
        resultsCount: 3,
        similarityStats: {
          averageScore: 0.89,
          highestScore: 0.94,
          lowestScore: 0.85,
        },
        properties: [
          {
            id: 'property_456',
            type: 'apartment',
            status: 'for_sale',
            price: 145000,
            currency: 'GTQ',
            bedrooms: 2,
            bathrooms: 2,
            area: 80,
            location: {
              country: 'Guatemala',
              level1: 'Guatemala',
              level2: 'Guatemala',
              neighborhood: 'Zona 15',
            },
            amenities: ['parking', 'security', 'gym'],
            isActive: true,
            isFeatured: true,
            isPremium: false,
            similarityScore: 0.94,
            metadata: {
              createdAt: '2025-06-20T10:00:00Z',
              updatedAt: '2025-06-23T15:30:00Z',
            },
          },
        ],
        searchParameters: {
          limit: 3,
          scoreThreshold: 0.85,
          excludeOriginal: true,
        },
        timestamp: '2025-06-23T17:30:00Z',
      },
    },
    useCases: [
      'Property recommendation systems',
      'Related properties on property detail pages',
      'Investment opportunity analysis',
      'Market comparison tools',
      'Real estate portfolio optimization',
    ],
    notes: [
      'Similarity is calculated using vector embeddings of property descriptions',
      'Higher scores indicate more similar properties',
      'Properties must be indexed in Qdrant to appear in results',
      'Similarity considers location, type, size, amenities, and description',
    ],
  });
}
