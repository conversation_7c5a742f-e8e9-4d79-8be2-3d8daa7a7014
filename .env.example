# Configuración de base de datos
DATABASE_URL=postgresql://user:password@localhost:5432/inmova

# APIs de AI
OPENAI_API_KEY=sk-...
ANTHROPIC_API_KEY=sk-ant-...
DEEPSEEK_API_KEY=sk-...
GROQ_API_KEY=gsk_...

# Convex
CONVEX_DEPLOYMENT=dev:...
NEXT_PUBLIC_CONVEX_URL=https://...

# Clerk Auth
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_...
CLERK_SECRET_KEY=sk_...

# Configuración de modelos AI (opcional - override de defaults)
# AI_MODEL_EMBEDDINGS=text-embedding-3-small
# AI_MODEL_CRITERIA_EXTRACTION=gpt-4o
# AI_MODEL_MAIN_AGENT=gpt-4o  
# AI_MODEL_RESPONSE_FORMATTER=gpt-4o-mini

# Ejemplos de modelos alternativos:
# AI_MODEL_CRITERIA_EXTRACTION=claude-3-5-sonnet-20241022
# AI_MODEL_MAIN_AGENT=claude-3-5-sonnet-20241022
# AI_MODEL_CRITERIA_EXTRACTION=gpt-4o-mini
# AI_MODEL_MAIN_AGENT=gpt-4o-mini

# Configuración de Pesos Semánticos (LEGACY - Sistema Multi-Componente DESCARTADO)
# ❌ NOTA: Sistema multi-componente descartado tras validación empírica (27 Jun 2025)
# ❌ Mantener solo para compatibilidad con código legacy
# ✅ Sistema actual: Embedding Simple (3.2x más rápido, 49% mejor precisión)
SEMANTIC_WEIGHT_LOCATION=0.5
SEMANTIC_WEIGHT_PROPERTY=0.25
SEMANTIC_WEIGHT_AMENITIES=0.15
SEMANTIC_WEIGHT_PRICE=0.1

# Configuración de búsqueda semántica
# ⭐ OPTIMIZADO: 0.25 validado empíricamente (+15.1% mejora)
SEARCH_SCORE_THRESHOLD=0.25

# ⚡ OPTIMIZACIONES DE RENDIMIENTO - DÍA 5
# Sistema de caché inteligente
ADAPTIVE_CACHE_TTL=1800000          # 30 minutos TTL para caché de análisis
ADAPTIVE_CACHE_SIZE=1000            # Máximo 1000 entradas en caché
PERFORMANCE_MONITORING=true         # Habilitar métricas de rendimiento

# Caché predictivo
PREDICTIVE_CACHE_ENABLED=true       # Habilitar pre-cálculo de consultas frecuentes

# Timeouts optimizados para OpenAI
OPENAI_TIMEOUT_PROFILE=3000         # 3s timeout para detección de perfil
OPENAI_TIMEOUT_NORMALIZE=2000       # 2s timeout para normalización
OPENAI_TIMEOUT_EMBEDDINGS=5000      # 5s timeout para embeddings

# Paralelización
PARALLEL_EMBEDDING_LIMIT=4          # Máximo 4 embeddings en paralelo

# Modelo específico para análisis de consultas (más rápido)
AI_MODEL_QUERY_ANALYSIS=gpt-4o-mini
