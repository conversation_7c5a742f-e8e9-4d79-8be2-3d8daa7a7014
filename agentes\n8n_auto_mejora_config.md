# CONFIGURACIÓN N8N - SISTEMA DE AUTO-MEJORA

## 🎯 OBJETIVO
Implementar un sistema que analice automáticamente las respuestas del agente y mejore el system message dinámicamente.

## 🔧 ARQUITECTURA

```
Usuario → Agente IA → Respuesta → Analizador → Base de Datos → System Message Dinámico
```

## 📋 NODOS REQUERIDOS EN N8N

### 1. **NODO ANALIZADOR DE CALIDAD** (Nuevo)
- **Tipo:** HTTP Request
- **Método:** POST
- **URL:** `https://tu-convex-url.convex.cloud/analyzeAgentResponse`
- **Headers:** 
  ```json
  {
    "Content-Type": "application/json",
    "Authorization": "Bearer TU_API_KEY"
  }
  ```
- **Body:**
  ```json
  {
    "chatId": "{{ $json.chatId }}",
    "userMessage": "{{ $json.userMessage }}",
    "agentResponse": "{{ $json.agentResponse }}",
    "messageId": "{{ $json.messageId }}"
  }
  ```

### 2. **NODO SYSTEM MESSAGE DINÁMICO** (Modificar existente)
- **Tipo:** HTTP Request
- **Método:** GET
- **URL:** `https://tu-convex-url.convex.cloud/getDynamicSystemMessage`
- **Headers:** 
  ```json
  {
    "Authorization": "Bearer TU_API_KEY"
  }
  ```

## 🔄 FLUJO MODIFICADO

### **FLUJO ACTUAL:**
```
1. Usuario envía mensaje
2. Agente IA responde
3. Respuesta se envía al usuario
```

### **FLUJO CON AUTO-MEJORA:**
```
1. Usuario envía mensaje
2. Obtener System Message Dinámico
3. Agente IA responde
4. Analizar calidad de respuesta (paralelo)
5. Respuesta se envía al usuario
6. Guardar análisis en base de datos
```

## 📝 PASOS DE IMPLEMENTACIÓN

### **PASO 1: Agregar Nodo Analizador**

1. **Crear nuevo nodo HTTP Request** después del nodo del agente IA
2. **Configurar como se muestra arriba**
3. **Conectar en paralelo** (no bloquear respuesta al usuario)

### **PASO 2: Modificar System Message**

1. **Ir al nodo del Agente IA**
2. **En "System Message"** cambiar de texto estático a:
   ```
   {{ $json.systemMessage }}
   ```
3. **Agregar nodo antes del agente** que obtenga el system message dinámico

### **PASO 3: Configurar Triggers**

1. **El analizador se ejecuta DESPUÉS** de cada respuesta
2. **El system message se obtiene ANTES** de cada respuesta
3. **Ambos usan la misma API key**

## 🎛️ CONFIGURACIÓN DETALLADA

### **Nodo: "Obtener System Message Dinámico"**
```json
{
  "name": "Get Dynamic System Message",
  "type": "n8n-nodes-base.httpRequest",
  "position": [400, 300],
  "parameters": {
    "url": "https://tu-convex-url.convex.cloud/getDynamicSystemMessage",
    "authentication": "genericCredentialType",
    "genericAuthType": "httpHeaderAuth",
    "httpHeaderAuth": {
      "name": "Authorization",
      "value": "Bearer TU_API_KEY"
    },
    "options": {}
  }
}
```

### **Nodo: "Analizar Respuesta del Agente"**
```json
{
  "name": "Analyze Agent Response",
  "type": "n8n-nodes-base.httpRequest",
  "position": [800, 400],
  "parameters": {
    "url": "https://tu-convex-url.convex.cloud/analyzeAgentResponse",
    "method": "POST",
    "authentication": "genericCredentialType",
    "genericAuthType": "httpHeaderAuth",
    "httpHeaderAuth": {
      "name": "Authorization", 
      "value": "Bearer TU_API_KEY"
    },
    "body": {
      "chatId": "={{ $json.chatId }}",
      "userMessage": "={{ $json.userMessage }}",
      "agentResponse": "={{ $json.agentResponse }}",
      "messageId": "={{ $json.messageId }}"
    },
    "options": {}
  }
}
```

### **Modificar Nodo Agente IA:**
```json
{
  "name": "AI Agent",
  "type": "@n8n/n8n-nodes-langchain.agent",
  "parameters": {
    "systemMessage": "={{ $json.systemMessage }}",
    "// ... resto de configuración"
  }
}
```

## 🔍 TESTING

### **Probar Análisis:**
```bash
curl -X POST https://tu-convex-url.convex.cloud/analyzeAgentResponse \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer TU_API_KEY" \
  -d '{
    "chatId": "test123",
    "userMessage": "Hola, busco una casa",
    "agentResponse": "Hola, ¿en qué puedo ayudarte?",
    "messageId": "msg123"
  }'
```

### **Probar System Message:**
```bash
curl -X GET https://tu-convex-url.convex.cloud/getDynamicSystemMessage \
  -H "Authorization: Bearer TU_API_KEY"
```

## 📊 MONITOREO

### **Dashboard de Mejoras** (Opcional)
- Crear endpoint para ver estadísticas
- Mostrar problemas más frecuentes
- Ver evolución de calidad

### **Logs a Revisar:**
- Problemas detectados más frecuentes
- Calidad promedio de respuestas
- Reglas auto-generadas activas

## ⚠️ CONSIDERACIONES

### **Performance:**
- El analizador NO debe bloquear la respuesta al usuario
- Ejecutar en paralelo o después de enviar respuesta

### **Límites:**
- Máximo 10 reglas auto-generadas activas
- Análisis solo de conversaciones importantes

### **Fallback:**
- Si falla el system message dinámico, usar v2.0 estático
- Si falla el analizador, continuar normalmente

## 🚀 PRÓXIMOS PASOS

1. **Implementar configuración básica**
2. **Probar con conversaciones reales**
3. **Ajustar reglas según resultados**
4. **Expandir análisis (sentiment, intención, etc.)**

---

## 📋 CHECKLIST DE IMPLEMENTACIÓN

- [ ] Crear nodo "Get Dynamic System Message"
- [ ] Modificar nodo "AI Agent" para usar system message dinámico
- [ ] Crear nodo "Analyze Agent Response"
- [ ] Conectar flujo correctamente
- [ ] Probar con mensaje de prueba
- [ ] Verificar que se guardan análisis en base de datos
- [ ] Confirmar que se generan reglas automáticamente
- [ ] Validar que system message se actualiza dinámicamente
