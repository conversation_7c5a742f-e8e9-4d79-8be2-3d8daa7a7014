import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import <PERSON><PERSON> from "stripe";

// Función para procesar webhooks de Stripe
export const processWebhook = mutation({
  args: {
    eventType: v.string(),
    data: v.any(),
  },
  handler: async (ctx, args) => {
    const { eventType, data } = args;

    switch (eventType) {
      case "checkout.session.completed":
        await handleCheckoutCompleted(ctx, data);
        break;
      case "customer.subscription.created":
        await handleSubscriptionCreated(ctx, data);
        break;
      case "customer.subscription.updated":
        await handleSubscriptionUpdated(ctx, data);
        break;
      case "customer.subscription.deleted":
        await handleSubscriptionCanceled(ctx, data);
        break;
      case "invoice.payment_succeeded":
        await handleInvoicePaymentSucceeded(ctx, data);
        break;
      case "invoice.payment_failed":
        await handleInvoicePaymentFailed(ctx, data);
        break;
      default:
        console.log(`Evento no manejado: ${eventType}`);
    }
  },
});

// Manejar checkout completado
async function handleCheckoutCompleted(ctx: any, session: any) {
  console.log("💳 Procesando checkout completado:", session.id);

  // Buscar usuario por email (múltiples fuentes)
  const customerEmail = session.customer_details?.email ||
                       session.customer_email ||
                       session.metadata?.userEmail;

  if (!customerEmail) {
    console.error("❌ No se encontró email en la sesión de checkout");
    console.log("📋 Datos de sesión disponibles:", {
      customer_details: session.customer_details,
      customer_email: session.customer_email,
      metadata: session.metadata
    });
    return;
  }

  console.log("📧 Email encontrado:", customerEmail);

  let user = await ctx.db
    .query("users")
    .filter((q: any) => q.eq(q.field("email"), customerEmail))
    .first();

  // Si no se encuentra por email principal, buscar por emailAddresses de Clerk
  if (!user) {
    const allUsers = await ctx.db.query("users").collect();
    user = allUsers.find((u: any) => {
      if (u.emailAddresses) {
        return u.emailAddresses.some((emailObj: any) => 
          emailObj.emailAddress === customerEmail
        );
      }
      return false;
    });
  }

  if (!user) {
    console.error("❌ Usuario no encontrado para el email:", customerEmail);
    return;
  }

  console.log("✅ Usuario encontrado:", user.email, "ID:", user.tokenIdentifier);

  // Extraer información del plan desde metadata o line items
  const plan = session.metadata?.plan || "premium";
  const { credits, maxProperties } = getPlanLimits(plan);

  // Crear o actualizar suscripción
  await processSubscription(ctx, {
    userId: user.tokenIdentifier,
    stripeSessionId: session.id,
    stripeCustomerId: session.customer,
    stripeSubscriptionId: session.subscription,
    plan,
    credits,
    maxProperties,
    status: "active"
  });
}

// Manejar suscripción creada
async function handleSubscriptionCreated(ctx: any, subscription: any) {
  console.log("🎯 Procesando suscripción creada:", subscription.id);

  // ✅ SKIP: No necesitamos procesar este evento porque checkout.session.completed
  // ya maneja toda la lógica de suscripción correctamente
  console.log("⏭️ Saltando procesamiento - checkout.session.completed ya manejó la suscripción");
  return;
}

// Manejar suscripción actualizada
async function handleSubscriptionUpdated(ctx: any, subscription: any) {
  const existingSubscription = await ctx.db
    .query("subscriptions")
    .withIndex("stripeSubscriptionId", (q: any) => 
      q.eq("stripeSubscriptionId", subscription.id)
    )
    .first();

  if (existingSubscription) {
    await ctx.db.patch(existingSubscription._id, {
      status: subscription.status,
      currentPeriodStart: subscription.current_period_start * 1000,
      currentPeriodEnd: subscription.current_period_end * 1000,
      updatedAt: Date.now(),
    });
  }
}

// Manejar suscripción cancelada
async function handleSubscriptionCanceled(ctx: any, subscription: any) {
  const existingSubscription = await ctx.db
    .query("subscriptions")
    .withIndex("stripeSubscriptionId", (q: any) => 
      q.eq("stripeSubscriptionId", subscription.id)
    )
    .first();

  if (existingSubscription) {
    await ctx.db.patch(existingSubscription._id, {
      status: "canceled",
      updatedAt: Date.now(),
    });
  }
}

// Manejar pago de factura exitoso
async function handleInvoicePaymentSucceeded(ctx: any, invoice: any) {
  console.log("💰 Pago exitoso para factura:", invoice.id);
  
  const subscription = await ctx.db
    .query("subscriptions")
    .withIndex("stripeSubscriptionId", (q: any) => 
      q.eq("stripeSubscriptionId", invoice.subscription)
    )
    .first();

  if (subscription) {
    // Renovar créditos si es necesario
    const plan = subscription.plan;
    const { credits } = getPlanLimits(plan);
    
    await ctx.db.patch(subscription._id, {
      credits,
      creditsUsed: 0, // Resetear créditos usados
      status: "active",
      updatedAt: Date.now(),
    });
  }
}

// Manejar pago de factura fallido
async function handleInvoicePaymentFailed(ctx: any, invoice: any) {
  console.log("❌ Pago fallido para factura:", invoice.id);
  
  const subscription = await ctx.db
    .query("subscriptions")
    .withIndex("stripeSubscriptionId", (q: any) => 
      q.eq("stripeSubscriptionId", invoice.subscription)
    )
    .first();

  if (subscription) {
    await ctx.db.patch(subscription._id, {
      status: "past_due",
      updatedAt: Date.now(),
    });
  }
}

// Función para crear sesión de checkout de Stripe
export const createCheckoutSession = mutation({
  args: {
    plan: v.union(v.literal("premium")),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autorizado");
    }

    // Obtener información del usuario
    const user = await ctx.db
      .query("users")
      .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.tokenIdentifier))
      .first();

    if (!user) {
      throw new Error("Usuario no encontrado");
    }

    // En desarrollo, retornar URL simulada
    if (process.env.NODE_ENV === "development") {
      return {
        checkoutUrl: `https://checkout.stripe.com/pay/demo?plan=${args.plan}&user=${user.email}`,
        message: "URL de desarrollo - en producción se conectará con Stripe real"
      };
    }

    // En producción, se creará la sesión real desde la API route
    return {
      requiresApi: true,
      plan: args.plan,
      userEmail: user.email
    };
  },
});

// Función para verificar límites antes de acciones
export const checkUsageLimits = query({
  args: {
    action: v.string(), // "create_property" | "receive_contact"
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return { allowed: false, reason: "No autorizado" };
    }

    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("userId", (q) => q.eq("userId", identity.subject))
      .first();

    if (!subscription || subscription.status !== "active") {
      // Plan gratuito
      if (args.action === "create_property") {
        const userProperties = await ctx.db
          .query("properties")
          .withIndex("by_owner", (q) => q.eq("ownerId", identity.subject))
          .collect();

        if (userProperties.length >= 5) {
          return { 
            allowed: false, 
            reason: "Has alcanzado el límite de 5 propiedades del plan gratuito",
            upgrade: true
          };
        }
      }
      
      if (args.action === "receive_contact") {
        if (subscription && subscription.creditsUsed >= 10) {
          return { 
            allowed: false, 
            reason: "Has agotado tus 10 consultas gratuitas",
            upgrade: true
          };
        }
      }
      
      return { allowed: true };
    }

    // Plan pagado
    if (args.action === "create_property") {
      const userProperties = await ctx.db
        .query("properties")
        .withIndex("by_owner", (q) => q.eq("ownerId", identity.subject))
        .collect();

      if (userProperties.length >= subscription.maxProperties) {
        return { 
          allowed: false, 
          reason: `Has alcanzado el límite de ${subscription.maxProperties} propiedades de tu plan`,
          upgrade: subscription.plan !== "premium"
        };
      }
    }

    if (args.action === "receive_contact") {
      if (subscription.creditsUsed >= subscription.credits) {
        return { 
          allowed: false, 
          reason: "Has agotado todos tus créditos de consultas",
          upgrade: subscription.plan !== "premium"
        };
      }
    }

    return { allowed: true };
  },
});

// Función para procesar suscripción directamente
export const processDirectSubscription = mutation({
  args: {
    userId: v.string(),
    plan: v.union(v.literal("free"), v.literal("premium")),
    credits: v.number(),
    maxProperties: v.number(),
    stripeSessionId: v.optional(v.string()),
    stripeData: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    return await processSubscription(ctx, args);
  },
});

// Helpers internos
async function processSubscription(ctx: any, data: any) {
  const { userId, plan, credits, maxProperties } = data;

  console.log("🎯 Procesando suscripción:", { userId, plan, credits, maxProperties });

  const existingSubscription = await ctx.db
    .query("subscriptions")
    .withIndex("userId", (q: any) => q.eq("userId", userId))
    .first();

  if (existingSubscription) {
    console.log("🔄 Actualizando suscripción existente");
    await ctx.db.patch(existingSubscription._id, {
      plan,
      status: data.status || "active",
      credits,
      maxProperties,
      stripeSubscriptionId: data.stripeSubscriptionId,
      stripeCustomerId: data.stripeCustomerId,
      currentPeriodStart: data.currentPeriodStart,
      currentPeriodEnd: data.currentPeriodEnd,
      updatedAt: Date.now(),
    });
  } else {
    console.log("✨ Creando nueva suscripción");
    await ctx.db.insert("subscriptions", {
      userId,
      plan,
      status: data.status || "active",
      credits,
      maxProperties,
      creditsUsed: 0,
      propertiesCount: 0,
      stripeSubscriptionId: data.stripeSubscriptionId,
      stripeCustomerId: data.stripeCustomerId,
      currentPeriodStart: data.currentPeriodStart,
      currentPeriodEnd: data.currentPeriodEnd,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
  }

  console.log("🎉 Suscripción procesada exitosamente");
  return { success: true, plan, credits, maxProperties };
}

function getPlanLimits(plan: string) {
  switch (plan) {
    case "premium":
      return { credits: 300, maxProperties: 100 };
    case "free":
      return { credits: 10, maxProperties: 5 };
    default:
      return { credits: 10, maxProperties: 5 }; // Default a free
  }
}

function determinePlanFromSubscription(subscription: any) {
  // Lógica para determinar el plan basado en el price_id o metadata
  const priceId = subscription.items?.data?.[0]?.price?.id;

  if (priceId === process.env.STRIPE_PRICE_PREMIUM) {
    return "premium";
  }

  // Solo tenemos Free y Premium - default a premium para suscripciones pagadas
  return "premium";
}

async function findUserByEmail(ctx: any, email: string) {
  let user = await ctx.db
    .query("users")
    .filter((q: any) => q.eq(q.field("email"), email))
    .first();

  if (!user) {
    const allUsers = await ctx.db.query("users").collect();
    user = allUsers.find((u: any) => {
      if (u.emailAddresses) {
        return u.emailAddresses.some((emailObj: any) => 
          emailObj.emailAddress === email
        );
      }
      return false;
    });
  }

  return user;
}

async function getCustomerFromStripe(customerId: string) {
  try {
    // Usar importación estática de Stripe
    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: '2023-10-16',
    });

    const customer = await stripe.customers.retrieve(customerId);

    if (customer.deleted) {
      console.error("❌ Cliente eliminado en Stripe:", customerId);
      return { email: null };
    }

    return {
      email: customer.email,
      name: customer.name,
      id: customer.id
    };
  } catch (error) {
    console.error("❌ Error obteniendo cliente de Stripe:", error);
    return { email: null };
  }
}

