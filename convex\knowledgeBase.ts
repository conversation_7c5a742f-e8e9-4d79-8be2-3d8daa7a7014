import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { api } from "./_generated/api";

// Generar ID único para consideración
function generateConsiderationId(): string {
  return `consideration_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Limpiar y normalizar texto de consideración
function cleanConsiderationText(text: string): string {
  return text.trim()
    .replace(/\s+/g, ' ') // Normalizar espacios
    .replace(/[^\w\s\-.,;:()]/g, '') // Remover caracteres especiales excepto puntuación básica
    .substring(0, 1000); // Limitar longitud
}

// Obtener todas las consideraciones activas
export const getActiveConsiderations = query({
  args: {
    category: v.optional(v.string()),
    priority: v.optional(v.union(
      v.literal("high"),
      v.literal("medium"), 
      v.literal("low")
    )),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db.query("knowledgeBase")
      .withIndex("by_active", (q) => q.eq("active", true));

    if (args.category) {
      query = query.filter((q: any) => q.eq(q.field("category"), args.category));
    }

    if (args.priority) {
      query = query.filter((q: any) => q.eq(q.field("priority"), args.priority));
    }

    const considerations = await query
      .order("desc")
      .take(args.limit || 100);

    return considerations;
  },
});

// Obtener consideraciones por frecuencia (más detectadas primero)
export const getConsiderationsByFrequency = query({
  args: {
    category: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let considerations = await ctx.db.query("knowledgeBase")
      .withIndex("by_active", (q) => q.eq("active", true))
      .collect();

    if (args.category) {
      considerations = considerations.filter(c => c.category === args.category);
    }

    // Ordenar por frecuencia descendente
    considerations.sort((a, b) => b.frequency - a.frequency);

    return considerations.slice(0, args.limit || 50);
  },
});

// Verificar si una consideración similar ya existe
export const checkDuplicateConsideration = query({
  args: {
    consideration: v.string(),
    threshold: v.optional(v.number()), // Umbral de similitud (0.8 por defecto)
  },
  handler: async (ctx, args) => {
    const cleanText = cleanConsiderationText(args.consideration);
    const threshold = args.threshold || 0.8;
    
    const existingConsiderations = await ctx.db.query("knowledgeBase")
      .withIndex("by_active", (q) => q.eq("active", true))
      .collect();

    // Buscar consideraciones similares usando similitud de texto simple
    for (const existing of existingConsiderations) {
      const similarity = calculateTextSimilarity(cleanText, existing.consideration);
      if (similarity >= threshold) {
        return {
          isDuplicate: true,
          existingConsideration: existing,
          similarity: similarity
        };
      }
    }

    return {
      isDuplicate: false,
      similarity: 0
    };
  },
});

// Función auxiliar para calcular similitud de texto
function calculateTextSimilarity(text1: string, text2: string): number {
  const words1 = text1.toLowerCase().split(/\s+/);
  const words2 = text2.toLowerCase().split(/\s+/);
  
  const set1 = new Set(words1);
  const set2 = new Set(words2);
  
  const intersection = new Set([...set1].filter(x => set2.has(x)));
  const union = new Set([...set1, ...set2]);
  
  return intersection.size / union.size; // Jaccard similarity
}

// Crear nueva consideración
export const createConsideration = mutation({
  args: {
    consideration: v.string(),
    category: v.optional(v.string()),
    priority: v.union(
      v.literal("high"),
      v.literal("medium"),
      v.literal("low")
    ),
    sourceAnalysis: v.optional(v.array(v.string())),
    skipDuplicateCheck: v.optional(v.boolean()),
  },
  handler: async (ctx, args): Promise<{
    success: boolean;
    action: string;
    considerationId: string;
    id: any;
  }> => {
    const now = new Date().toISOString();
    const cleanText = cleanConsiderationText(args.consideration);

    if (!cleanText || cleanText.length < 10) {
      throw new Error("La consideración debe tener al menos 10 caracteres");
    }

    // Verificar duplicados si no se especifica omitir la verificación
    if (!args.skipDuplicateCheck) {
      const duplicateCheck = await ctx.runQuery(api.knowledgeBase.checkDuplicateConsideration, {
        consideration: cleanText
      });

      if (duplicateCheck.isDuplicate) {
        // Si existe una similar, incrementar frecuencia en lugar de crear nueva
        const existingId = duplicateCheck.existingConsideration!._id;
        await ctx.db.patch(existingId, {
          frequency: duplicateCheck.existingConsideration!.frequency + 1,
          lastDetected: now,
          sourceAnalysis: [
            ...duplicateCheck.existingConsideration!.sourceAnalysis,
            ...(args.sourceAnalysis || [])
          ],
          updatedAt: now,
        });

        return {
          success: true,
          action: "updated_existing",
          considerationId: duplicateCheck.existingConsideration!.considerationId,
          id: existingId
        };
      }
    }

    // Crear nueva consideración
    const considerationId = generateConsiderationId();

    const newConsiderationId = await ctx.db.insert("knowledgeBase", {
      considerationId,
      consideration: cleanText,
      category: args.category || "general",
      priority: args.priority,
      firstDetected: now,
      lastDetected: now,
      frequency: 1,
      active: true,
      validated: false,
      sourceAnalysis: args.sourceAnalysis || [],
      createdAt: now,
      updatedAt: now,
    });

    return {
      success: true,
      action: "created_new",
      considerationId,
      id: newConsiderationId
    };
  },
});

// Crear múltiples consideraciones (para N8N)
export const createMultipleConsiderations = mutation({
  args: {
    considerations: v.array(v.object({
      consideration: v.string(),
      category: v.optional(v.string()),
      priority: v.union(
        v.literal("high"),
        v.literal("medium"),
        v.literal("low")
      ),
      sourceAnalysis: v.optional(v.array(v.string())),
    })),
    skipDuplicateCheck: v.optional(v.boolean()),
  },
  handler: async (ctx, args): Promise<{
    success: boolean;
    totalProcessed: number;
    successCount: number;
    errorCount: number;
    createdCount: number;
    updatedCount: number;
    results: any[];
  }> => {
    const results: any[] = [];

    for (const considerationData of args.considerations) {
      try {
        const result = await ctx.runMutation(api.knowledgeBase.createConsideration, {
          ...considerationData,
          skipDuplicateCheck: args.skipDuplicateCheck
        });
        results.push({
          ...result,
          originalText: considerationData.consideration
        });
      } catch (error: unknown) {
        results.push({
          success: false,
          error: error instanceof Error ? error.message : "Error desconocido",
          originalText: considerationData.consideration
        });
      }
    }

    const successCount = results.filter(r => r.success).length;
    const errorCount = results.filter(r => !r.success).length;
    const updatedCount = results.filter(r => r.success && r.action === "updated_existing").length;
    const createdCount = results.filter(r => r.success && r.action === "created_new").length;

    return {
      success: true,
      totalProcessed: args.considerations.length,
      successCount,
      errorCount,
      createdCount,
      updatedCount,
      results
    };
  },
});

// Actualizar consideración existente
export const updateConsideration = mutation({
  args: {
    id: v.id("knowledgeBase"),
    consideration: v.optional(v.string()),
    category: v.optional(v.string()),
    priority: v.optional(v.union(
      v.literal("high"),
      v.literal("medium"),
      v.literal("low")
    )),
    active: v.optional(v.boolean()),
    validated: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const { id, ...updates } = args;
    const now = new Date().toISOString();

    // Si se actualiza el texto de consideración, limpiarlo
    if (updates.consideration) {
      updates.consideration = cleanConsiderationText(updates.consideration);
    }

    await ctx.db.patch(id, {
      ...updates,
      updatedAt: now,
    });

    return await ctx.db.get(id);
  },
});

// Desactivar consideración
export const deactivateConsideration = mutation({
  args: {
    id: v.id("knowledgeBase"),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.id, {
      active: false,
      updatedAt: new Date().toISOString(),
    });

    return await ctx.db.get(args.id);
  },
});

// Actualizar consideraciones existentes para convertirlas en instrucciones directas
export const updateExistingConsiderations = mutation({
  handler: async (ctx) => {
    const allConsiderations = await ctx.db.query("knowledgeBase").collect();
    const updates = [];

    for (const consideration of allConsiderations) {
      let newText = consideration.consideration;

      // Convertir sugerencias de implementación en instrucciones directas
      if (newText.includes("Implementar un sistema de verificación automática de datos de contacto")) {
        newText = "Verifica los datos de contacto para evitar repeticiones innecesarias";
      } else if (newText.includes("Mejorar la gestión de la información proporcionada por el cliente")) {
        newText = "Recuerda la información proporcionada por el cliente para evitar solicitar la misma información varias veces";
      } else if (newText.includes("Mejorar la rapidez de respuesta inicial")) {
        newText = "Responde rápidamente para evitar frustraciones en el cliente";
      } else if (newText.toLowerCase().includes("implementar") || newText.toLowerCase().includes("mejorar") || newText.toLowerCase().includes("sistema")) {
        // Convertir otras sugerencias de implementación en instrucciones directas
        newText = newText
          .replace(/^Implementar\s+/i, "")
          .replace(/^Mejorar\s+/i, "")
          .replace(/un sistema de\s+/i, "")
          .replace(/la gestión de\s+/i, "")
          .replace(/para\s+/i, "");

        // Agregar verbo imperativo al inicio si no lo tiene
        if (!newText.match(/^(Verifica|Confirma|Pregunta|Recuerda|Responde|Evita|Asegúrate|Solicita)/i)) {
          newText = "Verifica " + newText.toLowerCase();
        }
      }

      if (newText !== consideration.consideration) {
        await ctx.db.patch(consideration._id, {
          consideration: newText,
          updatedAt: new Date().toISOString(),
        });
        updates.push({
          id: consideration._id,
          old: consideration.consideration,
          new: newText
        });
      }
    }

    return {
      success: true,
      totalConsiderations: allConsiderations.length,
      updatedCount: updates.length,
      updates: updates
    };
  },
});

// Obtener estadísticas de la base de conocimiento
export const getKnowledgeBaseStats = query({
  handler: async (ctx) => {
    const allConsiderations = await ctx.db.query("knowledgeBase").collect();

    const stats = {
      total: allConsiderations.length,
      active: allConsiderations.filter(c => c.active).length,
      inactive: allConsiderations.filter(c => !c.active).length,
      validated: allConsiderations.filter(c => c.validated).length,
      byCategory: {} as Record<string, number>,
      byPriority: {
        high: allConsiderations.filter(c => c.priority === "high").length,
        medium: allConsiderations.filter(c => c.priority === "medium").length,
        low: allConsiderations.filter(c => c.priority === "low").length,
      },
      averageFrequency: allConsiderations.reduce((sum, c) => sum + c.frequency, 0) / allConsiderations.length || 0,
      mostFrequent: allConsiderations.sort((a, b) => b.frequency - a.frequency).slice(0, 5),
    };

    // Contar por categoría
    allConsiderations.forEach(c => {
      const category = c.category || "sin_categoria";
      stats.byCategory[category] = (stats.byCategory[category] || 0) + 1;
    });

    return stats;
  },
});
