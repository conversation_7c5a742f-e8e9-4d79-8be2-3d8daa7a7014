"use client"

import clsx from 'clsx'
import {
  Banknote,
  Folder,
  HomeIcon,
  Settings,
  LucideIcon,
  Building2,
  Mail,
  Shield,
  Calendar,
  Heart,
  Search
} from "lucide-react"
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { Badge } from "@/components/ui/badge"
import { TrialStatusWidget } from "@/components/trial/trial-banner"

interface NavItem {
  label: string;
  href: string;
  icon: LucideIcon;
  showBadge?: boolean;
  showAgendaBadge?: boolean;
  showFavoritesBadge?: boolean;
  adminOnly?: boolean;
  buyerOnly?: boolean;
  sellerAgentOnly?: boolean;
}

const navItems: NavItem[] = [
  {
    label: "Panel Principal",
    href: "/dashboard",
    icon: HomeIcon
  },
  // Opciones para Buyers
  {
    label: "Explorar Propiedades",
    href: "/properties",
    icon: Search,
    buyerOnly: true
  },
  {
    label: "Mis Favoritos",
    href: "/dashboard/favorites",
    icon: Heart,
    buyerOnly: true,
    showFavoritesBadge: true
  },
  // Opciones para Sellers/Agents
  {
    label: "Mis Propiedades",
    href: "/dashboard/properties",
    icon: Building2,
    sellerAgentOnly: true
  },
  {
    label: "Mensajes",
    href: "/dashboard/messages",
    icon: Mail,
    showBadge: true,
    sellerAgentOnly: true
  },
  {
    label: "Agenda",
    href: "/dashboard/agenda",
    icon: Calendar,
    showAgendaBadge: true,
    sellerAgentOnly: true
  },
  {
    label: "Suscripción",
    href: "/dashboard/finance",
    icon: Banknote,
    sellerAgentOnly: true
  },
  // Opciones para Admin
  {
    label: "Administración",
    href: "/dashboard/admin",
    icon: Shield,
    adminOnly: true
  },
  // Opciones para todos
  {
    label: "Configuración",
    href: "/dashboard/settings",
    icon: Settings
  }
]

export default function DashboardSideBar() {
  const pathname = usePathname();
  const messageStats = useQuery(api.messages.getMessageStats, {});
  const agendaStats = useQuery(api.appointments.getAgendaNotificationStats, {});
  const favoritesCount = useQuery(api.properties.getUserFavoritesCount, {});

  // Obtener datos del usuario actual para verificar rol
  const currentUser = useQuery(api.users.getCurrentUser);

  // DEBUG: Log para verificar datos del usuario (temporal)
  // console.log('🔍 DEBUG Sidebar - Usuario actual:', {
  //   currentUser,
  //   role: currentUser?.role,
  //   hasUser: !!currentUser,
  //   userKeys: currentUser ? Object.keys(currentUser) : []
  // });

  // Filtrar items del menú basado en permisos y roles
  const filteredNavItems = navItems.filter(item => {
    // Admin siempre tiene acceso a todo
    if (currentUser?.role === 'admin') {
      return true;
    }

    // Items solo para admin
    if (item.adminOnly) {
      return false;
    }

    // Items solo para buyers
    if (item.buyerOnly) {
      return currentUser?.role === 'buyer';
    }

    // Items solo para seller/agent
    if (item.sellerAgentOnly) {
      return currentUser?.role === 'seller' || currentUser?.role === 'agent';
    }

    // Resto de items disponibles para todos
    return true;
  });
  
  return (
    <div className="min-[1024px]:block hidden w-64 border-r h-full bg-background">
      <div className="flex h-full flex-col">
        <div className="flex h-[3.45rem] items-center border-b px-4">
          <Link prefetch={true} className="flex items-center gap-2 font-semibold hover:cursor-pointer" href="/">
            <div className="w-6 h-6 bg-blue-600 rounded flex items-center justify-center mr-2">
              <HomeIcon className="h-4 w-4 text-white" />
            </div>
                          <span>Inmova</span>
          </Link>
        </div>

        <div className="p-4">
          {/* Trial Widget - Solo para seller/agent */}
          {(currentUser?.role === 'seller' || currentUser?.role === 'agent') && (
            <TrialStatusWidget />
          )}
        </div>

        <nav className="flex-1 space-y-1 p-4">
          {filteredNavItems.map((item: any) => (
            <Link
              key={item.href}
              prefetch={true}
              href={item.href}
              className={clsx(
                "flex items-center gap-2 rounded-lg px-3 py-2 text-sm transition-colors relative",
                pathname === item.href
                  ? "bg-blue-50 text-blue-600 hover:bg-blue-100"
                  : "text-muted-foreground hover:bg-blue-50 hover:text-blue-600"
              )}
            >
              <item.icon className="h-4 w-4" />
              {item.label}
              {item.adminOnly && currentUser?.role === 'admin' && (
                <Badge 
                  variant="secondary" 
                  className="ml-auto h-5 px-2 text-xs bg-orange-100 text-orange-800"
                >
                  Admin
                </Badge>
              )}
              {item.showBadge && messageStats && messageStats.unreadCount > 0 && (
                <Badge
                  variant="destructive"
                  className="ml-auto h-5 w-5 flex items-center justify-center p-0 text-xs bg-red-500 hover:bg-red-600"
                >
                  {messageStats.unreadCount > 99 ? '99+' : messageStats.unreadCount}
                </Badge>
              )}
              {item.showAgendaBadge && agendaStats && agendaStats.totalNotifications > 0 && (
                <Badge
                  variant="destructive"
                  className="ml-auto h-5 w-5 flex items-center justify-center p-0 text-xs bg-orange-500 hover:bg-orange-600"
                >
                  {agendaStats.totalNotifications > 99 ? '99+' : agendaStats.totalNotifications}
                </Badge>
              )}
              {item.showFavoritesBadge && favoritesCount !== undefined && favoritesCount > 0 && (
                <Badge
                  variant="secondary"
                  className="ml-auto h-5 w-5 flex items-center justify-center p-0 text-xs bg-pink-100 text-pink-800 hover:bg-pink-200"
                >
                  {favoritesCount > 99 ? '99+' : favoritesCount}
                </Badge>
              )}
            </Link>
          ))}
        </nav>
      </div>
    </div>
  )
}
