import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { api } from "./_generated/api";

// Función para obtener la suscripción del usuario
export const getUserSubscription = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
    }

    // Buscar suscripción activa del usuario
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("userId", (q) => q.eq("userId", identity.subject))
      .first();

    // Contar propiedades reales del usuario (solo las publicadas, no drafts)
    const userProperties = await ctx.db
      .query("properties")
      .withIndex("by_owner", (q) => q.eq("ownerId", identity.subject))
      .filter((q: any) => q.neq(q.field("status"), "draft"))
      .collect();

    const realPropertiesCount = userProperties.length;

    if (!subscription) {
      // Devolver plan gratuito por defecto
      return {
        id: "free-plan",
        status: "active",
        plan: "free",
        credits: 10,
        creditsUsed: 0,
        maxProperties: 5,
        propertiesCount: realPropertiesCount,
        isPremium: false,
        currentPeriodEnd: null,
        amount: 0,
        isTrialActive: false,
        trialEndDate: null,
        hasUsedTrial: false,
      };
    }

    return {
      id: subscription._id,
      status: subscription.status,
      plan: subscription.plan,
      credits: subscription.credits,
      creditsUsed: subscription.creditsUsed,
      maxProperties: subscription.maxProperties,
      propertiesCount: realPropertiesCount, // Usar el conteo real
      isPremium: subscription.plan !== "free" || subscription.isTrialActive,
      currentPeriodEnd: subscription.currentPeriodEnd,
      amount: subscription.amount,
      isTrialActive: subscription.isTrialActive || false,
      trialEndDate: subscription.trialEndDate,
      trialStartDate: subscription.trialStartDate,
      hasUsedTrial: subscription.hasUsedTrial || false,
      trialDaysGranted: subscription.trialDaysGranted,
    };
  },
});

// Función para obtener el estado de suscripción del usuario
export const getUserSubscriptionStatus = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return {
        hasActiveSubscription: false,
        plan: "free",
        isPremium: false
      };
    }

    // Buscar suscripción activa del usuario (duplicar lógica para evitar llamada a query)
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("userId", (q) => q.eq("userId", identity.subject))
      .first();

    if (!subscription || subscription.status !== "active") {
      return {
        hasActiveSubscription: false,
        plan: "free",
        isPremium: false
      };
    }

    let plan = "free";
    let isPremium = false;

    if (subscription.amount === 99) {
      plan = "premium";
      isPremium = true;
    }
    
    return {
      hasActiveSubscription: subscription.status === "active",
      plan,
      isPremium
    };
  },
});

// Función para consumir créditos
export const consumeCredits = mutation({
  args: {
    amount: v.number(),
    description: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    // Obtener usuario
    const user = await ctx.db
      .query("users")
      .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.subject))
      .unique();

    if (!user) {
      throw new Error("Usuario no encontrado");
    }

    // Obtener créditos actuales
    const currentCredits = parseInt(user.credits || "0");
    
    if (currentCredits < args.amount) {
      throw new Error("Créditos insuficientes");
    }

    // Restar créditos
    const newCredits = currentCredits - args.amount;
    await ctx.db.patch(user._id, {
      credits: newCredits.toString()
    });

    // Registrar el consumo (opcional: crear tabla de historial)
    return {
      success: true,
      remainingCredits: newCredits,
      consumed: args.amount
    };
  },
});

// Función para verificar si el usuario puede publicar más propiedades
export const canPublishProperty = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return false;
    }

    // Obtener suscripción (duplicar lógica)
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("userId", (q) => q.eq("userId", identity.subject))
      .first();

    let maxProperties = 5; // Plan gratuito por defecto

    if (subscription && subscription.status === "active") {
      if (subscription.amount === 29) {
        maxProperties = 50;
      } else if (subscription.amount === 99) {
        maxProperties = 999999;
      }
    }

    // Contar propiedades activas del usuario
    const propertiesCount = await ctx.db
      .query("properties")
      .withIndex("by_owner", (q) => q.eq("ownerId", identity.subject))
      .filter((q: any) => q.neq(q.field("status"), "draft"))
      .collect();

    return propertiesCount.length < maxProperties;
  },
});

// Función para obtener estadísticas de uso
export const getUsageStats = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
    }

    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("userId", (q) => q.eq("userId", identity.subject))
      .first();

    // Contar propiedades del usuario
    const userProperties = await ctx.db
      .query("properties")
      .withIndex("by_owner", (q) => q.eq("ownerId", identity.subject))
      .collect();

    const propertiesCount = userProperties.length;

    if (!subscription) {
      return {
        plan: "free",
        propertiesUsed: propertiesCount,
        propertiesLimit: 5,
        creditsUsed: 0,
        creditsLimit: 10,
        currentCredits: 10,
        percentage: {
          properties: (propertiesCount / 5) * 100,
          credits: 0,
        },
      };
    }

    const currentCredits = subscription.credits - subscription.creditsUsed;

    return {
      plan: subscription.plan,
      propertiesUsed: propertiesCount,
      propertiesLimit: subscription.maxProperties,
      creditsUsed: subscription.creditsUsed,
      creditsLimit: subscription.credits,
      currentCredits,
      percentage: {
        properties: (propertiesCount / subscription.maxProperties) * 100,
        credits: (subscription.creditsUsed / subscription.credits) * 100,
      },
    };
  },
});

// Función para agregar créditos (solo para desarrollo/testing)
export const addCreditsToUser = mutation({
  args: {
    creditsToAdd: v.number(),
    reason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    // Buscar suscripción existente
    let subscription = await ctx.db
      .query("subscriptions")
      .withIndex("userId", (q) => q.eq("userId", identity.subject))
      .first();

    const now = Date.now();

    if (!subscription) {
      // Crear suscripción si no existe
      const subscriptionId = await ctx.db.insert("subscriptions", {
        userId: identity.subject,
        plan: "free",
        status: "active",
        credits: 10 + args.creditsToAdd,
        creditsUsed: 0,
        maxProperties: 5,
        propertiesCount: 0,
        createdAt: now,
        updatedAt: now,
      });

      return {
        success: true,
        message: `Suscripción creada con ${10 + args.creditsToAdd} créditos`,
        totalCredits: 10 + args.creditsToAdd,
        creditsAdded: args.creditsToAdd,
      };
    } else {
      // Agregar créditos a la suscripción existente
      const newTotalCredits = subscription.credits + args.creditsToAdd;
      
      await ctx.db.patch(subscription._id, {
        credits: newTotalCredits,
        updatedAt: now,
      });

      return {
        success: true,
        message: `Se agregaron ${args.creditsToAdd} créditos. Total: ${newTotalCredits}`,
        totalCredits: newTotalCredits,
        creditsAdded: args.creditsToAdd,
        reason: args.reason || "Créditos adicionales",
      };
    }
  },
});

// Función para resetear créditos usados (solo para desarrollo/testing)
export const resetUsedCredits = mutation({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("userId", (q) => q.eq("userId", identity.subject))
      .first();

    if (!subscription) {
      throw new Error("No tienes suscripción");
    }

    await ctx.db.patch(subscription._id, {
      creditsUsed: 0,
      updatedAt: Date.now(),
    });

    return {
      success: true,
      message: `Créditos usados reseteados. Ahora tienes ${subscription.credits} créditos disponibles`,
      totalCredits: subscription.credits,
      creditsUsed: 0,
    };
  },
});

// 🧹 NUEVA: Limpieza completa de créditos consumidos (SOLO ADMINISTRADORES)
export const cleanupAllCreditConsumptions = mutation({
  args: {
    confirmAction: v.string(), // Debe ser exactamente "CONFIRMAR_LIMPIAR_CREDITOS"
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    // Verificar que el usuario es admin
    const currentUser = await ctx.db
      .query("users")
      .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.subject))
      .first();

    if (!currentUser || currentUser.role !== 'admin') {
      throw new Error("Solo los administradores pueden limpiar créditos");
    }

    // Verificación de seguridad
    if (args.confirmAction !== "CONFIRMAR_LIMPIAR_CREDITOS") {
      throw new Error("Confirmación requerida: debe escribir exactamente 'CONFIRMAR_LIMPIAR_CREDITOS'");
    }

    console.log("🧹 Iniciando limpieza completa de créditos consumidos...");

    let deletedCounts = {
      creditConsumptions: 0,
      transactions: 0,
      subscriptionsReset: 0
    };

    try {
      // 1. Eliminar TODOS los registros de creditConsumptions
      console.log("🗑️ Eliminando registros de creditConsumptions...");
      const allConsumptions = await ctx.db.query("creditConsumptions").collect();
      for (const consumption of allConsumptions) {
        await ctx.db.delete(consumption._id);
      }
      deletedCounts.creditConsumptions = allConsumptions.length;

      // 2. Eliminar TODAS las transacciones (featured, premium, etc.)
      console.log("🗑️ Eliminando transacciones...");
      const allTransactions = await ctx.db.query("transactions").collect();
      for (const transaction of allTransactions) {
        await ctx.db.delete(transaction._id);
      }
      deletedCounts.transactions = allTransactions.length;

      // 3. Resetear creditsUsed en TODAS las suscripciones
      console.log("🔄 Reseteando creditsUsed en suscripciones...");
      const allSubscriptions = await ctx.db.query("subscriptions").collect();
      for (const subscription of allSubscriptions) {
        await ctx.db.patch(subscription._id, {
          creditsUsed: 0,
          updatedAt: Date.now(),
        });
      }
      deletedCounts.subscriptionsReset = allSubscriptions.length;

      console.log("✅ Limpieza completa de créditos completada");

      return {
        success: true,
        message: "Limpieza completa de créditos realizada exitosamente",
        deletedCounts,
        totalDeleted: deletedCounts.creditConsumptions + deletedCounts.transactions,
        subscriptionsReset: deletedCounts.subscriptionsReset,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error("❌ Error en limpieza de créditos:", error);
      throw new Error(`Error en limpieza de créditos: ${error}`);
    }
  },
});

// Función para consumir créditos por acción específica
export const consumeCreditsForAction = mutation({
  args: {
    action: v.string(), // "respond_to_message", "view_appointment_request", etc.
    resourceId: v.string(), // ID del mensaje o solicitud
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    // Obtener configuración de la acción
    const creditAction = await ctx.db
      .query("creditActions")
      .filter((q: any) => q.eq(q.field("action"), args.action))
      .filter((q: any) => q.eq(q.field("isActive"), true))
      .first();

    if (!creditAction) {
      throw new Error("Acción no válida - Las configuraciones de créditos deben ser inicializadas");
    }

    // Verificar si ya se consumieron créditos para este recurso
    const existingConsumption = await ctx.db
      .query("creditConsumptions")
      .filter((q: any) => q.eq(q.field("userId"), identity.subject))
      .filter((q: any) => q.eq(q.field("action"), args.action))
      .filter((q: any) => q.eq(q.field("resourceId"), args.resourceId))
      .first();

    if (existingConsumption) {
      // Ya se pagó por este recurso
      return { success: true, alreadyPaid: true, creditsUsed: 0 };
    }

    // Obtener suscripción
    let subscription = await ctx.db
      .query("subscriptions")
      .withIndex("userId", (q) => q.eq("userId", identity.subject))
      .first();

    if (!subscription) {
      throw new Error("Suscripción no encontrada");
    }

    // ✨ NUEVA LÓGICA: Verificar si es usuario premium o en trial activo
    // Usar la misma lógica que getUserSubscriptionStatus para determinar premium
    const isPremiumUser = (subscription.amount === 99 || subscription.plan === "premium") && subscription.status === "active";
    const isTrialActive = subscription.isTrialActive && subscription.trialEndDate && Date.now() < subscription.trialEndDate;

    // Acciones gratuitas para usuarios premium y trial
    const freeActionsForPremium = [
      // "read_message", // REMOVIDO - leer mensajes es gratis para todos
      "view_appointment_request",
      "message_inquiry",
      "message_viewing",
      "message_offer",
      "message_negotiation",
      "respond_to_message",
      "respond_to_appointment"
    ];

    // Si es usuario premium o en trial activo y la acción es gratuita
    if ((isPremiumUser || isTrialActive) && freeActionsForPremium.includes(args.action)) {
      // Registrar el "consumo" pero sin costo para tracking
      await ctx.db.insert("creditConsumptions", {
        userId: identity.subject,
        action: args.action,
        resourceId: args.resourceId,
        creditsUsed: 0, // Gratis para premium
        description: `${creditAction.description} (Premium/Trial - Gratis)`,
        timestamp: Date.now(),
      });

      return {
        success: true,
        alreadyPaid: false,
        creditsUsed: 0,
        remainingCredits: subscription.credits - subscription.creditsUsed,
        isPremiumAccess: true
      };
    }

    // Para usuarios no premium o acciones que sí consumen créditos
    const availableCredits = subscription.credits - subscription.creditsUsed;
    if (availableCredits < creditAction.cost) {
      throw new Error("Créditos insuficientes");
    }

    // Consumir créditos
    const newCreditsUsed = subscription.creditsUsed + creditAction.cost;
    await ctx.db.patch(subscription._id, {
      creditsUsed: newCreditsUsed,
      updatedAt: Date.now(),
    });

    // Registrar el consumo
    await ctx.db.insert("creditConsumptions", {
      userId: identity.subject,
      action: args.action,
      resourceId: args.resourceId,
      creditsUsed: creditAction.cost,
      description: creditAction.description,
      timestamp: Date.now(),
    });

    return {
      success: true,
      alreadyPaid: false,
      creditsUsed: creditAction.cost,
      remainingCredits: subscription.credits - newCreditsUsed,
    };
  },
});

// Función de debug para verificar estado de suscripción
export const debugSubscriptionStatus = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return { error: "No autenticado" };
    }

    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("userId", (q) => q.eq("userId", identity.subject))
      .first();

    if (!subscription) {
      return { error: "Sin suscripción" };
    }

    // Verificar configuración de read_message
    const readMessageConfig = await ctx.db
      .query("creditActions")
      .filter((q: any) => q.eq(q.field("action"), "read_message"))
      .filter((q: any) => q.eq(q.field("isActive"), true))
      .first();

    // Verificar todas las configuraciones de créditos
    const allCreditActions = await ctx.db.query("creditActions").collect();



    const isPremiumByAmount = subscription.amount === 99;
    const isPremiumByPlan = subscription.plan === "premium";
    const isPremiumUser = (subscription.amount === 99 || subscription.plan === "premium") && subscription.status === "active";
    const isTrialActive = subscription.isTrialActive && subscription.trialEndDate && Date.now() < subscription.trialEndDate;

    return {
      subscription: {
        plan: subscription.plan,
        amount: subscription.amount,
        status: subscription.status,
        credits: subscription.credits,
        creditsUsed: subscription.creditsUsed,
        isTrialActive: subscription.isTrialActive,
        trialEndDate: subscription.trialEndDate,
      },
      checks: {
        isPremiumByAmount,
        isPremiumByPlan,
        isPremiumUser,
        isTrialActive,
      },
      readMessageConfig: readMessageConfig ? {
        action: readMessageConfig.action,
        cost: readMessageConfig.cost,
        isActive: readMessageConfig.isActive,
      } : null,

      totalCreditActions: allCreditActions.length,
      needsSeeding: allCreditActions.length === 0
    };
  },
});

// Función para agregar configuraciones faltantes
export const addMissingCreditActions = mutation({
  args: {},
  handler: async (ctx) => {
    // Verificar qué configuraciones faltan
    const existingActions = await ctx.db.query("creditActions").collect();
    const existingActionNames = existingActions.map(action => action.action);

    // Configuraciones que deberían existir
    const requiredActions = [
      "featured_property",
      "premium_home",
      "message_inquiry",
      "message_viewing",
      "message_offer",
      "message_negotiation",
      "read_message",
      "view_appointment_request",
      "respond_to_message",
      "respond_to_appointment"
    ];

    const missingActions = requiredActions.filter(action => !existingActionNames.includes(action));

    if (missingActions.length === 0) {
      return { message: "Todas las configuraciones ya existen", existing: existingActionNames };
    }

    // Configuraciones completas
    const allConfigurations = [
      {
        action: "featured_property",
        cost: 10,
        duration: 30,
        description: "Destacar propiedad por 30 días",
        isActive: true,
        metadata: { maxConcurrent: null, autoRenew: false }
      },
      {
        action: "premium_home",
        cost: 25,
        duration: 7,
        description: "Posición premium en home por 7 días",
        isActive: true,
        metadata: { maxConcurrent: 1, autoRenew: false }
      },
      {
        action: "message_inquiry",
        cost: 1,
        duration: undefined,
        description: "Recibir consulta general",
        isActive: true,
        metadata: {}
      },
      {
        action: "message_viewing",
        cost: 3,
        duration: undefined,
        description: "Recibir solicitud de visita",
        isActive: true,
        metadata: {}
      },
      {
        action: "message_offer",
        cost: 5,
        duration: undefined,
        description: "Recibir oferta de compra",
        isActive: true,
        metadata: {}
      },
      {
        action: "message_negotiation",
        cost: 2,
        duration: undefined,
        description: "Recibir consulta de negociación",
        isActive: true,
        metadata: {}
      },
      {
        action: "read_message",
        cost: 2,
        duration: undefined,
        description: "Leer mensaje recibido de web",
        isActive: true,
        metadata: {}
      },
      {
        action: "view_appointment_request",
        cost: 3,
        duration: undefined,
        description: "Ver solicitud de cita",
        isActive: true,
        metadata: {}
      },
      {
        action: "respond_to_message",
        cost: 2,
        duration: undefined,
        description: "Responder a mensaje (desbloquea información completa)",
        isActive: true,
        metadata: {}
      },
      {
        action: "respond_to_appointment",
        cost: 3,
        duration: undefined,
        description: "Responder a solicitud de cita (desbloquea información completa)",
        isActive: true,
        metadata: {}
      }
    ];

    // Insertar solo las configuraciones faltantes
    const inserted = [];
    const now = Date.now();

    for (const config of allConfigurations) {
      if (missingActions.includes(config.action)) {
        const actionId = await ctx.db.insert("creditActions", {
          ...config,
          createdAt: now,
          updatedAt: now,
        });
        inserted.push({ action: config.action, id: actionId });
      }
    }

    return {
      message: "Configuraciones faltantes agregadas",
      missing: missingActions,
      inserted: inserted.length,
      existing: existingActionNames
    };
  },
});

// Función temporal para inicializar configuraciones de créditos
export const initializeCreditActions = mutation({
  args: {},
  handler: async (ctx) => {
    // Verificar si ya existen configuraciones
    const existingActions = await ctx.db.query("creditActions").collect();

    if (existingActions.length > 0) {
      return { message: "Configuraciones ya existen", count: existingActions.length };
    }

    // Configuraciones por defecto
    const DEFAULT_CREDIT_ACTIONS = [
      {
        action: "featured_property",
        cost: 10,
        duration: 30,
        description: "Destacar propiedad por 30 días",
        isActive: true,
        metadata: { maxConcurrent: null, autoRenew: false }
      },
      {
        action: "premium_home",
        cost: 25,
        duration: 7,
        description: "Posición premium en home por 7 días",
        isActive: true,
        metadata: { maxConcurrent: 1, autoRenew: false }
      },
      {
        action: "message_inquiry",
        cost: 1,
        duration: undefined,
        description: "Recibir consulta general",
        isActive: true,
        metadata: {}
      },
      {
        action: "message_viewing",
        cost: 3,
        duration: undefined,
        description: "Recibir solicitud de visita",
        isActive: true,
        metadata: {}
      },
      {
        action: "message_offer",
        cost: 5,
        duration: undefined,
        description: "Recibir oferta de compra",
        isActive: true,
        metadata: {}
      },
      {
        action: "message_negotiation",
        cost: 2,
        duration: undefined,
        description: "Recibir consulta de negociación",
        isActive: true,
        metadata: {}
      },
      {
        action: "read_message",
        cost: 0, // Cambiar a 0 - leer mensajes es gratis
        duration: undefined,
        description: "Leer mensaje recibido de web (GRATIS - solo se cobra por responder)",
        isActive: false, // Desactivar - ya no se usa
        metadata: {}
      },
      {
        action: "view_appointment_request",
        cost: 3,
        duration: undefined,
        description: "Ver solicitud de cita",
        isActive: true,
        metadata: {}
      },
      {
        action: "respond_to_message",
        cost: 2,
        duration: undefined,
        description: "Responder a mensaje (desbloquea información completa)",
        isActive: true,
        metadata: {}
      },
      {
        action: "respond_to_appointment",
        cost: 3,
        duration: undefined,
        description: "Responder a solicitud de cita (desbloquea información completa)",
        isActive: true,
        metadata: {}
      }
    ];

    // Insertar configuraciones por defecto
    const inserted = [];
    const now = Date.now();

    for (const actionConfig of DEFAULT_CREDIT_ACTIONS) {
      const actionId = await ctx.db.insert("creditActions", {
        ...actionConfig,
        createdAt: now,
        updatedAt: now,
      });
      inserted.push(actionId);
    }

    return {
      message: "Configuraciones de créditos creadas exitosamente",
      count: inserted.length,
      actions: inserted
    };
  },
});

// NOTA: Eliminada función helper getAvailableCredits para evitar inconsistencias
// Ahora usamos directamente subscription.credits - subscription.creditsUsed
// igual que en el header (CreditsDisplay)

// Función para verificar si ya se consumieron créditos para una acción específica
export const checkCreditsConsumed = query({
  args: {
    action: v.string(), // "respond_to_message", "view_appointment_request", etc.
    resourceId: v.string(), // ID del mensaje o solicitud
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return false;
    }

    // Verificar si ya se consumieron créditos para este recurso
    const existingConsumption = await ctx.db
      .query("creditConsumptions")
      .filter((q: any) => q.eq(q.field("userId"), identity.subject))
      .filter((q: any) => q.eq(q.field("action"), args.action))
      .filter((q: any) => q.eq(q.field("resourceId"), args.resourceId))
      .first();

    return !!existingConsumption;
  },
});

// Verificar si se puede realizar una acción (si tiene créditos suficientes)
export const canPerformAction = query({
  args: {
    action: v.string(),
    resourceId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return { canPerform: false, reason: "No autenticado" };
    }

    // USAR LA MISMA LÓGICA QUE getUserSubscription
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("userId", (q) => q.eq("userId", identity.subject))
      .first();

    if (!subscription) {
      return { canPerform: false, reason: "Suscripción no encontrada" };
    }

    // Calcular créditos disponibles igual que en el header
    const availableCredits = subscription.credits - subscription.creditsUsed;

    // Para "respond_to_message" necesita 2 créditos (hardcoded como en el mensaje de error)
    const requiredCredits = args.action === "respond_to_message" ? 2 : 1;

    if (availableCredits < requiredCredits) {
      return {
        canPerform: false,
        reason: "Créditos insuficientes",
        requiredCredits,
        availableCredits,
        isPremiumAccess: false
      };
    }

    return {
      canPerform: true,
      reason: "Créditos suficientes",
      requiredCredits,
      availableCredits,
      isPremiumAccess: false // Siempre false en esta versión simplificada
    };
  },
});

// ===== FUNCIONES DE TRIAL SYSTEM =====

// Verificar estado del trial
export const checkTrialStatus = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return { hasAccess: false, reason: "No autenticado" };
    }

    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("userId", (q) => q.eq("userId", identity.subject))
      .first();

    if (!subscription) {
      return { hasAccess: false, reason: "Sin suscripción" };
    }

    const now = Date.now();

    // Si tiene trial activo, verificar si no ha expirado
    if (subscription.isTrialActive && subscription.trialEndDate) {
      if (now > subscription.trialEndDate) {
        // Trial expirado - solo retornar info, no modificar en query
        return {
          hasAccess: false,
          reason: "Trial expirado",
          trialExpired: true,
          trialEndDate: subscription.trialEndDate,
          needsUpdate: true // Flag para que el frontend llame a mutation
        };
      }

      return {
        hasAccess: true,
        reason: "Trial activo",
        isTrialActive: true,
        trialEndDate: subscription.trialEndDate,
        daysRemaining: Math.ceil((subscription.trialEndDate - now) / (24 * 60 * 60 * 1000))
      };
    }

    // Si tiene suscripción premium activa
    if (subscription.plan !== "free" && subscription.status === "active") {
      return {
        hasAccess: true,
        reason: "Suscripción premium activa",
        isTrialActive: false,
        plan: subscription.plan
      };
    }

    return { hasAccess: false, reason: "Plan gratuito" };
  },
});

// Mutation para actualizar trial expirado
export const updateExpiredTrial = mutation({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("userId", (q) => q.eq("userId", identity.subject))
      .first();

    if (!subscription) {
      throw new Error("Sin suscripción");
    }

    const now = Date.now();

    // Si tiene trial activo pero expirado, actualizar
    if (subscription.isTrialActive && subscription.trialEndDate && now > subscription.trialEndDate) {
      await ctx.db.patch(subscription._id, {
        isTrialActive: false,
        status: "active", // Mantener activo pero sin trial
        updatedAt: now,
      });

      return { success: true, message: "Trial actualizado a expirado" };
    }

    return { success: false, message: "No necesita actualización" };
  },
});

// Iniciar trial para nuevos usuarios
export const startTrial = mutation({
  args: {
    userRole: v.union(v.literal("seller"), v.literal("agent")),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    // Verificar que el rol necesite suscripción
    if (args.userRole !== "seller" && args.userRole !== "agent") {
      throw new Error("Solo vendedores y agentes pueden iniciar trial");
    }

    // Verificar si ya tiene suscripción
    const existingSubscription = await ctx.db
      .query("subscriptions")
      .withIndex("userId", (q) => q.eq("userId", identity.subject))
      .first();

    if (existingSubscription) {
      // Si ya usó trial, no puede iniciar otro
      if (existingSubscription.hasUsedTrial) {
        throw new Error("Ya has usado tu período de prueba gratuito");
      }

      // Si ya tiene trial activo, devolver info
      if (existingSubscription.isTrialActive) {
        return {
          success: true,
          message: "Trial ya activo",
          trialEndDate: existingSubscription.trialEndDate,
          alreadyActive: true
        };
      }
    }

    // Obtener configuración de días de trial
    const trialDaysSetting = await ctx.db
      .query("adminSettings")
      .withIndex("by_key", (q) => q.eq("key", "trial_days"))
      .first();

    const trialDays = trialDaysSetting?.value as number || 15;
    const now = Date.now();
    const trialEndDate = now + (trialDays * 24 * 60 * 60 * 1000);

    if (existingSubscription) {
      // Actualizar suscripción existente con trial
      await ctx.db.patch(existingSubscription._id, {
        isTrialActive: true,
        trialStartDate: now,
        trialEndDate: trialEndDate,
        trialDaysGranted: trialDays,
        hasUsedTrial: true,
        userRole: args.userRole,
        status: "trialing",
        updatedAt: now,
      });
    } else {
      // Crear nueva suscripción con trial
      await ctx.db.insert("subscriptions", {
        userId: identity.subject,
        plan: "free",
        status: "trialing",
        credits: 300, // Créditos de trial
        creditsUsed: 0,
        maxProperties: 999999, // Ilimitadas durante trial
        propertiesCount: 0,
        isTrialActive: true,
        trialStartDate: now,
        trialEndDate: trialEndDate,
        trialDaysGranted: trialDays,
        hasUsedTrial: true,
        userRole: args.userRole,
        createdAt: now,
        updatedAt: now,
      });
    }

    return {
      success: true,
      message: `Trial de ${trialDays} días iniciado exitosamente`,
      trialEndDate,
      trialDays
    };
  },
});

// Función para hacer downgrade de plan (solo de premium a free)
export const downgradePlan = mutation({
  args: {
    targetPlan: v.literal("free"), // Solo se puede hacer downgrade a free
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("userId", (q) => q.eq("userId", identity.subject))
      .first();

    if (!subscription) {
      throw new Error("No se encontró suscripción");
    }

    // Validar que sea un downgrade válido
    const currentPlan = subscription.plan;
    if (currentPlan === "free") {
      throw new Error("Ya estás en el plan gratuito");
    }

    if (currentPlan !== "premium") {
      throw new Error("Solo puedes hacer downgrade desde el plan premium");
    }

    if (args.targetPlan !== "free") {
      throw new Error("Solo puedes hacer downgrade al plan gratuito");
    }

    // Obtener límites del nuevo plan
    const newLimits = getPlanLimits(args.targetPlan);

    // Verificar si el usuario excede los límites del nuevo plan
    const userProperties = await ctx.db
      .query("properties")
      .withIndex("by_owner", (q) => q.eq("ownerId", identity.subject))
      .filter((q: any) => q.neq(q.field("status"), "draft"))
      .collect();

    if (userProperties.length > newLimits.maxProperties) {
      throw new Error(
        `No puedes hacer downgrade porque tienes ${userProperties.length} propiedades publicadas. ` +
        `El plan ${args.targetPlan} permite máximo ${newLimits.maxProperties} propiedades. ` +
        `Por favor, elimina o despublica algunas propiedades primero.`
      );
    }

    const now = Date.now();

    // Actualizar suscripción
    await ctx.db.patch(subscription._id, {
      plan: args.targetPlan,
      credits: newLimits.credits,
      maxProperties: newLimits.maxProperties,
      // Mantener créditos usados pero no exceder el nuevo límite
      creditsUsed: Math.min(subscription.creditsUsed, newLimits.credits),
      // Si era trial, mantener trial activo si aplica
      status: subscription.isTrialActive ? "trialing" : "active",
      updatedAt: now,
    });

    return {
      success: true,
      message: `Plan cambiado exitosamente a ${args.targetPlan}`,
      newPlan: args.targetPlan,
      newLimits,
    };
  },
});

// Función auxiliar para obtener límites de plan (solo free y premium)
function getPlanLimits(plan: string) {
  switch (plan) {
    case "premium":
      return { credits: 300, maxProperties: 999999 }; // Ilimitadas
    default: // free
      return { credits: 10, maxProperties: 5 };
  }
}

// 🔄 NUEVA: Sistema de reset automático completo cuando expiran trials o se agotan créditos
export const processAutomaticResets = mutation({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();
    let processedUsers = 0;
    let resetCredits = 0;
    let downgradedProperties = 0;

    console.log("🔄 Iniciando proceso de reset automático...");

    // 1. Buscar usuarios con trials expirados
    const expiredTrials = await ctx.db
      .query("subscriptions")
      .filter((q: any) =>
        q.and(
          q.eq(q.field("isTrialActive"), true),
          q.lt(q.field("trialEndDate"), now)
        )
      )
      .collect();

    // 2. Buscar usuarios con créditos agotados (plan free)
    const depleted = await ctx.db
      .query("subscriptions")
      .filter((q: any) =>
        q.and(
          q.eq(q.field("plan"), "free"),
          q.gte(q.field("creditsUsed"), q.field("credits"))
        )
      )
      .collect();

    const usersToReset = [...expiredTrials, ...depleted];

    for (const subscription of usersToReset) {
      try {
        console.log(`🔄 Procesando reset para usuario: ${subscription.userId}`);

        // A. RESET DE TRIAL EXPIRADO
        if (subscription.isTrialActive && subscription.trialEndDate && now > subscription.trialEndDate) {
          console.log(`⏰ Trial expirado para usuario: ${subscription.userId}`);

          // Actualizar suscripción a plan free
          await ctx.db.patch(subscription._id, {
            plan: "free",
            status: "active",
            isTrialActive: false,
            credits: 10, // Créditos del plan free
            creditsUsed: 0, // Reset completo
            maxProperties: 5,
            updatedAt: now,
          });

          resetCredits++;
        }

        // B. RESET DE CRÉDITOS AGOTADOS (plan free)
        else if (subscription.plan === "free" && subscription.creditsUsed >= subscription.credits) {
          console.log(`💳 Créditos agotados para usuario free: ${subscription.userId}`);

          // Reset mensual de créditos para plan free
          await ctx.db.patch(subscription._id, {
            creditsUsed: 0,
            credits: 10, // Asegurar límite correcto
            updatedAt: now,
          });

          resetCredits++;
        }

        // C. DOWNGRADE AUTOMÁTICO DE PROPIEDADES
        const userProperties = await ctx.db
          .query("properties")
          .withIndex("by_owner", (q) => q.eq("ownerId", subscription.userId))
          .collect();

        for (const property of userProperties) {
          let propertyUpdated = false;

          // Quitar featured si está activo
          if (property.featured && property.featuredUntil && now > property.featuredUntil) {
            await ctx.db.patch(property._id, {
              featured: false,
              featuredUntil: undefined,
              featuredTransaction: undefined,
            });
            propertyUpdated = true;
          }

          // Quitar premium home si está activo
          if (property.premiumHomeUntil && now > property.premiumHomeUntil) {
            await ctx.db.patch(property._id, {
              premiumHomeUntil: undefined,
              premiumTransaction: undefined,
            });
            propertyUpdated = true;
          }

          if (propertyUpdated) {
            downgradedProperties++;
          }
        }

        processedUsers++;

      } catch (error) {
        console.error(`❌ Error procesando reset para usuario ${subscription.userId}:`, error);
      }
    }

    console.log(`✅ Reset automático completado: ${processedUsers} usuarios, ${resetCredits} resets de créditos, ${downgradedProperties} propiedades downgradeadas`);

    return {
      success: true,
      processedUsers,
      resetCredits,
      downgradedProperties,
      message: `Reset automático completado para ${processedUsers} usuarios`
    };
  },
});

// 🔄 NUEVA: Reset manual para usuario específico (cuando detecta estado inconsistente)
export const resetUserSubscriptionState = mutation({
  args: {
    userId: v.optional(v.string()), // Si no se proporciona, usa el usuario actual
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    const targetUserId = args.userId || identity.subject;

    // Solo admins pueden resetear otros usuarios
    if (args.userId && args.userId !== identity.subject) {
      const user = await ctx.db
        .query("users")
        .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.subject))
        .first();

      if (!user || user.role !== "admin") {
        throw new Error("Solo administradores pueden resetear otros usuarios");
      }
    }

    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("userId", (q) => q.eq("userId", targetUserId))
      .first();

    if (!subscription) {
      throw new Error("Usuario sin suscripción");
    }

    const now = Date.now();
    let resetPerformed = false;
    let actions = [];

    // A. RESET DE TRIAL EXPIRADO
    if (subscription.isTrialActive && subscription.trialEndDate && now > subscription.trialEndDate) {
      await ctx.db.patch(subscription._id, {
        plan: "free",
        status: "active",
        isTrialActive: false,
        credits: 10,
        creditsUsed: 0,
        maxProperties: 5,
        updatedAt: now,
      });

      actions.push("Trial expirado → Plan Free");
      resetPerformed = true;
    }

    // B. RESET DE CRÉDITOS AGOTADOS
    else if (subscription.plan === "free" && subscription.creditsUsed >= subscription.credits) {
      await ctx.db.patch(subscription._id, {
        creditsUsed: 0,
        credits: 10,
        updatedAt: now,
      });

      actions.push("Créditos reseteados");
      resetPerformed = true;
    }

    // C. DOWNGRADE DE PROPIEDADES EXPIRADAS
    const userProperties = await ctx.db
      .query("properties")
      .withIndex("by_owner", (q) => q.eq("ownerId", targetUserId))
      .collect();

    let propertiesDowngraded = 0;

    for (const property of userProperties) {
      let propertyUpdated = false;

      // Quitar featured expirado
      if (property.featured && property.featuredUntil && now > property.featuredUntil) {
        await ctx.db.patch(property._id, {
          featured: false,
          featuredUntil: undefined,
          featuredTransaction: undefined,
        });
        propertyUpdated = true;
      }

      // Quitar premium expirado
      if (property.premiumHomeUntil && now > property.premiumHomeUntil) {
        await ctx.db.patch(property._id, {
          premiumHomeUntil: undefined,
          premiumTransaction: undefined,
        });
        propertyUpdated = true;
      }

      if (propertyUpdated) {
        propertiesDowngraded++;
      }
    }

    if (propertiesDowngraded > 0) {
      actions.push(`${propertiesDowngraded} propiedades downgradeadas`);
      resetPerformed = true;
    }

    return {
      success: true,
      resetPerformed,
      actions,
      message: resetPerformed
        ? `Reset completado: ${actions.join(", ")}`
        : "No se requiere reset"
    };
  },
});

// 📧 Procesar recordatorios de suscripciones y trials (ejecutado por cron)
export const processSubscriptionReminders = mutation({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();

    // Buscar todas las suscripciones activas
    const subscriptions = await ctx.db
      .query("subscriptions")
      .filter((q: any) => q.eq(q.field("status"), "active"))
      .collect();

    // También buscar suscripciones en trial
    const trialSubscriptions = await ctx.db
      .query("subscriptions")
      .filter((q: any) => q.eq(q.field("status"), "trialing"))
      .collect();

    const allSubscriptions = [...subscriptions, ...trialSubscriptions];

    let subscriptionReminders = 0;
    let trialReminders = 0;

    for (const subscription of allSubscriptions) {
      try {
        // Obtener información del usuario
        const user = await ctx.db
          .query("users")
          .withIndex("by_token", (q) => q.eq("tokenIdentifier", subscription.userId))
          .first();

        if (!user || !user.email) continue;

        // Verificar trials por vencer (3 días antes)
        if (subscription.isTrialActive && subscription.trialEndDate) {
          const daysUntilTrialEnd = Math.ceil((subscription.trialEndDate - now) / (24 * 60 * 60 * 1000));

          if (daysUntilTrialEnd === 3 && !subscription.trialReminderSent) {
            // Obtener estadísticas del usuario durante el trial
            const userProperties = await ctx.db
              .query("properties")
              .withIndex("by_owner", (q) => q.eq("ownerId", subscription.userId))
              .collect();

            const userMessages = await ctx.db
              .query("messages")
              .withIndex("by_receiver", (q) => q.eq("receiverId", subscription.userId))
              .collect();

            const totalViews = userProperties.reduce((sum, prop) => sum + (prop.views || 0), 0);
            const totalFavorites = userProperties.reduce((sum, prop) => sum + (prop.favoritesCount || 0), 0);

            const hoursLeft = Math.ceil((subscription.trialEndDate - now) / (60 * 60 * 1000));

            await ctx.scheduler.runAfter(0, api.emails.notifyTrialExpiring, {
              userEmail: user.email,
              userName: user.name || "Usuario",
              daysLeft: daysUntilTrialEnd,
              hoursLeft,
              trialEndDate: new Date(subscription.trialEndDate).toLocaleDateString('es-ES'),
              propertiesPublished: userProperties.length,
              inquiriesReceived: userMessages.length,
              totalViews,
              favoritesReceived: totalFavorites,
              upgradeUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/finance`,
              dashboardUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard`,
            });

            // Marcar como enviado
            await ctx.db.patch(subscription._id, {
              trialReminderSent: true,
            });

            trialReminders++;
          }
        }

      } catch (error) {
        console.error("Error procesando recordatorio de suscripción:", error);
      }
    }

    return {
      subscriptionReminders,
      trialReminders,
      processedAt: new Date().toISOString(),
    };
  },
});
