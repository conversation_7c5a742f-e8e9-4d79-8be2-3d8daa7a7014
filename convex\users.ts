import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

export const getUser = query({
    args: {},
    handler: async (ctx) => {
        const identity = await ctx.auth.getUserIdentity();
        if (identity === null) {
            return ("Not authenticated");
        }
        return identity
    }
})

// Obtener usuario actual completo desde la base de datos
export const getCurrentUser = query({
    args: {},
    handler: async (ctx) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            return null;
        }

        const user = await ctx.db
            .query("users")
            .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.subject))
            .first();

        return user;
    },
});

export const getUserByToken = query({
    args: { tokenIdentifier: v.string() },
    handler: async (ctx, args) => {
        const user = await ctx.db.query("users")
            .withIndex("by_token", (q) => q.eq("tokenIdentifier", args.tokenIdentifier))
            .first();

        if (!user) return null;



        // Si el usuario no tiene nombre o tiene "Usuario", usar el email como fallback
        let displayName = user.name;
        if (!displayName || displayName.trim() === '' || displayName === 'Usuario') {
            const emailPart = user.email.split('@')[0];
            displayName = emailPart.charAt(0).toUpperCase() + emailPart.slice(1);
        }

        return {
            name: displayName,
            email: user.email,
            phone: user.phone || "+502 57324963",
            company: user.company || "InmoApp",
            bio: user.bio || "Profesional certificado con años de experiencia en el mercado inmobiliario. Te ayudamos a encontrar la propiedad perfecta para ti.",
            avatar: user.avatar || user.image,
            role: user.role,
            license: user.license
        };
    },
});

export const store = mutation({
    args: {},
    handler: async (ctx) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Called storeUser without authentication present");
        }

        // Check if we've already stored this identity before
        const user = await ctx.db
            .query("users")
            .withIndex("by_token", (q) =>
                q.eq("tokenIdentifier", identity.subject)
            )
            .unique();

        if (user !== null) {
            // Actualizar solo campos básicos si han cambiado
            const updateData: any = {};

            // Priorizar displayName de unsafeMetadata sobre identity.name
            const metadata = identity.unsafeMetadata as any;
            const preferredName = metadata?.displayName || identity.name;

            // Actualizar el nombre si es diferente
            if (preferredName && user.name !== preferredName) {
                updateData.name = preferredName;
            }

            // Actualizar bio si existe en metadata
            if (metadata?.bio && user.bio !== metadata.bio) {
                updateData.bio = metadata.bio;
            }

            if (user.email !== identity.email) updateData.email = identity.email;
            if (user.image !== identity.pictureUrl) updateData.image = identity.pictureUrl;

            if (Object.keys(updateData).length > 0) {
                await ctx.db.patch(user._id, updateData);
            }

            return user._id;
        }

        // Crear nuevo usuario con campos básicos
        const userName = identity.name || identity.email?.split('@')[0] || "Usuario";

        return await ctx.db.insert("users", {
            name: userName,
            email: identity.email!,
            image: identity.pictureUrl,
            userId: identity.subject,
            tokenIdentifier: identity.subject,
            createdAt: new Date().toISOString(),
        });
    },
});

export const updateProfile = mutation({
    args: {
        role: v.optional(v.string()),
        phone: v.optional(v.string()),
        company: v.optional(v.string()),
        license: v.optional(v.string()),
        bio: v.optional(v.string()),
        avatar: v.optional(v.string()),
        name: v.optional(v.string()),
        currency: v.optional(v.string()),
        notifications: v.optional(v.boolean()),
        newsletter: v.optional(v.boolean()),
    },
    handler: async (ctx, args) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Not authenticated");
        }

        let user = await ctx.db
            .query("users")
            .withIndex("by_token", (q) =>
                q.eq("tokenIdentifier", identity.subject)
            )
            .unique();

        if (!user) {
            // Si el usuario no existe, crearlo automáticamente
            // console.log("Usuario no encontrado, creando automáticamente...");
            const userName = identity.name || identity.email?.split('@')[0] || "Usuario";

            const userId = await ctx.db.insert("users", {
                name: userName,
                email: identity.email!,
                image: identity.pictureUrl,
                userId: identity.subject,
                tokenIdentifier: identity.subject,
                createdAt: new Date().toISOString(),
            });

            user = await ctx.db.get(userId);
            if (!user) {
                throw new Error("Error creating user");
            }
        }

        // Actualizar perfil con los nuevos datos
        await ctx.db.patch(user._id, {
            ...args,
            // Convertir role a tipo correcto si se proporciona
            ...(args.role && { role: args.role as any }),
        });

        return user._id;
    },
});

export const deleteUser = mutation({
    args: {},
    handler: async (ctx) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Not authenticated");
        }

        const user = await ctx.db
            .query("users")
            .withIndex("by_token", (q) =>
                q.eq("tokenIdentifier", identity.subject)
            )
            .unique();

        if (!user) {
            throw new Error("User not found");
        }

        let deletedCount = {
            properties: 0,
            favorites: 0,
            messages: 0,
            appointments: 0,
            appointmentRequests: 0,
            transactions: 0,
            orders: 0,
            leads: 0,
            creditConsumptions: 0
        };

        // 1. Eliminar propiedades del usuario (como propietario)
        const userProperties = await ctx.db
            .query("properties")
            .withIndex("by_owner", (q) => q.eq("ownerId", identity.subject))
            .collect();

        for (const property of userProperties) {
            await ctx.db.delete(property._id);
            deletedCount.properties++;
        }

        // 2. Eliminar favoritos del usuario
        const userFavorites = await ctx.db
            .query("favorites")
            .withIndex("by_user", (q) => q.eq("userId", identity.subject))
            .collect();

        for (const favorite of userFavorites) {
            await ctx.db.delete(favorite._id);
            deletedCount.favorites++;
        }

        // 3. Eliminar mensajes enviados por el usuario
        const sentMessages = await ctx.db
            .query("messages")
            .withIndex("by_sender", (q) => q.eq("senderId", identity.subject))
            .collect();

        for (const message of sentMessages) {
            await ctx.db.delete(message._id);
            deletedCount.messages++;
        }

        // 4. Eliminar mensajes recibidos por el usuario
        const receivedMessages = await ctx.db
            .query("messages")
            .withIndex("by_receiver", (q) => q.eq("receiverId", identity.subject))
            .collect();

        for (const message of receivedMessages) {
            await ctx.db.delete(message._id);
            deletedCount.messages++;
        }

        // 5. Eliminar citas como host
        const hostAppointments = await ctx.db
            .query("appointments")
            .withIndex("by_host", (q) => q.eq("hostId", identity.subject))
            .collect();

        for (const appointment of hostAppointments) {
            await ctx.db.delete(appointment._id);
            deletedCount.appointments++;
        }

        // 6. Eliminar citas como invitado
        const guestAppointments = await ctx.db
            .query("appointments")
            .withIndex("by_guest", (q) => q.eq("guestId", identity.subject))
            .collect();

        for (const appointment of guestAppointments) {
            await ctx.db.delete(appointment._id);
            deletedCount.appointments++;
        }

        // 7. Eliminar solicitudes de citas como host
        const hostRequests = await ctx.db
            .query("appointmentRequests")
            .withIndex("by_host", (q) => q.eq("hostId", identity.subject))
            .collect();

        for (const request of hostRequests) {
            await ctx.db.delete(request._id);
            deletedCount.appointmentRequests++;
        }

        // 8. Eliminar solicitudes de citas como invitado
        const guestRequests = await ctx.db
            .query("appointmentRequests")
            .withIndex("by_guest", (q) => q.eq("guestId", identity.subject))
            .collect();

        for (const request of guestRequests) {
            await ctx.db.delete(request._id);
            deletedCount.appointmentRequests++;
        }

        // 9. Eliminar transacciones del usuario
        const userTransactions = await ctx.db
            .query("transactions")
            .withIndex("by_user", (q) => q.eq("userId", identity.subject))
            .collect();

        for (const transaction of userTransactions) {
            await ctx.db.delete(transaction._id);
            deletedCount.transactions++;
        }

        // 10. Eliminar órdenes del usuario
        const userOrders = await ctx.db
            .query("orders")
            .withIndex("by_user", (q) => q.eq("userId", identity.subject))
            .collect();

        for (const order of userOrders) {
            await ctx.db.delete(order._id);
            deletedCount.orders++;
        }

        // 11. Eliminar leads asignados al usuario (si es agente)
        const assignedLeads = await ctx.db
            .query("leads")
            .withIndex("by_assignedAgent", (q) => q.eq("assignedAgentId", identity.subject))
            .collect();

        for (const lead of assignedLeads) {
            await ctx.db.delete(lead._id);
            deletedCount.leads++;
        }

        // 12. Eliminar consumos de créditos del usuario
        const userCreditConsumptions = await ctx.db
            .query("creditConsumptions")
            .withIndex("by_user", (q) => q.eq("userId", identity.subject))
            .collect();

        for (const consumption of userCreditConsumptions) {
            await ctx.db.delete(consumption._id);
            deletedCount.creditConsumptions++;
        }

        // 13. Finalmente, eliminar el usuario de Convex
        await ctx.db.delete(user._id);

        const totalDeleted = Object.values(deletedCount).reduce((sum, count) => sum + count, 0);

        return {
            success: true,
            message: `Usuario completamente eliminado de Inmova. Se eliminaron ${totalDeleted} registros relacionados.`,
            deletedCount,
            totalDeleted
        };
    },
});





// Función para actualizar un usuario específico por tokenIdentifier
export const updateUserName = mutation({
    args: { tokenIdentifier: v.string() },
    handler: async (ctx, args) => {
        const user = await ctx.db.query("users")
            .withIndex("by_token", (q) => q.eq("tokenIdentifier", args.tokenIdentifier))
            .first();

        if (!user) {
            throw new Error("Usuario no encontrado");
        }

        if (!user.name || user.name.trim() === '' || user.name === 'Usuario') {
            const emailPart = user.email.split('@')[0];
            const newName = emailPart
                .replace(/[._-]/g, ' ')
                .split(' ')
                .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                .join(' ');

            await ctx.db.patch(user._id, {
                name: newName
            });

            return { updated: true, newName };
        }

        return { updated: false, currentName: user.name };
    },
});

// FUNCIÓN ELIMINADA: syncPublicProfile
// Esta función causaba errores de autenticación y no es esencial.
// La sincronización de perfil público se maneja ahora a través de updateProfile.

// Función para asignar rol de administrador (solo para desarrollo)
export const makeAdmin = mutation({
    args: {
        email: v.string(),
    },
    handler: async (ctx, args) => {
        // Buscar usuario por email
        const user = await ctx.db
            .query("users")
            .filter((q: any) => q.eq(q.field("email"), args.email))
            .first();

        if (!user) {
            throw new Error(`Usuario con email ${args.email} no encontrado`);
        }

        // Asignar rol de admin
        await ctx.db.patch(user._id, {
            role: "admin",
        });

        return {
            success: true,
            message: `Usuario ${user.name || user.email} ahora es administrador`,
            userId: user._id,
        };
    },
});

// Función para asignar rol específico a un usuario por email
export const assignUserRole = mutation({
    args: {
        email: v.string(),
        role: v.union(
            v.literal("buyer"),
            v.literal("seller"),
            v.literal("agent"),
            v.literal("admin")
        ),
    },
    handler: async (ctx, args) => {
        // Buscar usuario por email
        const user = await ctx.db
            .query("users")
            .filter((q: any) => q.eq(q.field("email"), args.email))
            .first();

        if (!user) {
            throw new Error(`Usuario con email ${args.email} no encontrado`);
        }

        // Asignar el rol especificado
        await ctx.db.patch(user._id, {
            role: args.role,
        });

        return {
            success: true,
            message: `Usuario ${user.name || user.email} ahora tiene rol: ${args.role}`,
            userId: user._id,
            previousRole: user.role || "sin rol",
            newRole: args.role,
        };
    },
});

// Función para resetear usuario al onboarding (eliminar rol)
export const resetUserForOnboarding = mutation({
    args: {
        email: v.string(),
    },
    handler: async (ctx, args) => {
        // Buscar usuario por email
        const user = await ctx.db
            .query("users")
            .filter((q: any) => q.eq(q.field("email"), args.email))
            .first();

        if (!user) {
            throw new Error(`Usuario con email ${args.email} no encontrado`);
        }

        // Eliminar el rol para forzar onboarding
        await ctx.db.patch(user._id, {
            role: undefined,
        });

        return {
            success: true,
            message: `Usuario ${user.name || user.email} reseteado para onboarding`,
            userId: user._id,
        };
    },
});

// Función para migrar usuarios sin rol (redirigirlos al onboarding)
export const migrateUsersWithoutRole = mutation({
    args: {},
    handler: async (ctx) => {
        const usersWithoutRole = await ctx.db
            .query("users")
            .filter((q: any) => q.eq(q.field("role"), undefined))
            .collect();

        let migrated = 0;
        const results = [];

        for (const user of usersWithoutRole) {
            // No asignar rol automáticamente, solo marcar para onboarding
            results.push({
                email: user.email,
                name: user.name,
                needsOnboarding: true,
            });
            migrated++;
        }

        return {
            success: true,
            message: `Encontrados ${migrated} usuarios sin rol que necesitan completar onboarding`,
            usersNeedingOnboarding: results,
            migrated,
        };
    },
});

// Actualizar preferencias de ubicación del usuario
export const updateLocationPreferences = mutation({
    args: {
        defaultCountry: v.optional(v.string()),
        preferredLanguage: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Debes estar autenticado");
        }

        const user = await ctx.db
            .query("users")
            .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.subject))
            .unique();

        if (!user) {
            throw new Error("Usuario no encontrado");
        }

        await ctx.db.patch(user._id, {
            defaultCountry: args.defaultCountry,
            preferredLanguage: args.preferredLanguage,
        });

        return { message: "Preferencias actualizadas exitosamente" };
    },
});

// Obtener preferencias de ubicación del usuario
export const getUserLocationPreferences = query({
    args: {},
    handler: async (ctx) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            return { defaultCountry: "GT", preferredLanguage: "es" }; // Valores por defecto
        }

        const user = await ctx.db
            .query("users")
            .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.subject))
            .unique();

        if (!user) {
            return { defaultCountry: "GT", preferredLanguage: "es" };
        }

        return {
            defaultCountry: user.defaultCountry || "GT",
            preferredLanguage: user.preferredLanguage || "es",
        };
    },
});


