import { QdrantClient } from '@qdrant/js-client-rest';
import { getScoreThreshold } from './unified-config';

// Configuración de Qdrant
export const QDRANT_CONFIG = {
  url: process.env.QDRANT_URL || 'http://localhost:6333',
  apiKey: process.env.QDRANT_API_KEY,
  collectionName: 'properties',
  vectorSize: 1536, // OpenAI text-embedding-3-small
  distance: 'Cosine' as const,
};

// Cliente Qdrant singleton
let qdrantClient: QdrantClient | null = null;

export function getQdrantClient(): QdrantClient {
  if (!qdrantClient) {
    // ⚠️ SOLO PARA DESARROLLO: Configuración SSL permisiva
    // En producción, Vercel maneja SSL automáticamente
    if (process.env.NODE_ENV === 'development') {
      process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
    }

    const isHttps = QDRANT_CONFIG.url.includes('https');
    qdrantClient = new QdrantClient({
      url: QDRANT_CONFIG.url,
      apiKey: QDRANT_CONFIG.apiKey,
      // Configuración optimizada para desarrollo y producción
      port: isHttps ? 443 : 6333,
      https: isHttps,
      timeout: process.env.NODE_ENV === 'development' ? 10000 : 30000,
    });
  }
  return qdrantClient;
}

// Esquema de la colección de propiedades
export const PROPERTY_COLLECTION_SCHEMA = {
  vectors: {
    size: QDRANT_CONFIG.vectorSize,
    distance: QDRANT_CONFIG.distance,
  },
  optimizers_config: {
    default_segment_number: 2,
  },
  replication_factor: 1,
};

// Configuración de payload para filtros
export const PAYLOAD_SCHEMA = {
  propertyId: { type: 'keyword' as const },
  type: { type: 'keyword' as const },
  status: { type: 'keyword' as const },
  price: { type: 'integer' as const },
  currency: { type: 'keyword' as const },
  bedrooms: { type: 'integer' as const },
  bathrooms: { type: 'integer' as const },
  area: { type: 'integer' as const },
  country: { type: 'keyword' as const },
  level1: { type: 'keyword' as const },
  level2: { type: 'keyword' as const },
  level3: { type: 'keyword' as const },
  level4: { type: 'keyword' as const },
  neighborhood: { type: 'keyword' as const },
  coordinates: { type: 'geo' as const },
  amenities: { type: 'keyword' as const },
  createdAt: { type: 'datetime' as const },
  updatedAt: { type: 'datetime' as const },
  isActive: { type: 'bool' as const },
  isFeatured: { type: 'bool' as const },
  isPremium: { type: 'bool' as const },
};

// Función para verificar conexión
export async function testQdrantConnection(): Promise<boolean> {
  try {
    const client = getQdrantClient();
    await client.getCollections();
    return true;
  } catch (error) {
    console.error('Error connecting to Qdrant:', error);
    return false;
  }
}

// Función para crear la colección si no existe
export async function ensurePropertyCollection(): Promise<void> {
  try {
    const client = getQdrantClient();
    
    // Verificar si la colección existe
    const collections = await client.getCollections();
    const collectionExists = collections.collections.some(
      (col) => col.name === QDRANT_CONFIG.collectionName
    );

    if (!collectionExists) {
      console.log(`Creating collection: ${QDRANT_CONFIG.collectionName}`);
      
      await client.createCollection(QDRANT_CONFIG.collectionName, {
        vectors: PROPERTY_COLLECTION_SCHEMA.vectors,
        optimizers_config: PROPERTY_COLLECTION_SCHEMA.optimizers_config,
        replication_factor: PROPERTY_COLLECTION_SCHEMA.replication_factor,
      });

      console.log(`Collection ${QDRANT_CONFIG.collectionName} created successfully`);
    } else {
      console.log(`Collection ${QDRANT_CONFIG.collectionName} already exists`);
    }
  } catch (error) {
    console.error('Error ensuring property collection:', error);
    throw error;
  }
}

// Tipos TypeScript para Qdrant
export interface PropertyVector {
  id: string;
  vector: number[];
  payload: PropertyPayload;
}

export interface PropertyPayload {
  propertyId: string;
  title: string;
  description: string;
  type: string;
  status: string;
  price: number;
  currency: string;
  bedrooms: number;
  bathrooms: number;
  area: number;
  address: string;

  // Ubicación jerárquica completa (4 niveles)
  country: string;
  level1?: string;
  level2?: string;
  level3?: string;
  level4?: string;

  // Ubicación legacy para compatibilidad
  neighborhood?: string;

  coordinates?: {
    lat: number;
    lon: number;
  };

  amenities: string[];
  searchKeywords: string[];

  // Metadatos adicionales
  ownerId: string;
  agentId?: string;

  createdAt: string;
  updatedAt: string;
  isActive: boolean;
  isFeatured: boolean;
  isPremium: boolean;
  embeddingText: string; // Texto usado para generar el embedding
}

// Configuración de búsqueda
export const SEARCH_CONFIG = {
  defaultLimit: 10,
  maxLimit: 50,
  scoreThreshold: getScoreThreshold(), // ✅ UNIFICADO: Era 0.35, ahora usa configuración centralizada
  hybridAlpha: 0.7, // Balance entre búsqueda vectorial y filtros (0.7 = 70% vectorial, 30% filtros)
};

// Función para validar configuración
export function validateQdrantConfig(): void {
  if (!process.env.QDRANT_URL) {
    throw new Error('QDRANT_URL environment variable is required');
  }
  
  if (!process.env.OPENAI_API_KEY) {
    throw new Error('OPENAI_API_KEY environment variable is required for embeddings');
  }
  
  console.log('Qdrant configuration validated successfully');
}
