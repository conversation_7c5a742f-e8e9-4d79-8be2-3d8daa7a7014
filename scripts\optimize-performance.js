/**
 * Script de Optimización de Rendimiento - Día 5
 * 
 * Configura automáticamente las variables de entorno para máximo rendimiento
 * y valida que el sistema esté optimizado correctamente.
 */

const fs = require('fs');
const path = require('path');

// Configuraciones optimizadas para diferentes entornos
const PERFORMANCE_CONFIGS = {
  development: {
    ADAPTIVE_CACHE_TTL: '1800000',        // 30 minutos
    ADAPTIVE_CACHE_SIZE: '500',           // Menor para desarrollo
    PERFORMANCE_MONITORING: 'true',
    PREDICTIVE_CACHE_ENABLED: 'false',    // Deshabilitado en desarrollo
    OPENAI_TIMEOUT_PROFILE: '5000',       // Más tiempo en desarrollo
    OPENAI_TIMEOUT_NORMALIZE: '3000',
    OPENAI_TIMEOUT_EMBEDDINGS: '7000',
    PARALLEL_EMBEDDING_LIMIT: '2',        // Menos paralelo en desarrollo
    AI_MODEL_QUERY_ANALYSIS: 'gpt-4o-mini',
  },
  
  production: {
    ADAPTIVE_CACHE_TTL: '3600000',        // 1 hora
    ADAPTIVE_CACHE_SIZE: '2000',          // Mayor para producción
    PERFORMANCE_MONITORING: 'true',
    PREDICTIVE_CACHE_ENABLED: 'true',     // Habilitado en producción
    OPENAI_TIMEOUT_PROFILE: '3000',       // Timeouts más estrictos
    OPENAI_TIMEOUT_NORMALIZE: '2000',
    OPENAI_TIMEOUT_EMBEDDINGS: '5000',
    PARALLEL_EMBEDDING_LIMIT: '4',        // Máximo paralelo
    AI_MODEL_QUERY_ANALYSIS: 'gpt-4o-mini',
  },
  
  testing: {
    ADAPTIVE_CACHE_TTL: '300000',         // 5 minutos
    ADAPTIVE_CACHE_SIZE: '100',           // Pequeño para testing
    PERFORMANCE_MONITORING: 'false',
    PREDICTIVE_CACHE_ENABLED: 'false',    // Deshabilitado en testing
    OPENAI_TIMEOUT_PROFILE: '10000',      // Timeouts largos para testing
    OPENAI_TIMEOUT_NORMALIZE: '5000',
    OPENAI_TIMEOUT_EMBEDDINGS: '10000',
    PARALLEL_EMBEDDING_LIMIT: '1',        // Sin paralelismo en testing
    AI_MODEL_QUERY_ANALYSIS: 'gpt-4o-mini',
  }
};

/**
 * Detecta el entorno actual
 */
function detectEnvironment() {
  const nodeEnv = process.env.NODE_ENV || 'development';
  
  if (nodeEnv === 'production') return 'production';
  if (nodeEnv === 'test') return 'testing';
  return 'development';
}

/**
 * Lee el archivo .env.local actual
 */
function readEnvFile() {
  const envPath = path.join(process.cwd(), '.env.local');
  
  if (!fs.existsSync(envPath)) {
    console.log('📝 Archivo .env.local no existe, creando uno nuevo...');
    return {};
  }
  
  const content = fs.readFileSync(envPath, 'utf8');
  const env = {};
  
  content.split('\n').forEach(line => {
    const trimmed = line.trim();
    if (trimmed && !trimmed.startsWith('#')) {
      const [key, ...valueParts] = trimmed.split('=');
      if (key && valueParts.length > 0) {
        env[key.trim()] = valueParts.join('=').trim();
      }
    }
  });
  
  return env;
}

/**
 * Escribe el archivo .env.local optimizado
 */
function writeEnvFile(env) {
  const envPath = path.join(process.cwd(), '.env.local');
  
  let content = '# Archivo de configuración optimizado para rendimiento\n';
  content += `# Generado automáticamente el ${new Date().toISOString()}\n\n`;
  
  // Agrupar configuraciones
  const groups = {
    'Configuración básica': [
      'CONVEX_DEPLOYMENT',
      'NEXT_PUBLIC_CONVEX_URL',
      'OPENAI_API_KEY',
      'QDRANT_URL',
      'QDRANT_API_KEY'
    ],
    'Búsqueda semántica': [
      'SEARCH_SCORE_THRESHOLD',
      'SEMANTIC_WEIGHT_LOCATION',
      'SEMANTIC_WEIGHT_PROPERTY',
      'SEMANTIC_WEIGHT_AMENITIES',
      'SEMANTIC_WEIGHT_PRICE'
    ],
    'Optimizaciones de rendimiento': [
      'ADAPTIVE_CACHE_TTL',
      'ADAPTIVE_CACHE_SIZE',
      'PERFORMANCE_MONITORING',
      'PREDICTIVE_CACHE_ENABLED',
      'OPENAI_TIMEOUT_PROFILE',
      'OPENAI_TIMEOUT_NORMALIZE',
      'OPENAI_TIMEOUT_EMBEDDINGS',
      'PARALLEL_EMBEDDING_LIMIT',
      'AI_MODEL_QUERY_ANALYSIS'
    ]
  };
  
  Object.entries(groups).forEach(([groupName, keys]) => {
    content += `# ${groupName}\n`;
    keys.forEach(key => {
      if (env[key] !== undefined) {
        content += `${key}=${env[key]}\n`;
      }
    });
    content += '\n';
  });
  
  // Agregar otras variables no agrupadas
  Object.entries(env).forEach(([key, value]) => {
    const isGrouped = Object.values(groups).some(group => group.includes(key));
    if (!isGrouped) {
      content += `${key}=${value}\n`;
    }
  });
  
  fs.writeFileSync(envPath, content);
  console.log(`✅ Archivo .env.local actualizado: ${envPath}`);
}

/**
 * Aplica configuraciones de rendimiento
 */
function applyPerformanceConfig(environment) {
  console.log(`🚀 Aplicando configuración de rendimiento para: ${environment}`);
  
  const currentEnv = readEnvFile();
  const performanceConfig = PERFORMANCE_CONFIGS[environment];
  
  // Combinar configuraciones
  const optimizedEnv = {
    ...currentEnv,
    ...performanceConfig
  };
  
  // Escribir archivo optimizado
  writeEnvFile(optimizedEnv);
  
  return optimizedEnv;
}

/**
 * Valida la configuración de rendimiento
 */
function validatePerformanceConfig(env) {
  console.log('🔍 Validando configuración de rendimiento...');
  
  const validations = [];
  
  // Validar caché
  const cacheSize = parseInt(env.ADAPTIVE_CACHE_SIZE || '0');
  const cacheTTL = parseInt(env.ADAPTIVE_CACHE_TTL || '0');
  
  if (cacheSize < 100) {
    validations.push('⚠️  ADAPTIVE_CACHE_SIZE muy pequeño, recomendado: >= 100');
  }
  
  if (cacheTTL < 300000) { // 5 minutos
    validations.push('⚠️  ADAPTIVE_CACHE_TTL muy pequeño, recomendado: >= 300000 (5 min)');
  }
  
  // Validar timeouts
  const timeouts = {
    OPENAI_TIMEOUT_PROFILE: parseInt(env.OPENAI_TIMEOUT_PROFILE || '0'),
    OPENAI_TIMEOUT_NORMALIZE: parseInt(env.OPENAI_TIMEOUT_NORMALIZE || '0'),
    OPENAI_TIMEOUT_EMBEDDINGS: parseInt(env.OPENAI_TIMEOUT_EMBEDDINGS || '0'),
  };
  
  Object.entries(timeouts).forEach(([key, value]) => {
    if (value < 1000) {
      validations.push(`⚠️  ${key} muy pequeño, recomendado: >= 1000ms`);
    }
    if (value > 10000) {
      validations.push(`⚠️  ${key} muy grande, recomendado: <= 10000ms`);
    }
  });
  
  // Validar paralelismo
  const parallelLimit = parseInt(env.PARALLEL_EMBEDDING_LIMIT || '0');
  if (parallelLimit < 1 || parallelLimit > 8) {
    validations.push('⚠️  PARALLEL_EMBEDDING_LIMIT fuera de rango, recomendado: 1-8');
  }
  
  // Mostrar resultados
  if (validations.length === 0) {
    console.log('✅ Configuración de rendimiento válida');
  } else {
    console.log('⚠️  Advertencias de configuración:');
    validations.forEach(warning => console.log(`   ${warning}`));
  }
  
  return validations.length === 0;
}

/**
 * Muestra resumen de configuración
 */
function showConfigSummary(env, environment) {
  console.log('\n📊 RESUMEN DE CONFIGURACIÓN DE RENDIMIENTO');
  console.log('=' .repeat(50));
  console.log(`Entorno: ${environment.toUpperCase()}`);
  console.log(`Fecha: ${new Date().toLocaleString()}`);
  console.log('');
  
  console.log('🗄️  CACHÉ:');
  console.log(`   Tamaño máximo: ${env.ADAPTIVE_CACHE_SIZE} entradas`);
  console.log(`   TTL: ${Math.round(parseInt(env.ADAPTIVE_CACHE_TTL) / 60000)} minutos`);
  console.log(`   Predictivo: ${env.PREDICTIVE_CACHE_ENABLED === 'true' ? 'Habilitado' : 'Deshabilitado'}`);
  console.log(`   Monitoreo: ${env.PERFORMANCE_MONITORING === 'true' ? 'Habilitado' : 'Deshabilitado'}`);
  
  console.log('\n⏱️  TIMEOUTS:');
  console.log(`   Detección perfil: ${env.OPENAI_TIMEOUT_PROFILE}ms`);
  console.log(`   Normalización: ${env.OPENAI_TIMEOUT_NORMALIZE}ms`);
  console.log(`   Embeddings: ${env.OPENAI_TIMEOUT_EMBEDDINGS}ms`);
  
  console.log('\n⚡ PARALELIZACIÓN:');
  console.log(`   Embeddings paralelos: ${env.PARALLEL_EMBEDDING_LIMIT}`);
  console.log(`   Modelo análisis: ${env.AI_MODEL_QUERY_ANALYSIS}`);
  
  console.log('\n🎯 MÉTRICAS ESPERADAS:');
  console.log('   Tiempo respuesta: <1s (95% consultas)');
  console.log('   Cache hit ratio: >70%');
  console.log('   Reducción llamadas OpenAI: ~60%');
  console.log('   Throughput: +50% consultas/segundo');
}

/**
 * Función principal
 */
function main() {
  console.log('🚀 OPTIMIZADOR DE RENDIMIENTO - DÍA 5');
  console.log('=' .repeat(50));
  
  try {
    // Detectar entorno
    const environment = detectEnvironment();
    console.log(`🔍 Entorno detectado: ${environment}`);
    
    // Aplicar configuración
    const optimizedEnv = applyPerformanceConfig(environment);
    
    // Validar configuración
    const isValid = validatePerformanceConfig(optimizedEnv);
    
    // Mostrar resumen
    showConfigSummary(optimizedEnv, environment);
    
    if (isValid) {
      console.log('\n✅ Optimización completada exitosamente');
      console.log('💡 Reinicia el servidor para aplicar los cambios');
    } else {
      console.log('\n⚠️  Optimización completada con advertencias');
      console.log('💡 Revisa las advertencias antes de continuar');
    }
    
  } catch (error) {
    console.error('❌ Error durante la optimización:', error.message);
    process.exit(1);
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  main();
}

module.exports = {
  applyPerformanceConfig,
  validatePerformanceConfig,
  PERFORMANCE_CONFIGS
};
