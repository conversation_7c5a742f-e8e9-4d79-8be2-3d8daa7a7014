"use client";

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { useMutation, useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Building2, Users, UserCheck, Shield } from 'lucide-react';

export default function OnboardingPage() {
  const { user } = useUser();
  const router = useRouter();
  const [step, setStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);

  // Estados del formulario
  const [formData, setFormData] = useState({
    role: '',
    phone: '',
    company: '',
    license: '',
    bio: '',
    currency: 'USD',
    notifications: true,
    newsletter: false,
  });

  const updateUser = useMutation(api.users.updateProfile);
  const startTrial = useMutation(api.subscriptions.startTrial);
  const currentUser = useQuery(api.users.getUserByToken,
    user ? { tokenIdentifier: user.id } : "skip"
  );

  // Si el usuario ya completó el onboarding, redirigir
  useEffect(() => {
    if (currentUser && currentUser.role) {
      router.push('/dashboard');
    }
  }, [currentUser, router]);

  const handleNext = () => {
    if (step < 3) setStep(step + 1);
  };

  const handleBack = () => {
    if (step > 1) setStep(step - 1);
  };

  const handleSubmit = async () => {
    if (!formData.role) {
      toast.error('Por favor selecciona un rol');
      return;
    }

    setIsLoading(true);
    try {
      // Actualizar perfil del usuario
      await updateUser({
        role: formData.role as any,
        phone: formData.phone,
        company: formData.company,
        license: formData.license,
        bio: formData.bio,
        currency: formData.currency,
        notifications: formData.notifications,
        newsletter: formData.newsletter,
      });

      // Si es seller o agent, iniciar trial automáticamente
      if (formData.role === 'seller' || formData.role === 'agent') {
        try {
          const trialResult = await startTrial({
            userRole: formData.role as 'seller' | 'agent'
          });

          if (trialResult.success) {
            toast.success(`¡Perfil completado! 🎉 Tienes ${trialResult.trialDays} días de acceso premium gratis.`);
          } else {
            toast.success('¡Perfil completado exitosamente!');
          }
        } catch (trialError: any) {
          console.log('Info trial:', trialError.message);
          toast.success('¡Perfil completado exitosamente!');
          // No fallar el onboarding si hay error en trial
        }
      } else {
        toast.success('¡Perfil completado exitosamente!');
      }

      router.push('/dashboard');
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Error al completar el perfil');
    } finally {
      setIsLoading(false);
    }
  };

  const roleOptions = [
    {
      value: 'buyer',
      label: 'Comprador',
      description: 'Busco propiedades para comprar o alquilar',
      icon: Users,
    },
    {
      value: 'seller',
      label: 'Vendedor/Propietario',
      description: 'Tengo propiedades propias que quiero vender o alquilar',
      icon: Building2,
    },
    {
      value: 'agent',
      label: 'Agente Inmobiliario',
      description: 'Represento a múltiples clientes y manejo propiedades de terceros',
      icon: UserCheck,
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold">
                          ¡Bienvenido a Inmova! 🏠
          </CardTitle>
          <CardDescription>
            Completa tu perfil para comenzar (Paso {step} de 3)
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Paso 1: Selección de Rol */}
          {step === 1 && (
            <div className="space-y-6">
              <div>
                <Label className="text-base font-semibold">¿Cuál es tu rol principal?</Label>
                <p className="text-sm text-muted-foreground mt-1">
                  Esto nos ayuda a personalizar tu experiencia
                </p>
              </div>

              <RadioGroup
                value={formData.role}
                onValueChange={(value) => setFormData({ ...formData, role: value })}
                className="space-y-4"
              >
                {roleOptions.map((option: any) => (
                  <div key={option.value} className="flex items-center space-x-3">
                    <RadioGroupItem value={option.value} id={option.value} />
                    <Label
                      htmlFor={option.value}
                      className="flex items-center space-x-3 cursor-pointer flex-1 p-4 rounded-lg border hover:bg-gray-50"
                    >
                      <option.icon className="h-6 w-6 text-blue-600" />
                      <div>
                        <div className="font-medium">{option.label}</div>
                        <div className="text-sm text-muted-foreground">
                          {option.description}
                        </div>
                      </div>
                    </Label>
                  </div>
                ))}
              </RadioGroup>
            </div>
          )}

          {/* Paso 2: Información de Contacto */}
          {step === 2 && (
            <div className="space-y-6">
              <div>
                <Label className="text-base font-semibold">Información de contacto</Label>
                <p className="text-sm text-muted-foreground mt-1">
                  Ayuda a otros usuarios a contactarte
                </p>
              </div>

              <div className="grid gap-4">
                <div className="space-y-2">
                  <Label htmlFor="phone">Teléfono</Label>
                  <Input
                    id="phone"
                    placeholder="+502 1234 5678"
                    value={formData.phone}
                    onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                  />
                </div>

                {formData.role === 'agent' && (
                  <>
                    <div className="space-y-2">
                      <Label htmlFor="company">Empresa/Inmobiliaria</Label>
                      <Input
                        id="company"
                        placeholder="Nombre de tu empresa"
                        value={formData.company}
                        onChange={(e) => setFormData({ ...formData, company: e.target.value })}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="license">Número de Licencia</Label>
                      <Input
                        id="license"
                        placeholder="Número de licencia profesional"
                        value={formData.license}
                        onChange={(e) => setFormData({ ...formData, license: e.target.value })}
                      />
                    </div>
                  </>
                )}

                <div className="space-y-2">
                  <Label htmlFor="bio">Biografía (opcional)</Label>
                  <Textarea
                    id="bio"
                    placeholder="Cuéntanos un poco sobre ti..."
                    value={formData.bio}
                    onChange={(e) => setFormData({ ...formData, bio: e.target.value })}
                    rows={3}
                  />
                </div>
              </div>
            </div>
          )}

          {/* Paso 3: Preferencias */}
          {step === 3 && (
            <div className="space-y-6">
              <div>
                <Label className="text-base font-semibold">Preferencias</Label>
                <p className="text-sm text-muted-foreground mt-1">
                  Configura tus preferencias iniciales
                </p>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="currency">Moneda preferida</Label>
                  <Select
                    value={formData.currency}
                    onValueChange={(value) => setFormData({ ...formData, currency: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="USD">Dólar Americano (USD)</SelectItem>
                      <SelectItem value="GTQ">Quetzal Guatemalteco (GTQ)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="font-medium">Notificaciones por email</Label>
                      <p className="text-sm text-muted-foreground">
                        Recibir notificaciones importantes
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      checked={formData.notifications}
                      onChange={(e) => setFormData({ ...formData, notifications: e.target.checked })}
                      className="h-4 w-4"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="font-medium">Newsletter</Label>
                      <p className="text-sm text-muted-foreground">
                        Recibir noticias y ofertas especiales
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      checked={formData.newsletter}
                      onChange={(e) => setFormData({ ...formData, newsletter: e.target.checked })}
                      className="h-4 w-4"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Botones de navegación */}
          <div className="flex justify-between pt-6">
            <Button
              variant="outline"
              onClick={handleBack}
              disabled={step === 1}
            >
              Anterior
            </Button>

            {step < 3 ? (
              <Button
                onClick={handleNext}
                disabled={step === 1 && !formData.role}
              >
                Siguiente
              </Button>
            ) : (
              <Button
                onClick={handleSubmit}
                disabled={isLoading}
              >
                {isLoading ? 'Completando...' : 'Completar Perfil'}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 