# 🧪 Scripts de Pruebas de Búsqueda

Este directorio contiene scripts para comparar el rendimiento entre el **Sistema Simple** y el **Sistema Multi-componente** de búsqueda semántica.

## 📋 Archivos

### **Scripts de Comparación Semántica:**
- `test-search-comparison.js` - Script principal de comparación
- `run-comparison.js` - Ejecutor simplificado
- `test-50-queries-threshold-025.js` - Test exhaustivo con 50 consultas

### **🆕 Scripts de Boost Comercial:**
- `test-boost-comercial.js` - Test completo del sistema de boost Premium/Featured
- `test-single-query.js` - Test individual rápido para consultas específicas

### **Scripts de Configuración:**
- `setup-universal-agent.js`, `initialize-rag.js`, etc.
- `README.md` - Esta documentación

## 🚀 Cómo Ejecutar las Pruebas

### Opción 1: Usando npm script (Recomendado)
```bash
npm run test:search
```

### Opción 2: Directamente con Node.js
```bash
node scripts/run-comparison.js
```

### Opción 3: Solo el script de comparación
```bash
node scripts/test-search-comparison.js
```

---

## 🎯 **Pruebas de Boost Comercial (NUEVO)**

### **Test Completo del Boost:**
```bash
# Probar sistema completo de boost Premium/Featured
node scripts/test-boost-comercial.js
```

### **Test Individual Rápido:**
```bash
# Usar consulta por defecto
node scripts/test-single-query.js

# Probar consulta personalizada
node scripts/test-single-query.js "apartamento zona 14"
node scripts/test-single-query.js "casa en venta zona 10"
```

### **Qué verifica el test de boost:**
- ✅ Propiedades **Premium** aparecen **PRIMERO** (×2.0 boost)
- ✅ Propiedades **Featured** aparecen **SEGUNDO** (×1.5 boost)  
- ✅ Propiedades **Normal** aparecen **DESPUÉS** (×1.0)
- 📊 Análisis detallado del score boost aplicado
- ⏱️ Tiempo de respuesta y performance

### **Ejemplo de salida esperada:**
```
🔍 Buscando: "apartamento zona 14"
✅ Respuesta recibida en 290ms
📊 6 resultados encontrados

🎯 RESULTADOS CON BOOST APLICADO:
1. 💎 PREMIUM
   📊 Score: 1.606 (0.803 × 2.0)
   📍 Apartamento de Lujo Zona 14
   💰 Q1,200,000

2. ⭐ FEATURED  
   📊 Score: 1.178 (0.785 × 1.5)
   📍 Apartamento Moderno Z14
   💰 Q950,000

3. 🏠 NORMAL
   📊 Score: 0.850
   📍 Apartamento Zona 14 Centro
   💰 Q750,000

📈 RESUMEN DE BOOST:
💎 Premium: 1 propiedades (×2.0 boost)
⭐ Featured: 1 propiedades (×1.5 boost)
🏠 Normal: 4 propiedades (×1.0 boost)

✅ VALIDACIÓN:
✅ Premium aparecen primero
🎯 Boost comercial: 2 propiedades promocionadas aparecen antes que las normales
```

## ⚙️ Configuración

### Variables de Entorno Requeridas
```bash
# URL de la API (por defecto: http://localhost:3000)
API_BASE_URL=http://localhost:3000

# API Key para autenticación
NEXT_PUBLIC_RAG_API_KEY=demo-rag-key-2025
```

### Dependencias
```bash
# Instalar node-fetch si no está disponible
npm install node-fetch
```

## 📊 Qué Mide la Prueba

### Métricas de Rendimiento
- ⏱️ **Tiempo de respuesta** (ms)
- 📈 **Número de resultados** encontrados
- 🎯 **Score promedio** de similitud
- ✅ **Tasa de éxito** (% consultas con resultados)

### Categorías de Consultas (25 total)
1. **Básicas** (5): "apartamento zona 14", "casa en venta"
2. **Específicas** (5): "apartamento 3 habitaciones zona 14"
3. **Abreviaciones** (5): "apto z10", "depto zona 14"
4. **Sinónimos** (5): "arrendar apartamento", "conseguir casa"
5. **Edge Cases** (5): "apartamento", "zona 14"

### Sistemas Comparados

#### Sistema Simple
```json
{
  "searchType": "simple",
  "adaptiveThreshold": true,
  "intentDetection": true
}
```

#### Sistema Multi-componente
```json
{
  "searchType": "semantic", 
  "useSemanticWeights": true,
  "includeBreakdown": true
}
```

## 📈 Interpretación de Resultados

### Ejemplo de Salida
```
🏆 MÉTRICAS GLOBALES:
┌─────────────────────┬─────────────────┬─────────────────┬─────────────┐
│ Métrica             │ Sistema Simple  │ Multi-componente│ Ganador     │
├─────────────────────┼─────────────────┼─────────────────┼─────────────┤
│ Tiempo Promedio     │       800ms     │      3200ms     │ Simple 🏆   │
│ Resultados Promedio │         5.2     │         5.1     │ Simple 🏆   │
│ Score Promedio      │       0.456     │       0.461     │ Multi 🏆    │
│ Tasa de Éxito       │        92.0%    │        90.0%    │ Simple 🏆   │
└─────────────────────┴─────────────────┴─────────────────┴─────────────┘

📈 DIFERENCIAS PORCENTUALES:
⏱️  Sistema Simple es 75.0% más rápido
📊 Sistema Simple encuentra 2.0% más resultados  
🎯 Sistema Simple tiene 1.1% menor score promedio

🎯 RECOMENDACIÓN:
✅ USAR SISTEMA SIMPLE: Más rápido con calidad similar
```

### Criterios de Decisión
- **Usar Simple**: Si es >50% más rápido y diferencia de score <10%
- **Usar Multi-componente**: Si score es >10% mejor
- **Ambos viables**: Si diferencias son menores

## 🔧 Personalización

### Modificar Consultas de Prueba
Editar `TEST_QUERIES` en `test-search-comparison.js`:

```javascript
const TEST_QUERIES = {
  basicas: [
    "apartamento zona 14",
    "tu consulta personalizada"
  ],
  // ...
};
```

### Cambiar Número de Iteraciones
Modificar `ITERATIONS_PER_QUERY` (por defecto: 3):

```javascript
const ITERATIONS_PER_QUERY = 5; // Más iteraciones = más precisión
```

### Ajustar Configuración de Sistemas
Modificar `SYSTEM_CONFIGS` para probar diferentes configuraciones.

## 🐛 Solución de Problemas

### Error: "node-fetch no está instalado"
```bash
npm install node-fetch
```

### Error: "HTTP error! status: 500"
- Verificar que el servidor Next.js esté ejecutándose
- Verificar variables de entorno
- Revisar logs del servidor

### Error: "API_BASE_URL no responde"
- Verificar URL en variables de entorno
- Probar manualmente: `curl http://localhost:3000/api/v1/search`

## 📝 Notas Técnicas

- Cada consulta se ejecuta **3 veces** y se promedian los resultados
- Hay una pausa de **500ms** entre iteraciones para evitar sobrecarga
- Los resultados se muestran en tiempo real durante la ejecución
- El script es compatible con Node.js 14+ y Next.js 13+
