/**
 * Configuración de Pesos Semánticos para Sistema de Embeddings Multi-Componente
 * 
 * Este archivo define los perfiles de pesos para diferentes tipos de consultas
 * y la lógica de detección automática del perfil apropiado.
 */

// Importar tipos desde configuración unificada para evitar duplicación
import { SemanticWeights } from './unified-config';

export interface WeightProfile {
  name: string;
  description: string;
  weights: SemanticWeights;
  detectionCriteria: string;
}

// Perfiles de pesos predefinidos - SISTEMA EXPANDIDO (11 ámbitos)
export const SEMANTIC_WEIGHT_PROFILES: Record<string, WeightProfile> = {
  // 1. UBICACIÓN ESPECÍFICA
  LOCATION_FOCUSED: {
    name: 'LOCATION_FOCUSED',
    description: 'Para consultas con ubicación específica como "apartamento zona 14"',
    weights: {
      location: 0.70,
      property: 0.15,
      amenities: 0.08,
      characteristics: 0.05,
      price: 0.02,
    },
    detectionCriteria: 'Ubicación específica detectada (zona, colonia, área específica)',
  },

  // 2. TIPO DE PROPIEDAD
  PROPERTY_TYPE_FOCUSED: {
    name: 'PROPERTY_TYPE_FOCUSED',
    description: 'Para consultas enfocadas en tipo de propiedad como "casa", "apartamento", "oficina"',
    weights: {
      location: 0.30,
      property: 0.45,
      amenities: 0.12,
      characteristics: 0.08,
      price: 0.05,
    },
    detectionCriteria: 'Tipo de propiedad específico mencionado',
  },

  // 3. HABITACIONES Y BAÑOS (AHORA USA CHARACTERISTICS)
  ROOMS_SPECS_FOCUSED: {
    name: 'ROOMS_SPECS_FOCUSED',
    description: 'Para consultas sobre habitaciones/baños como "3 habitaciones", "2 baños"',
    weights: {
      location: 0.25,
      property: 0.30,
      amenities: 0.10,
      characteristics: 0.30,
      price: 0.05,
    },
    detectionCriteria: 'Número específico de habitaciones o baños mencionado',
  },

  // 4. ÁREA/METROS CUADRADOS (AHORA USA CHARACTERISTICS)
  SIZE_FOCUSED: {
    name: 'SIZE_FOCUSED',
    description: 'Para consultas sobre área como "200 m2", "casa grande"',
    weights: {
      location: 0.30,
      property: 0.25,
      amenities: 0.10,
      characteristics: 0.30,
      price: 0.05,
    },
    detectionCriteria: 'Área específica o descriptores de tamaño mencionados',
  },

  // 5. AMENIDADES ESPECÍFICAS
  AMENITIES_FOCUSED: {
    name: 'AMENITIES_FOCUSED',
    description: 'Para consultas con amenidades como "piscina", "gimnasio", "terraza"',
    weights: {
      location: 0.25,
      property: 0.20,
      amenities: 0.45,
      characteristics: 0.05,
      price: 0.05,
    },
    detectionCriteria: 'Una o más amenidades específicas mencionadas',
  },

  // 6. PRESUPUESTO/PRECIO
  BUDGET_FOCUSED: {
    name: 'BUDGET_FOCUSED',
    description: 'Para consultas sobre precio como "menos de Q500k", "económico"',
    weights: {
      location: 0.30,
      property: 0.20,
      amenities: 0.05,
      characteristics: 0.05,
      price: 0.40,
    },
    detectionCriteria: 'Rango de precio o términos económicos mencionados',
  },

  // 7. INTENCIÓN DE COMPRA/RENTA
  STATUS_FOCUSED: {
    name: 'STATUS_FOCUSED',
    description: 'Para consultas de intención como "quiero comprar", "para alquilar"',
    weights: {
      location: 0.35,
      property: 0.35,
      amenities: 0.15,
      characteristics: 0.10,
      price: 0.05,
    },
    detectionCriteria: 'Intención de compra o renta explícita',
  },

  // 8. EDAD/AÑO DE CONSTRUCCIÓN
  AGE_FOCUSED: {
    name: 'AGE_FOCUSED',
    description: 'Para consultas sobre edad como "casa nueva", "construcción reciente"',
    weights: {
      location: 0.30,
      property: 0.40,
      amenities: 0.15,
      characteristics: 0.10,
      price: 0.05,
    },
    detectionCriteria: 'Año de construcción o términos de edad mencionados',
  },

  // 9. CARACTERÍSTICAS ESPECIALES
  FEATURES_FOCUSED: {
    name: 'FEATURES_FOCUSED',
    description: 'Para consultas sobre características como "con tour virtual", "destacada"',
    weights: {
      location: 0.25,
      property: 0.35,
      amenities: 0.25,
      characteristics: 0.10,
      price: 0.05,
    },
    detectionCriteria: 'Características especiales o servicios adicionales mencionados',
  },

  // 10. CRITERIOS MÚLTIPLES
  MIXED_CRITERIA: {
    name: 'MIXED_CRITERIA',
    description: 'Para consultas con múltiples criterios como "casa 3 hab zona 10 con piscina"',
    weights: {
      location: 0.30,
      property: 0.25,
      amenities: 0.20,
      characteristics: 0.20,
      price: 0.05,
    },
    detectionCriteria: '3 o más criterios de diferentes categorías detectados',
  },

  // 11. BÚSQUEDA GENERAL
  GENERAL_SEARCH: {
    name: 'GENERAL_SEARCH',
    description: 'Para consultas generales o vagas como "propiedad", "algo bonito"',
    weights: {
      location: 0.35,
      property: 0.25,
      amenities: 0.20,
      characteristics: 0.10,
      price: 0.10,
    },
    detectionCriteria: 'Consulta general sin criterios específicos',
  },

  // PERFIL BALANCEADO (LEGACY - mantener compatibilidad)
  BALANCED: {
    name: 'BALANCED',
    description: 'Perfil balanceado para compatibilidad con sistema anterior',
    weights: {
      location: 0.35,
      property: 0.25,
      amenities: 0.20,
      characteristics: 0.10,
      price: 0.10,
    },
    detectionCriteria: 'Fallback para compatibilidad',
  },

  // PERFIL LEGACY (mantener compatibilidad)
  FEATURE_FOCUSED: {
    name: 'FEATURE_FOCUSED',
    description: 'Perfil legacy para múltiples amenidades',
    weights: {
      location: 0.25,
      property: 0.20,
      amenities: 0.45,
      characteristics: 0.05,
      price: 0.05,
    },
    detectionCriteria: 'Legacy: 3 o más amenidades mencionadas',
  },
};

// ❌ LEGACY: Configuración por defecto del sistema multi-componente DESCARTADO
// @deprecated Sistema multi-componente descartado tras validación empírica (27 Jun 2025)
// ✅ USAR EN SU LUGAR: Sistema Simple con searchPropertiesSimple()
export const DEFAULT_SEMANTIC_WEIGHTS: SemanticWeights = SEMANTIC_WEIGHT_PROFILES.BALANCED.weights;

/**
 * Detecta automáticamente el perfil de pesos apropiado para una consulta
 * LEGACY FUNCTION - Mantener para compatibilidad con sistema anterior
 *
 * NUEVO: Usar QueryProfileDetector para detección inteligente con OpenAI
 */
export function detectWeightProfile(query: string, decomposedQuery?: {
  location?: string;
  property?: string;
  amenities?: string;
  price?: string;
}): WeightProfile {
  console.log('⚠️  Usando detector legacy - considera migrar a QueryProfileDetector');

  const queryLower = query.toLowerCase();

  // Detectar ubicación específica
  const locationPatterns = [
    /zona\s*\d+/i,
    /z\d+/i,
    /colonia\s+\w+/i,
    /barrio\s+\w+/i,
    /sector\s+\w+/i,
    /área\s+\w+/i,
    /centro\s+histórico/i,
    /ciudad\s+\w+/i,
  ];

  const hasSpecificLocation = locationPatterns.some(pattern => pattern.test(queryLower)) ||
    (decomposedQuery?.location && decomposedQuery.location.trim().length > 0);

  // Detectar múltiples amenidades
  const amenityPatterns = [
    /piscina/i, /alberca/i, /pool/i,
    /gimnasio/i, /gym/i,
    /terraza/i, /balcón/i, /balcon/i,
    /jardín/i, /jardin/i, /garden/i,
    /garaje/i, /cochera/i, /parking/i,
    /seguridad/i, /vigilancia/i,
    /elevador/i, /ascensor/i,
    /aire\s+acondicionado/i, /a\/c/i,
    /amueblado/i, /furnished/i,
    /vista/i, /view/i,
  ];

  const amenityCount = amenityPatterns.filter(pattern => pattern.test(queryLower)).length;
  const hasMultipleAmenities = amenityCount >= 3 ||
    (decomposedQuery?.amenities && decomposedQuery.amenities.split(',').length >= 3);

  // Lógica de detección legacy
  if (hasSpecificLocation && !hasMultipleAmenities) {
    return SEMANTIC_WEIGHT_PROFILES.LOCATION_FOCUSED;
  }

  if (hasMultipleAmenities) {
    return SEMANTIC_WEIGHT_PROFILES.AMENITIES_FOCUSED; // Actualizado para usar nuevo perfil
  }

  return SEMANTIC_WEIGHT_PROFILES.GENERAL_SEARCH; // Actualizado para usar nuevo perfil
}

/**
 * Detecta perfil usando el nuevo sistema inteligente con OpenAI
 * RECOMENDADO: Usar esta función para nueva funcionalidad
 */
export async function detectWeightProfileIntelligent(query: string): Promise<WeightProfile> {
  try {
    // Importación dinámica para evitar dependencias circulares
    const { QueryProfileDetector } = await import('./query-profile-detector');
    const detector = new QueryProfileDetector();

    const result = await detector.detectProfile(query);

    console.log(`🎯 Perfil detectado: ${result.profile.name} (confianza: ${result.analysis.confidence.toFixed(2)})`);
    console.log(`📊 Criterios: ${result.analysis.primaryCriteria.join(', ')}`);

    return result.profile;

  } catch (error) {
    console.warn('Error en detección inteligente, usando fallback:', error);
    return detectWeightProfile(query); // Fallback al sistema legacy
  }
}

/**
 * Valida que los pesos sumen exactamente 1.0
 */
export function validateWeights(weights: SemanticWeights): boolean {
  const sum = weights.location + weights.property + weights.amenities + weights.characteristics + weights.price;
  const tolerance = 0.001; // Tolerancia para errores de punto flotante
  return Math.abs(sum - 1.0) < tolerance;
}

/**
 * Normaliza los pesos para que sumen exactamente 1.0
 */
export function normalizeWeights(weights: SemanticWeights): SemanticWeights {
  const sum = weights.location + weights.property + weights.amenities + weights.characteristics + weights.price;
  
  if (sum === 0) {
    // Si todos los pesos son 0, usar distribución balanceada
    return { ...DEFAULT_SEMANTIC_WEIGHTS };
  }
  
  return {
    location: weights.location / sum,
    property: weights.property / sum,
    amenities: weights.amenities / sum,
    characteristics: weights.characteristics / sum,
    price: weights.price / sum,
  };
}

/**
 * Obtiene los pesos configurados desde variables de entorno con validación
 */
export function getConfiguredWeights(): SemanticWeights {
  const weights: SemanticWeights = {
    location: parseFloat(process.env.SEMANTIC_WEIGHT_LOCATION || '0.70'),
    property: parseFloat(process.env.SEMANTIC_WEIGHT_PROPERTY || '0.15'),
    amenities: parseFloat(process.env.SEMANTIC_WEIGHT_AMENITIES || '0.08'),
    characteristics: parseFloat(process.env.SEMANTIC_WEIGHT_CHARACTERISTICS || '0.05'),
    price: parseFloat(process.env.SEMANTIC_WEIGHT_PRICE || '0.02'),
  };

  // Validar y normalizar si es necesario
  if (!validateWeights(weights)) {
    console.warn('Los pesos semánticos configurados no suman 1.0, normalizando automáticamente');
    return normalizeWeights(weights);
  }

  return weights;
}

/**
 * Obtiene información detallada sobre la configuración actual
 */
export function getWeightConfigInfo(): {
  configuredWeights: SemanticWeights;
  isValid: boolean;
  profiles: Record<string, WeightProfile>;
  source: 'environment' | 'default';
} {
  const configuredWeights = getConfiguredWeights();
  const isValid = validateWeights(configuredWeights);
  
  // Determinar si se están usando valores de entorno o por defecto
  const hasEnvVars = !!(
    process.env.SEMANTIC_WEIGHT_LOCATION ||
    process.env.SEMANTIC_WEIGHT_PROPERTY ||
    process.env.SEMANTIC_WEIGHT_AMENITIES ||
    process.env.SEMANTIC_WEIGHT_CHARACTERISTICS ||
    process.env.SEMANTIC_WEIGHT_PRICE
  );

  return {
    configuredWeights,
    isValid,
    profiles: SEMANTIC_WEIGHT_PROFILES,
    source: hasEnvVars ? 'environment' : 'default',
  };
}

// Validación en tiempo de carga del módulo
const configInfo = getWeightConfigInfo();
if (!configInfo.isValid) {
  console.warn('⚠️  Configuración de pesos semánticos inválida detectada al cargar el módulo');
}

console.log(`✅ Configuración de pesos semánticos cargada (fuente: ${configInfo.source})`);
