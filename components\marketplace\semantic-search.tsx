"use client";

import React, { useState, useCallback, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, Sparkles, Loader2, Brain, Filter, X, MapPin } from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { toast } from "sonner";


interface SemanticSearchProps {
  variant?: "home" | "page";
  className?: string;
}



interface SearchResult {
  id: string;
  score: number;
  property: {
    propertyId: string;
    title: string; // ✅ AGREGAR: Título real de la propiedad
    address: string; // ✅ AGREGAR: Dirección de la propiedad
    type: string;
    status: string;
    price: number;
    currency: string;
    bedrooms: number;
    bathrooms: number;
    area: number;
    location: {
      country: string;
      level1?: string;
      level2?: string;
      level3?: string;
      level4?: string;
      neighborhood?: string;
    };
    coordinates?: {
      lat: number;
      lon: number;
    };
    amenities: string[];
    images: string[];
    isActive: boolean;
    isFeatured: boolean;
    isPremium: boolean;
    createdAt: string;
    updatedAt: string;
  };
}

interface SearchResponse {
  query: string;
  searchType: string;
  resultsCount: number;
  properties: SearchResult[];
  aiResponse?: {
    text: string;
    summary: any;
  };
  timestamp: string;
}

// Función para formatear la ubicación completa
const formatLocation = (location: SearchResult['property']['location']): string => {
  const parts = [
    location.level4,
    location.level3,
    location.level2,
    location.level1,
    location.country
  ].filter(Boolean);

  return parts.join(', ');
};

export function SemanticSearch({ variant = "home", className = "" }: SemanticSearchProps) {
  const [query, setQuery] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [results, setResults] = useState<SearchResult[]>([]);
  const [aiResponse, setAiResponse] = useState<string>("");
  const [searchType, setSearchType] = useState<"semantic" | "traditional">("semantic");
  const [showResults, setShowResults] = useState(false);
  const [showPropertySummary, setShowPropertySummary] = useState(false);

  const isHomeVariant = variant === "home";

  // ✅ ACTUALIZADO: Función para obtener categoría de relevancia (compatible con boost comercial)
  const getRelevanceCategory = (score: number) => {
    // Detectar si es score boosted y calcular score original para categorización
    let originalScore = score;
    let boostInfo = "";
    
    // Si score > 1.0, probablemente está boosted
    if (score > 1.0) {
      if (score >= 1.4 && score <= 2.2) {
        // Probablemente Premium (×2.0 boost)
        originalScore = score / 2.0;
        boostInfo = " 💎";
      } else if (score >= 1.2 && score < 1.4) {
        // Probablemente Featured (×1.5 boost) 
        originalScore = score / 1.5;
        boostInfo = " ⭐";
      }
    }
    
    // Usar score original para categorización
    if (originalScore >= 0.95 || score > 1.8) return {
      label: "COINCIDENCIA EXACTA",
      color: "green",
      bgColor: "bg-green-100",
      textColor: "text-green-800",
      borderColor: "border-green-300",
      icon: "🎯",
      boostInfo
    };
    if (originalScore >= 0.80 || score > 1.2) return {
      label: "COINCIDENCIA PARCIAL",
      color: "blue",
      bgColor: "bg-blue-100",
      textColor: "text-blue-800",
      borderColor: "border-blue-300",
      icon: "🔄",
      boostInfo
    };
    if (originalScore >= 0.60 || score > 0.9) return {
      label: "ALTERNATIVA RELEVANTE",
      color: "yellow",
      bgColor: "bg-yellow-100",
      textColor: "text-yellow-800",
      borderColor: "border-yellow-300",
      icon: "💡",
      boostInfo
    };
    return {
      label: "BAJA RELEVANCIA",
      color: "red",
      bgColor: "bg-red-100",
      textColor: "text-red-800",
      borderColor: "border-red-300",
      icon: "⚠️",
      boostInfo
    };
  };

  // ✅ SIMPLIFICADO: Renderizar resultados como cards limpias
  const renderSimplifiedResults = (searchResults: SearchResult[]) => {
    if (!searchResults.length) return null;

    // Ordenar resultados por score (mayor a menor)
    const sortedResults = [...searchResults].sort((a, b) => b.score - a.score);

    return (
      <div className="space-y-4">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200 mb-6">
          <div className="text-center">
            <h3 className="text-xl font-bold text-gray-900 mb-2">
              {sortedResults.length} propiedades encontradas
            </h3>
            <p className="text-gray-600">
              {sortedResults.some(r => r.score > 1.0) 
                ? "Resultados ordenados por relevancia y promoción comercial" 
                : "Resultados ordenados por relevancia"}
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {sortedResults.map((result, index) => {
            const property = result.property;
            const isTopResult = index === 0;
            
            // Calcular score original para mostrar
            let displayScore = result.score;
            let boostInfo = "";
            
            if (result.score > 1.0) {
              if (result.score >= 1.4 && result.score <= 2.2) {
                displayScore = result.score / 2.0;
                // ✅ CORREGIDO: No mostrar boost "Premium" si ya tiene badge Premium
                if (!property.isPremium) {
                  boostInfo = "💎 Premium";
                }
              } else if (result.score >= 1.2 && result.score < 1.4) {
                displayScore = result.score / 1.5;
                // ✅ CORREGIDO: No mostrar boost "Destacada" si ya tiene badge Destacada
                if (!property.isFeatured) {
                  boostInfo = "⭐ Destacada";
                }
              }
            }

            const category = getRelevanceCategory(result.score);

            return (
              <div
                key={result.id}
                className={`bg-white rounded-lg border shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden ${
                  isTopResult ? 'ring-2 ring-yellow-400 border-yellow-400' : 'border-gray-200'
                }`}
              >
                                  {/* ✅ OPTIMIZADO: Header más compacto */}
                <div className="p-3 border-b border-gray-100">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      {isTopResult && (
                        <Badge className="bg-yellow-100 text-yellow-800 border-yellow-300">
                          🏆 Top
                        </Badge>
                      )}
                      {/* ✅ Badge de Premium */}
                      {property.isPremium && (
                        <Badge className="bg-gradient-to-r from-purple-400 to-pink-400 text-white border-0 font-bold">
                          👑 PREMIUM
                        </Badge>
                      )}
                      {/* ✅ Badge de Destacada */}
                      {property.isFeatured && (
                        <Badge className="bg-gradient-to-r from-amber-400 to-orange-500 text-white border-0 font-bold">
                          ⭐ DESTACADA
                        </Badge>
                      )}
                      {boostInfo && (
                        <Badge className="bg-blue-100 text-blue-800 border-blue-300">
                          {boostInfo}
                        </Badge>
                      )}
                    </div>
                    <Badge className={`${category.bgColor} ${category.textColor} ${category.borderColor} border text-xs`}>
                      {Math.min(Math.round(displayScore * 100), 100)}%
                    </Badge>
                  </div>
                  
                  {/* ✅ CORREGIDO: Usar título real en lugar de auto-generado */}
                  <h4 className="font-bold text-gray-900 text-base mb-1 line-clamp-2">
                    {property.title || `${property.type === 'apartment' ? 'Apartamento' : 
                     property.type === 'house' ? 'Casa' : 
                     property.type === 'office' ? 'Oficina' : 
                     property.type === 'commercial' ? 'Local Comercial' : 'Propiedad'}${property.bedrooms > 0 ? ` ${property.bedrooms} habitaciones` : ''}`}
                  </h4>
                  
                  {/* ✅ OPTIMIZADO: Solo ubicación, sin dirección para hacer tarjetas más compactas */}
                  <p className="text-gray-600 text-sm">
                    📍 {formatLocation(property.location)}
                  </p>
                </div>

                {/* ✅ OPTIMIZADO: Imagen más pequeña para tarjetas compactas */}
                <div className="relative h-40 bg-gray-100">
                  {property.images && property.images.length > 0 && property.images[0] ? (
                    <Image
                      src={property.images[0]}
                      alt={property.title || `Imagen de ${property.type === 'apartment' ? 'apartamento' : 
                             property.type === 'house' ? 'casa' : 
                             property.type === 'office' ? 'oficina' : 'propiedad'}`}
                      fill
                      className="object-cover"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    />
                  ) : (
                    <div className="h-full flex items-center justify-center bg-gray-200">
                      <div className="text-center text-gray-500">
                        <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <p className="text-sm">Sin imagen disponible</p>
                      </div>
                    </div>
                  )}
                  
                  {/* Badge de múltiples imágenes */}
                  {property.images && property.images.length > 1 && (
                    <div className="absolute top-3 right-3 bg-black/70 text-white px-2 py-1 rounded-md text-xs font-medium backdrop-blur-sm">
                      +{property.images.length - 1} fotos
                    </div>
                  )}
                </div>

                {/* ✅ OPTIMIZADO: Contenido más compacto */}
                <div className="p-3 space-y-3">
                  {/* Precio destacado */}
                  <div className="bg-blue-50 rounded-lg p-3 border border-blue-100">
                    <div className="flex items-center justify-between">
                      <div>
                        <span className="text-blue-700 text-sm font-medium">Precio</span>
                        <div className="font-bold text-xl text-blue-900">
                          {property.currency} ${property.price?.toLocaleString() || 'Consultar'}
                        </div>
                      </div>
                      <div className="text-right">
                        <span className="text-blue-700 text-sm font-medium">Estado</span>
                        <div className="font-semibold text-blue-800">
                          {property.status === 'for_sale' ? 'En Venta' : 
                           property.status === 'for_rent' ? 'En Alquiler' : property.status}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Características principales */}
                  <div className="grid grid-cols-3 gap-3">
                    <div className="text-center bg-gray-50 rounded-lg p-3">
                      <div className="text-2xl font-bold text-gray-900">{property.bedrooms || '0'}</div>
                      <div className="text-xs text-gray-500 mt-1">🛏️ Habitaciones</div>
                    </div>
                    <div className="text-center bg-gray-50 rounded-lg p-3">
                      <div className="text-2xl font-bold text-gray-900">{property.bathrooms || '0'}</div>
                      <div className="text-xs text-gray-500 mt-1">🚿 Baños</div>
                    </div>
                    <div className="text-center bg-gray-50 rounded-lg p-3">
                      <div className="text-2xl font-bold text-gray-900">{property.area || '0'}</div>
                      <div className="text-xs text-gray-500 mt-1">📐 m²</div>
                    </div>
                  </div>

                  {/* Amenidades */}
                  {property.amenities && property.amenities.length > 0 && (
                    <div>
                      <span className="text-gray-700 text-sm font-medium block mb-2">✨ Amenidades</span>
                      <div className="flex flex-wrap gap-1">
                        {property.amenities.slice(0, 4).map((amenity, i) => (
                          <span key={i} className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800 border border-green-200">
                            {amenity}
                          </span>
                        ))}
                        {property.amenities.length > 4 && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-600 border border-gray-200">
                            +{property.amenities.length - 4} más
                          </span>
                        )}
                      </div>
                    </div>
                  )}
                </div>

                {/* ✅ OPTIMIZADO: Footer más compacto */}
                <div className="p-3 bg-gray-50 border-t border-gray-100">
                  <Link
                    href={`/properties/${property.propertyId || result.id}`}
                    className={`w-full inline-flex items-center justify-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                      isTopResult
                        ? 'bg-yellow-500 hover:bg-yellow-600 text-white shadow-lg hover:shadow-xl'
                        : 'bg-blue-600 hover:bg-blue-700 text-white shadow-md hover:shadow-lg'
                    }`}
                  >
                    <span>Ver detalles</span>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const handleSearch = async () => {
    if (!query.trim()) {
      toast.error("Por favor ingresa una búsqueda");
      return;
    }

    setIsSearching(true);
    setShowResults(true);
    setShowPropertySummary(false); // Resetear el estado del resumen

    try {
      console.log(`Performing ${searchType} search for: "${query}"`);

      if (searchType === "semantic") {
        // Búsqueda semántica con RAG
        const response = await fetch('/api/v1/search', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-API-Key': process.env.NEXT_PUBLIC_RAG_API_KEY || 'demo-rag-key-2025',
          },
          body: JSON.stringify({
            query,
            options: {
              limit: 12,
              includeResponse: true,
              responseLanguage: 'es',
              // scoreThreshold se maneja en el servidor usando SEARCH_SCORE_THRESHOLD
              searchType: 'simple', // ✅ OPTIMIZADO: Sistema Simple (3.2x más rápido, 49% mejor precisión)
              adaptiveThreshold: true, // ✅ MANTENER: Threshold adaptativo
              intentDetection: true, // ✅ MANTENER: Detección de intención
              // ❌ REMOVIDO: useSemanticWeights (sistema multi-componente más lento y menos preciso)
            },
          }),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data: SearchResponse = await response.json();
        setResults(data.properties);
        setAiResponse(data.aiResponse?.text || "");

        toast.success(`Encontré ${data.resultsCount} propiedades relevantes`);
      } else {
        // Búsqueda tradicional (fallback)
        // Aquí podrías llamar a la API tradicional de Convex
        toast.info("Búsqueda tradicional no implementada en este componente");
      }
    } catch (error) {
      console.error('Error in semantic search:', error);
      
      // Fallback a búsqueda tradicional
      toast.error("Error en búsqueda semántica, usando búsqueda tradicional");
      setSearchType("traditional");
      
      // Aquí podrías implementar un fallback a la búsqueda tradicional
      setResults([]);
      setAiResponse("Lo siento, no pude procesar tu búsqueda semántica. Intenta con términos más específicos.");
    } finally {
      setIsSearching(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const clearSearch = () => {
    setQuery("");
    setResults([]);
    setAiResponse("");
    setShowResults(false);
    setShowPropertySummary(false);
  };

  const toggleSearchType = () => {
    setSearchType(prev => prev === "semantic" ? "traditional" : "semantic");
    toast.info(`Cambiado a búsqueda ${searchType === "semantic" ? "tradicional" : "semántica"}`);
  };

  return (
    <div className={`${className}`}>
      {/* Componente de búsqueda */}
      <div className={`${isHomeVariant ? 'bg-white rounded-3xl shadow-2xl border border-white/20' : 'bg-white rounded-2xl shadow-lg'} p-4 md:p-6`}>
        <div className="space-y-4 md:space-y-6">
          {/* Header con indicador de tipo de búsqueda */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {searchType === "semantic" ? (
                <Brain className="h-5 w-5 text-blue-600" />
              ) : (
                <Filter className="h-5 w-5 text-blue-600" />
              )}
              <span className="text-sm font-medium text-gray-700">
                {searchType === "semantic" ? "Búsqueda Inteligente" : "Búsqueda Tradicional"}
              </span>
              <Badge
                variant={searchType === "semantic" ? "default" : "secondary"}
                className={searchType === "semantic" ? "bg-blue-100 text-blue-700" : ""}
              >
                {searchType === "semantic" ? "IA" : "Filtros"}
              </Badge>
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleSearchType}
              className="text-gray-600 hover:text-blue-600"
            >
              Cambiar modo
            </Button>
          </div>

          {/* Campo de búsqueda principal */}
          <div className="space-y-4">
            <div className="relative">
              {searchType === "semantic" ? (
                <Sparkles className="absolute left-4 top-1/2 transform -translate-y-1/2 text-blue-500 h-5 w-5" />
              ) : (
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              )}
              <Input
                placeholder={
                  searchType === "semantic"
                    ? "Describe lo que buscas: 'casa moderna con jardín cerca del centro'..."
                    : "Buscar por palabras clave: 'apartamento zona 14'..."
                }
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                onKeyPress={handleKeyPress}
                className={`pl-12 ${isHomeVariant ? 'h-12 md:h-16' : 'h-12'} text-gray-900 border-0 ${
                  searchType === "semantic"
                    ? 'bg-blue-50 focus:ring-2 focus:ring-blue-500'
                    : 'bg-gray-50 focus:ring-2 focus:ring-blue-500'
                } rounded-xl`}
                disabled={isSearching}
              />
              {query && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearSearch}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>

            {/* Botón de búsqueda */}
            <Button
              onClick={handleSearch}
              disabled={isSearching || !query.trim()}
              size="lg"
              className={`w-full ${isHomeVariant ? 'h-12 md:h-14' : 'h-12'} bg-blue-600 hover:bg-blue-700 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300`}
            >
              {isSearching ? (
                <>
                  <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                  {searchType === "semantic" ? "Analizando..." : "Buscando..."}
                </>
              ) : (
                <>
                  {searchType === "semantic" ? (
                    <Sparkles className="mr-2 h-5 w-5" />
                  ) : (
                    <Search className="mr-2 h-5 w-5" />
                  )}
                  {searchType === "semantic" ? "Buscar con IA" : "Buscar Propiedades"}
                </>
              )}
            </Button>
          </div>


        </div>
      </div>

      {/* Resultados de búsqueda simplificados */}
      {showResults && (
        <div className="mt-8">
          {/* Resultados como cards limpias */}
          {results.length > 0 ? (
            renderSimplifiedResults(results)
          ) : !isSearching ? (
            // Mensaje cuando no hay resultados
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-gradient-to-r from-gray-100 to-gray-200 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-sm">
                <Search className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">No encontramos propiedades</h3>
              <p className="text-gray-600 mb-4 max-w-sm mx-auto">
                Intenta con términos diferentes o menos específicos.
              </p>
              <Button
                variant="outline"
                onClick={clearSearch}
                className="bg-white hover:bg-gray-50 border-gray-300 text-gray-700 px-4 py-2 rounded-lg"
              >
                Nueva búsqueda
              </Button>
            </div>
          ) : null}
        </div>
      )}
    </div>
  );
}
