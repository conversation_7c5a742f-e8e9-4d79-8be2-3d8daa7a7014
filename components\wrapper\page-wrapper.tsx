"use client"
import { api } from '@/convex/_generated/api';
import { useAuth } from '@clerk/nextjs';
import { useMutation, useQuery } from 'convex/react';
import { useEffect } from 'react';
import Footer from './footer';
import NavBar from './navbar';

export default function PageWrapper({ children }: { children: React.ReactNode }) {
  const { isSignedIn, isLoaded } = useAuth();
  const user = useQuery(api.users.getUser);
  const storeUser = useMutation(api.users.store);

  useEffect(() => {
    // Temporalmente comentado hasta configurar Clerk JWT template
    // if (user && isSignedIn && isLoaded) {
    //   try {
    //     storeUser();
    //   } catch (error) {
    //     console.error("Error storing user:", error);
    //   }
    // }
  }, [user, isSignedIn, isLoaded, storeUser]);

  return (
    <>
      <NavBar />
      <main className="min-w-screen min-h-screen pt-16 bg-gray-50">
        {children}
      </main>
      <Footer />
    </>
  )
}