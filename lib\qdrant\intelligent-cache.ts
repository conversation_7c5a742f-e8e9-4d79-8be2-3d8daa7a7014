/**
 * Intelligent Cache System - Sistema de Caché Inteligente
 * 
 * Cachea análisis de consultas y embeddings (que no cambian) pero mantiene
 * búsquedas de propiedades en tiempo real (que sí cambian).
 * 
 * Día 5 - Optimización de Rendimiento
 */

import { ProfileDetectionResult } from './query-profile-detector';
import { AdaptiveWeightsResult } from './adaptive-weights-engine';
import { MultiComponentEmbeddingResult } from './multi-embedding';
import { DecompositionResult } from './semantic-decomposer';

// Tipos para el sistema de caché
export interface CachedQueryAnalysis {
  query: string;
  normalizedQuery: string;
  profileDetection: ProfileDetectionResult;
  adaptiveWeights: AdaptiveWeightsResult;
  decomposition: DecompositionResult;
  embeddings: MultiComponentEmbeddingResult;
  timestamp: number;
  hitCount: number;
  lastAccessed: number;
}

export interface CacheMetrics {
  totalRequests: number;
  cacheHits: number;
  cacheMisses: number;
  hitRatio: number;
  averageResponseTime: number;
  cacheSize: number;
  oldestEntry: number;
  newestEntry: number;
}

export interface CacheConfig {
  maxSize: number;
  ttlMs: number;
  enableMetrics: boolean;
  enablePredictive: boolean;
  cleanupIntervalMs: number;
}

/**
 * Sistema de caché inteligente con TTL y LRU
 */
export class IntelligentCache {
  private cache: Map<string, CachedQueryAnalysis>;
  private config: CacheConfig;
  private metrics: CacheMetrics;
  private cleanupTimer?: NodeJS.Timeout;

  constructor(config?: Partial<CacheConfig>) {
    this.config = {
      maxSize: parseInt(process.env.ADAPTIVE_CACHE_SIZE || '1000'),
      ttlMs: parseInt(process.env.ADAPTIVE_CACHE_TTL || '1800000'), // 30 minutos
      enableMetrics: process.env.PERFORMANCE_MONITORING === 'true',
      enablePredictive: process.env.PREDICTIVE_CACHE_ENABLED === 'true',
      cleanupIntervalMs: 300000, // 5 minutos
      ...config,
    };

    this.cache = new Map();
    this.metrics = this.initializeMetrics();
    
    // Iniciar limpieza automática
    this.startCleanupTimer();
  }

  /**
   * Obtiene análisis cacheado de una consulta
   */
  async getCachedAnalysis(query: string): Promise<CachedQueryAnalysis | null> {
    const startTime = Date.now();
    const normalizedKey = this.normalizeQueryForCache(query);
    
    try {
      const cached = this.cache.get(normalizedKey);
      
      if (!cached) {
        this.recordCacheMiss(startTime);
        return null;
      }

      // Verificar TTL
      if (this.isExpired(cached)) {
        this.cache.delete(normalizedKey);
        this.recordCacheMiss(startTime);
        return null;
      }

      // Actualizar estadísticas de acceso
      cached.hitCount++;
      cached.lastAccessed = Date.now();
      
      this.recordCacheHit(startTime);
      
      console.log(`🎯 Cache HIT: "${query}" (hits: ${cached.hitCount})`);
      return cached;

    } catch (error) {
      console.error('Error accediendo al caché:', error);
      this.recordCacheMiss(startTime);
      return null;
    }
  }

  /**
   * Cachea el análisis completo de una consulta
   */
  async cacheAnalysis(
    query: string,
    normalizedQuery: string,
    profileDetection: ProfileDetectionResult,
    adaptiveWeights: AdaptiveWeightsResult,
    decomposition: DecompositionResult,
    embeddings: MultiComponentEmbeddingResult
  ): Promise<void> {
    try {
      const normalizedKey = this.normalizeQueryForCache(query);
      const now = Date.now();

      const cacheEntry: CachedQueryAnalysis = {
        query,
        normalizedQuery,
        profileDetection,
        adaptiveWeights,
        decomposition,
        embeddings,
        timestamp: now,
        hitCount: 0,
        lastAccessed: now,
      };

      // Verificar límite de tamaño
      if (this.cache.size >= this.config.maxSize) {
        this.evictOldestEntries();
      }

      this.cache.set(normalizedKey, cacheEntry);
      
      console.log(`💾 Cached analysis: "${query}" (cache size: ${this.cache.size})`);

    } catch (error) {
      console.error('Error cacheando análisis:', error);
    }
  }

  /**
   * Invalida entradas del caché que coincidan con un patrón
   */
  invalidatePattern(pattern: RegExp): number {
    let invalidated = 0;
    
    for (const [key, entry] of this.cache.entries()) {
      if (pattern.test(entry.query) || pattern.test(entry.normalizedQuery)) {
        this.cache.delete(key);
        invalidated++;
      }
    }

    if (invalidated > 0) {
      console.log(`🗑️  Invalidated ${invalidated} cache entries matching pattern`);
    }

    return invalidated;
  }

  /**
   * Limpia todo el caché
   */
  clearCache(): void {
    const size = this.cache.size;
    this.cache.clear();
    this.metrics = this.initializeMetrics();
    console.log(`🗑️  Cleared entire cache (${size} entries)`);
  }

  /**
   * Obtiene métricas del caché
   */
  getMetrics(): CacheMetrics {
    if (!this.config.enableMetrics) {
      return this.initializeMetrics();
    }

    const entries = Array.from(this.cache.values());
    
    return {
      ...this.metrics,
      cacheSize: this.cache.size,
      oldestEntry: entries.length > 0 ? Math.min(...entries.map(e => e.timestamp)) : 0,
      newestEntry: entries.length > 0 ? Math.max(...entries.map(e => e.timestamp)) : 0,
    };
  }

  /**
   * Normaliza consulta para usar como clave de caché
   */
  private normalizeQueryForCache(query: string): string {
    return query
      .toLowerCase()
      .trim()
      .replace(/\s+/g, ' ') // Normalizar espacios
      .replace(/[^\w\s]/g, '') // Remover puntuación
      .substring(0, 200); // Limitar longitud
  }

  /**
   * Verifica si una entrada ha expirado
   */
  private isExpired(entry: CachedQueryAnalysis): boolean {
    return (Date.now() - entry.timestamp) > this.config.ttlMs;
  }

  /**
   * Elimina las entradas más antiguas cuando se alcanza el límite
   */
  private evictOldestEntries(): void {
    const entries = Array.from(this.cache.entries());
    
    // Ordenar por último acceso (LRU)
    entries.sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed);
    
    // Eliminar el 10% más antiguo
    const toEvict = Math.max(1, Math.floor(entries.length * 0.1));
    
    for (let i = 0; i < toEvict; i++) {
      const [key] = entries[i];
      this.cache.delete(key);
    }

    console.log(`🗑️  Evicted ${toEvict} oldest cache entries`);
  }

  /**
   * Limpia entradas expiradas
   */
  private cleanupExpiredEntries(): void {
    let cleaned = 0;
    const now = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if ((now - entry.timestamp) > this.config.ttlMs) {
        this.cache.delete(key);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      console.log(`🧹 Cleaned up ${cleaned} expired cache entries`);
    }
  }

  /**
   * Inicia el timer de limpieza automática
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanupExpiredEntries();
    }, this.config.cleanupIntervalMs);
  }

  /**
   * Detiene el timer de limpieza
   */
  stopCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }
  }

  /**
   * Inicializa métricas
   */
  private initializeMetrics(): CacheMetrics {
    return {
      totalRequests: 0,
      cacheHits: 0,
      cacheMisses: 0,
      hitRatio: 0,
      averageResponseTime: 0,
      cacheSize: 0,
      oldestEntry: 0,
      newestEntry: 0,
    };
  }

  /**
   * Registra un cache hit
   */
  private recordCacheHit(startTime: number): void {
    if (!this.config.enableMetrics) return;

    this.metrics.totalRequests++;
    this.metrics.cacheHits++;
    this.metrics.hitRatio = this.metrics.cacheHits / this.metrics.totalRequests;
    
    const responseTime = Date.now() - startTime;
    this.metrics.averageResponseTime = 
      (this.metrics.averageResponseTime * (this.metrics.totalRequests - 1) + responseTime) / 
      this.metrics.totalRequests;
  }

  /**
   * Registra un cache miss
   */
  private recordCacheMiss(startTime: number): void {
    if (!this.config.enableMetrics) return;

    this.metrics.totalRequests++;
    this.metrics.cacheMisses++;
    this.metrics.hitRatio = this.metrics.cacheHits / this.metrics.totalRequests;
    
    const responseTime = Date.now() - startTime;
    this.metrics.averageResponseTime = 
      (this.metrics.averageResponseTime * (this.metrics.totalRequests - 1) + responseTime) / 
      this.metrics.totalRequests;
  }

  /**
   * Destructor para limpiar recursos
   */
  destroy(): void {
    this.stopCleanupTimer();
    this.clearCache();
  }
}
