/**
 * Weighted Semantic Search Engine
 * 
 * Motor de búsqueda que realiza búsquedas paralelas por componente
 * y combina los resultados usando pesos semánticos.
 */

import { QdrantClient } from '@qdrant/js-client-rest';
import { getQdrantClient } from './config';
import { SEARCH_CONFIG, PropertyPayload } from './config';
import { SemanticWeights } from './unified-config';
import { MultiComponentEmbeddingResult, ComponentEmbedding } from './multi-embedding';
import { getAdaptiveThresholds, getComponentThreshold } from './unified-config';

// Tipos para búsqueda ponderada
export interface ComponentSearchResult {
  component: string;
  results: Array<{
    id: string;
    score: number;
    payload: PropertyPayload;
  }>;
  weight: number;
  processingTime: number;
  resultCount: number;
}

export interface WeightedSearchResult {
  componentResults: ComponentSearchResult[];
  combinedResults: Array<{
    id: string;
    finalScore: number;
    componentScores: Record<string, number>;
    payload: PropertyPayload;
  }>;
  totalProcessingTime: number;
  searchStats: {
    totalResults: number;
    uniqueProperties: number;
    averageScore: number;
    topScore: number;
  };
}

/**
 * Clase para búsqueda semántica ponderada
 */
export class WeightedSemanticSearchEngine {
  private client: QdrantClient;
  private collectionName: string;

  constructor() {
    this.client = getQdrantClient();
    this.collectionName = 'properties';
  }

  /**
   * Realiza búsqueda ponderada usando embeddings multi-componente
   */
  async searchWithWeights(
    embeddings: MultiComponentEmbeddingResult,
    weights: SemanticWeights,
    options: {
      limit?: number;
      scoreThreshold?: number;
      filters?: any;
      locationQuery?: string; // Para aplicar filtros exactos de ubicación
    } = {}
  ): Promise<WeightedSearchResult> {
    const startTime = Date.now();
    const { limit = 20, scoreThreshold = SEARCH_CONFIG.scoreThreshold, filters, locationQuery } = options;

    // Aplicar filtros exactos de ubicación si se detecta una zona específica
    let enhancedFilters = filters;
    // TODO: Implementar filtros exactos de ubicación (método faltante)
    // if (locationQuery && this.isSpecificZoneQuery(locationQuery)) {
    //   enhancedFilters = this.addLocationFilter(filters, locationQuery);
    //   console.log(`🎯 Applied exact location filter for: "${locationQuery}"`);
    // }

    try {
      // Realizar búsquedas paralelas por componente
      const componentSearchPromises = this.createComponentSearchPromises(
        embeddings,
        weights,
        { limit: limit * 2, scoreThreshold: scoreThreshold * 0.5, filters: enhancedFilters } // Threshold más permisivo para componentes
      );

      const componentResults = await Promise.all(componentSearchPromises);

      // Combinar y ponderar resultados
      const combinedResults = this.combineAndWeightResults(componentResults, weights);

      // Aplicar threshold final y limitar resultados
      // Para pesos semánticos, usar threshold ajustado basado en el perfil de pesos
      const adjustedThreshold = this.calculateAdaptiveThreshold(scoreThreshold, weights);
      const filteredResults = combinedResults
        .filter(result => result.finalScore >= adjustedThreshold)
        .slice(0, limit);

      console.log(`🎯 Weighted search completed: ${filteredResults.length} results (threshold: ${adjustedThreshold.toFixed(3)}) in ${Date.now() - startTime}ms`);

      // Calcular estadísticas
      const searchStats = this.calculateSearchStats(filteredResults);

      console.log(`🎯 Weighted search completed: ${filteredResults.length} results in ${Date.now() - startTime}ms`);

      return {
        componentResults,
        combinedResults: filteredResults,
        totalProcessingTime: Date.now() - startTime,
        searchStats,
      };

    } catch (error) {
      console.error('Error in weighted semantic search:', error);
      throw error;
    }
  }

  /**
   * Crea promesas de búsqueda para cada componente
   */
  private createComponentSearchPromises(
    embeddings: MultiComponentEmbeddingResult,
    weights: SemanticWeights,
    options: { limit: number; scoreThreshold: number; filters?: any }
  ): Promise<ComponentSearchResult>[] {
    const components = [
      { name: 'location', embedding: embeddings.location, weight: weights.location },
      { name: 'property', embedding: embeddings.property, weight: weights.property },
      { name: 'amenities', embedding: embeddings.amenities, weight: weights.amenities },
      { name: 'characteristics', embedding: embeddings.characteristics, weight: weights.characteristics },
      { name: 'price', embedding: embeddings.price, weight: weights.price },
    ];

    return components.map(({ name, embedding, weight }) =>
      this.searchByComponent(name, embedding, weight, options)
    );
  }

  /**
   * Realiza búsqueda para un componente específico
   */
  private async searchByComponent(
    componentName: string,
    embedding: ComponentEmbedding,
    weight: number,
    options: { limit: number; scoreThreshold: number; filters?: any }
  ): Promise<ComponentSearchResult> {
    const startTime = Date.now();

    try {
      // Si el embedding está vacío, retornar resultado vacío
      if (embedding.isEmpty || embedding.embedding.length === 0) {
        return {
          component: componentName,
          results: [],
          weight,
          processingTime: Date.now() - startTime,
          resultCount: 0,
        };
      }

      // Realizar búsqueda en Qdrant
      const searchResult = await this.client.search(this.collectionName, {
        vector: embedding.embedding,
        limit: options.limit,
        score_threshold: options.scoreThreshold,
        filter: options.filters,
        with_payload: true,
        with_vector: false,
      });

      const results = searchResult.map(point => ({
        id: point.id as string,
        score: point.score,
        payload: point.payload as unknown as PropertyPayload,
      }));

      console.log(`🔍 Component "${componentName}" found ${results.length} results (weight: ${weight})`);

      return {
        component: componentName,
        results,
        weight,
        processingTime: Date.now() - startTime,
        resultCount: results.length,
      };

    } catch (error) {
      console.error(`Error searching component ${componentName}:`, error);
      return {
        component: componentName,
        results: [],
        weight,
        processingTime: Date.now() - startTime,
        resultCount: 0,
      };
    }
  }

  /**
   * Combina y pondera los resultados de todos los componentes
   */
  private combineAndWeightResults(
    componentResults: ComponentSearchResult[],
    weights: SemanticWeights
  ): Array<{
    id: string;
    finalScore: number;
    componentScores: Record<string, number>;
    payload: PropertyPayload;
  }> {
    // Recopilar todos los IDs únicos de propiedades
    const propertyMap = new Map<string, {
      payload: PropertyPayload;
      componentScores: Record<string, number>;
    }>();

    // Procesar resultados de cada componente
    componentResults.forEach(componentResult => {
      componentResult.results.forEach(result => {
        if (!propertyMap.has(result.id)) {
          propertyMap.set(result.id, {
            payload: result.payload,
            componentScores: {
              location: 0,
              property: 0,
              amenities: 0,
              characteristics: 0,
              price: 0,
            },
          });
        }

        // Asignar score del componente
        const propertyData = propertyMap.get(result.id)!;
        propertyData.componentScores[componentResult.component] = result.score;
      });
    });

    // Calcular scores finales ponderados con normalización adaptativa
    const combinedResults = Array.from(propertyMap.entries()).map(([id, data]) => {
      // Identificar componentes activos (que tienen embeddings generados)
      const activeComponents = componentResults.filter(cr => cr.results.length > 0);
      const activeComponentNames = activeComponents.map(cr => cr.component);

      // Calcular peso total de componentes activos
      let totalActiveWeight = 0;
      let weightedScore = 0;

      // Solo considerar componentes que están activos
      if (activeComponentNames.includes('location')) {
        weightedScore += data.componentScores.location * weights.location;
        totalActiveWeight += weights.location;
      }
      if (activeComponentNames.includes('property')) {
        weightedScore += data.componentScores.property * weights.property;
        totalActiveWeight += weights.property;
      }
      if (activeComponentNames.includes('amenities')) {
        weightedScore += data.componentScores.amenities * weights.amenities;
        totalActiveWeight += weights.amenities;
      }
      if (activeComponentNames.includes('characteristics')) {
        weightedScore += data.componentScores.characteristics * (weights.characteristics || 0);
        totalActiveWeight += (weights.characteristics || 0);
      }
      if (activeComponentNames.includes('price')) {
        weightedScore += data.componentScores.price * weights.price;
        totalActiveWeight += weights.price;
      }

      // Normalizar el score basado en los pesos activos
      // Esto evita que propiedades sean penalizadas por componentes vacíos
      const finalScore = totalActiveWeight > 0 ? weightedScore / totalActiveWeight : 0;

      // Log detallado para debugging
      const locationName = data.payload.level3 || data.payload.level4 || 'Unknown';
      const propertyType = data.payload.type || 'Unknown';
      console.log(`🔍 ${locationName} (${propertyType}): location=${data.componentScores.location.toFixed(3)} (×${weights.location}) + property=${data.componentScores.property.toFixed(3)} (×${weights.property}) + amenities=${data.componentScores.amenities.toFixed(3)} (×${weights.amenities}) = ${weightedScore.toFixed(3)} / ${totalActiveWeight.toFixed(3)} = ${finalScore.toFixed(3)}`);

      return {
        id,
        finalScore,
        componentScores: data.componentScores,
        payload: data.payload,
      };
    });

    // Ordenar por score final descendente
    return combinedResults.sort((a, b) => b.finalScore - a.finalScore);
  }

  /**
   * Calcula estadísticas de la búsqueda
   */
  private calculateSearchStats(results: Array<{
    finalScore: number;
    componentScores: Record<string, number>;
  }>): {
    totalResults: number;
    uniqueProperties: number;
    averageScore: number;
    topScore: number;
  } {
    if (results.length === 0) {
      return {
        totalResults: 0,
        uniqueProperties: 0,
        averageScore: 0,
        topScore: 0,
      };
    }

    const scores = results.map(r => r.finalScore);
    const averageScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    const topScore = Math.max(...scores);

    return {
      totalResults: results.length,
      uniqueProperties: results.length, // Ya son únicos por construcción
      averageScore,
      topScore,
    };
  }

  /**
   * Obtiene breakdown detallado de scores por componente
   */
  getScoreBreakdown(result: {
    finalScore: number;
    componentScores: Record<string, number>;
  }, weights: SemanticWeights): {
    finalScore: number;
    breakdown: Array<{
      component: string;
      score: number;
      weight: number;
      contribution: number;
    }>;
  } {
    const breakdown = [
      {
        component: 'location',
        score: result.componentScores.location,
        weight: weights.location,
        contribution: result.componentScores.location * weights.location,
      },
      {
        component: 'property',
        score: result.componentScores.property,
        weight: weights.property,
        contribution: result.componentScores.property * weights.property,
      },
      {
        component: 'amenities',
        score: result.componentScores.amenities,
        weight: weights.amenities,
        contribution: result.componentScores.amenities * weights.amenities,
      },
      {
        component: 'price',
        score: result.componentScores.price,
        weight: weights.price,
        contribution: result.componentScores.price * weights.price,
      },
    ];

    return {
      finalScore: result.finalScore,
      breakdown,
    };
  }

  /**
   * Calcula threshold adaptativo basado en el perfil de pesos y configuración centralizada
   */
  private calculateAdaptiveThreshold(baseThreshold: number, weights: SemanticWeights): number {
    // Para búsquedas con pesos semánticos, usar un threshold más permisivo
    // que permita encontrar múltiples resultados relevantes

    // Determinar el componente dominante
    const dominantComponent = Object.entries(weights).reduce((max, [component, weight]) =>
      weight > max.weight ? { component: component as keyof SemanticWeights, weight } : max,
      { component: 'location' as keyof SemanticWeights, weight: 0 }
    );

    // Para búsquedas semánticas, usar threshold más permisivo para encontrar más resultados
    // Especialmente importante para consultas como "apartamento zona 14" que deberían
    // devolver múltiples opciones
    let adjustedThreshold = baseThreshold;

    // Si el componente dominante tiene alta prioridad (>0.5), ser más permisivo
    if (dominantComponent.weight > 0.5) {
      adjustedThreshold = baseThreshold * 0.6; // Más permisivo para encontrar más resultados
    } else {
      adjustedThreshold = baseThreshold * 0.7; // Ligeramente más permisivo
    }

    // Asegurar un mínimo razonable pero no demasiado bajo
    const minThreshold = 0.08; // Mínimo absoluto para mantener calidad

    console.log(`🎯 Adaptive threshold: base=${baseThreshold}, dominant=${dominantComponent.component}(${dominantComponent.weight}), adjusted=${adjustedThreshold.toFixed(3)}`);

    return Math.max(adjustedThreshold, minThreshold);
  }
}
