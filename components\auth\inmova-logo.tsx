"use client";

interface InmovaLogoProps {
  className?: string;
  size?: "sm" | "md" | "lg" | "xl";
  showText?: boolean;
}

export default function InmovaLogo({
  className = "",
  size = "md",
  showText = false
}: InmovaLogoProps) {

  const sizeClasses = {
    sm: "w-8 h-8",
    md: "w-12 h-12",
    lg: "w-16 h-16",
    xl: "w-20 h-20"
  };

  const textSizes = {
    sm: "text-sm",
    md: "text-xl",
    lg: "text-2xl",
    xl: "text-3xl"
  };

  const dotSizes = {
    sm: "w-2 h-2",
    md: "w-4 h-4",
    lg: "w-5 h-5",
    xl: "w-6 h-6"
  };

  return (
    <div className={`flex items-center justify-center gap-3 ${className}`}>
      <div className="relative">
        {/* Logo principal de Inmova */}
        <div className={`${sizeClasses[size]} bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105`}>
          <span className={`text-white font-bold ${textSizes[size]}`}>I</span>
        </div>

        {/* Punto decorativo */}
        <div className={`absolute -top-1 -right-1 ${dotSizes[size]} bg-gradient-to-br from-orange-400 to-orange-500 rounded-full shadow-md`}></div>
      </div>

      {/* Texto opcional */}
      {showText && (
        <div className="flex flex-col">
          <span className="text-2xl font-bold text-gray-900">Inmova</span>
          <span className="text-sm text-gray-500">Tu hogar ideal</span>
        </div>
      )}
    </div>
  );
}
