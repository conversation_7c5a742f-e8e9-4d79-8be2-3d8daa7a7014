"use client";

import React, { useState, useMemo } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Calendar } from "@/components/ui/calendar";
import {
  Calendar as CalendarIcon,
  ChevronLeft,
  ChevronRight,
  Clock,
  User,
  Home,
  MapPin,
  Video,
  Phone
} from 'lucide-react';
import { format, isSameDay, startOfMonth, endOfMonth, eachDayOfInterval, isToday } from 'date-fns';
import { es } from 'date-fns/locale';
import { formatTimeFor12h, formatDateLocal, formatTimeRange } from '@/lib/time-utils';

interface CalendarViewProps {
  appointments: any[];
  onDateSelect: (date: Date) => void;
  selectedDate: Date;
}

export function CalendarView({ appointments, onDateSelect, selectedDate }: CalendarViewProps) {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewMode, setViewMode] = useState<'month' | 'week'>('month');

  // Funciones auxiliares
  const getTypeText = (type: string) => {
    switch (type) {
      case 'property_viewing': return 'Visita de Propiedad';
      case 'consultation': return 'Consulta';
      case 'negotiation': return 'Negociación';
      case 'document_signing': return 'Firma de Documentos';
      case 'other': return 'Otro';
      default: return type;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'property_viewing': return <Home className="w-3 h-3" />;
      case 'consultation': return <User className="w-3 h-3" />;
      case 'negotiation': return <User className="w-3 h-3" />;
      case 'document_signing': return <User className="w-3 h-3" />;
      default: return <CalendarIcon className="w-3 h-3" />;
    }
  };

  const getMeetingTypeIcon = (meetingType: string) => {
    switch (meetingType) {
      case 'video_call': return <Video className="w-3 h-3 text-blue-600" />;
      case 'phone_call': return <Phone className="w-3 h-3 text-green-600" />;
      case 'in_person': return <MapPin className="w-3 h-3 text-purple-600" />;
      default: return <CalendarIcon className="w-3 h-3" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-green-100 text-green-800 border-green-200';
      case 'scheduled': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'completed': return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'cancelled': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    }
  };

  // Obtener citas para una fecha específica
  const getAppointmentsForDate = (date: Date) => {
    return appointments.filter(appointment =>
      isSameDay(new Date(appointment.startTime), date)
    );
  };

  // Obtener fechas con citas para el mes actual
  const datesWithAppointments = useMemo(() => {
    const start = startOfMonth(currentDate);
    const end = endOfMonth(currentDate);
    const daysInMonth = eachDayOfInterval({ start, end });

    return daysInMonth.filter(date =>
      getAppointmentsForDate(date).length > 0
    );
  }, [currentDate, appointments]);

  // Citas para la fecha seleccionada
  const selectedDateAppointments = getAppointmentsForDate(selectedDate);

  return (
    <div className="space-y-6">
      {/* Header con controles */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div className="flex items-center gap-3">
              <CalendarIcon className="w-6 h-6 text-blue-600" />
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  {format(currentDate, 'MMMM yyyy', { locale: es })}
                </h2>
                <p className="text-sm text-gray-600">
                  {isToday(selectedDate) ? 'Hoy' : formatDateLocal(selectedDate.toISOString())} - {selectedDateAppointments.length} cita{selectedDateAppointments.length !== 1 ? 's' : ''}
                </p>
              </div>
            </div>

            {/* Controles de vista */}
            <div className="flex items-center gap-2">
              <div className="flex items-center bg-gray-100 rounded-lg p-1">
                <Button
                  variant={viewMode === 'month' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('month')}
                  className="h-8 px-3"
                >
                  Mes
                </Button>
                <Button
                  variant={viewMode === 'week' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('week')}
                  className="h-8 px-3"
                >
                  Semana
                </Button>
              </div>

              {/* Navegación de mes */}
              <div className="flex items-center gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1))}
                  className="h-8 w-8 p-0"
                >
                  <ChevronLeft className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentDate(new Date())}
                  className="h-8 px-3 text-xs"
                >
                  Hoy
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1))}
                  className="h-8 w-8 p-0"
                >
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Contenido principal */}
      <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
        {/* Calendario compacto */}
        <Card className="xl:col-span-3">
          <CardContent className="p-4">
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={(date) => date && onDateSelect(date)}
              month={currentDate}
              onMonthChange={setCurrentDate}
              locale={es}
              modifiers={{
                hasAppointments: datesWithAppointments,
                today: [new Date()]
              }}
              modifiersStyles={{
                hasAppointments: {
                  backgroundColor: '#dbeafe',
                  color: '#1e40af',
                  fontWeight: 'bold',
                  borderRadius: '6px'
                }
              }}
              className="w-full"
              classNames={{
                months: "flex w-full",
                month: "w-full",
                caption: "flex justify-center pt-1 relative items-center mb-4",
                caption_label: "text-lg font-semibold",
                table: "w-full border-collapse",
                head_row: "flex w-full",
                head_cell: "text-gray-500 rounded-md w-full font-medium text-sm p-2",
                row: "flex w-full mt-1",
                cell: "h-12 w-full text-center text-sm p-0 relative hover:bg-gray-50 rounded-md",
                day: "h-12 w-full p-0 font-normal hover:bg-gray-100 rounded-md transition-colors",
                day_selected: "bg-blue-600 text-white hover:bg-blue-700",
                day_today: "bg-blue-50 text-blue-600 font-semibold",
                day_outside: "text-gray-400 opacity-50",
                day_disabled: "text-gray-300 opacity-30",
              }}
            />

            {/* Leyenda compacta */}
            <div className="mt-4 pt-4 border-t flex items-center justify-center gap-6 text-sm text-gray-600">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-blue-100 border border-blue-200 rounded"></div>
                <span>Con citas</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-blue-600 rounded"></div>
                <span>Seleccionado</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-blue-50 border border-blue-200 rounded"></div>
                <span>Hoy</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Panel lateral de citas */}
        <Card className="xl:col-span-1">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Clock className="w-5 h-5" />
              {isToday(selectedDate) ? 'Hoy' : format(selectedDate, 'dd MMM', { locale: es })}
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            {selectedDateAppointments.length === 0 ? (
              <div className="text-center py-6">
                <CalendarIcon className="w-8 h-8 text-gray-400 mx-auto mb-3" />
                <p className="text-gray-500 text-sm">
                  Sin citas programadas
                </p>
              </div>
            ) : (
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {selectedDateAppointments
                  .sort((a, b) => new Date(a.startTime).getTime() - new Date(b.startTime).getTime())
                  .map((appointment: any) => (
                    <div
                      key={appointment._id}
                      className="p-3 bg-gray-50 rounded-lg border hover:shadow-sm transition-all hover:bg-gray-100"
                    >
                      {/* Header compacto */}
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2 min-w-0 flex-1">
                          {getTypeIcon(appointment.type)}
                          <h4 className="font-medium text-sm text-gray-900 truncate">
                            {getTypeText(appointment.type)}
                          </h4>
                        </div>
                        <Badge className={`${getStatusColor(appointment.status)} text-xs px-2 py-1 flex-shrink-0`}>
                          {appointment.status === 'confirmed' ? 'OK' :
                           appointment.status === 'scheduled' ? 'Prog' :
                           appointment.status === 'completed' ? 'Done' :
                           appointment.status === 'cancelled' ? 'X' :
                           appointment.status}
                        </Badge>
                      </div>

                      {/* Detalles compactos */}
                      <div className="space-y-1 text-xs text-gray-600">
                        <div className="flex items-center gap-2">
                          <Clock className="w-3 h-3 flex-shrink-0" />
                          <span className="font-medium">
                            {formatTimeRange(appointment.startTime, appointment.endTime)}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <User className="w-3 h-3 flex-shrink-0" />
                          <span className="truncate">{appointment.guestName}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          {getMeetingTypeIcon(appointment.meetingType)}
                          <span>
                            {appointment.meetingType === 'video_call' && 'Video'}
                            {appointment.meetingType === 'phone_call' && 'Llamada'}
                            {appointment.meetingType === 'in_person' && 'Presencial'}
                          </span>
                        </div>
                        {appointment.property && (
                          <div className="flex items-center gap-2">
                            <Home className="w-3 h-3 flex-shrink-0" />
                            <span className="truncate text-blue-600">{appointment.property.title}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}