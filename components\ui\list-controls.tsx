"use client";

import React from 'react';
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ExpandIcon, ShrinkIcon, Eye, EyeOff } from 'lucide-react';

interface ListControlsProps {
  totalCount: number;
  expandedCount: number;
  onExpandAll: () => void;
  onCollapseAll: () => void;
  className?: string;
  variant?: 'default' | 'compact';
}

export function ListControls({
  totalCount,
  expandedCount,
  onExpandAll,
  onCollapseAll,
  className = "",
  variant = 'default'
}: ListControlsProps) {
  if (totalCount === 0) return null;

  const allExpanded = expandedCount === totalCount;
  const allCollapsed = expandedCount === 0;

  if (variant === 'compact') {
    return (
      <div className={`flex items-center justify-between p-2 bg-gray-50 rounded-lg text-sm ${className}`}>
        <span className="text-gray-600">
          {expandedCount} de {totalCount} expandidos
        </span>
        <div className="flex gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={onExpandAll}
            disabled={allExpanded}
            className="h-7 px-2 text-xs"
          >
            <Eye className="h-3 w-3 mr-1" />
            Todos
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={onCollapseAll}
            disabled={allCollapsed}
            className="h-7 px-2 text-xs"
          >
            <EyeOff className="h-3 w-3 mr-1" />
            Ninguno
          </Button>
        </div>
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <span className="text-sm text-gray-600">
              <span className="font-medium">{expandedCount}</span> de{" "}
              <span className="font-medium">{totalCount}</span> elementos expandidos
            </span>
            {expandedCount > 0 && expandedCount < totalCount && (
              <span className="text-xs text-amber-600 bg-amber-50 px-2 py-1 rounded">
                Vista mixta
              </span>
            )}
          </div>
          
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onExpandAll}
              disabled={allExpanded}
              className="flex items-center gap-2"
            >
              <ExpandIcon className="h-4 w-4" />
              Expandir todos
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={onCollapseAll}
              disabled={allCollapsed}
              className="flex items-center gap-2"
            >
              <ShrinkIcon className="h-4 w-4" />
              Contraer todos
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Hook para manejar controles de lista
export function useListControls(itemIds: string[], defaultExpanded = false) {
  const [expandedItems, setExpandedItems] = React.useState<Set<string>>(() =>
    new Set(defaultExpanded ? itemIds : [])
  );

  // Memoizar los itemIds para evitar re-renders innecesarios
  const memoizedItemIds = React.useMemo(() => itemIds, [itemIds.join(',')]);

  const expandedCount = expandedItems.size;
  const totalCount = memoizedItemIds.length;

  const toggleItem = React.useCallback((itemId: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(itemId)) {
        newSet.delete(itemId);
      } else {
        newSet.add(itemId);
      }
      return newSet;
    });
  }, []);

  const expandAll = React.useCallback(() => {
    setExpandedItems(new Set(memoizedItemIds));
  }, [memoizedItemIds]);

  const collapseAll = React.useCallback(() => {
    setExpandedItems(new Set());
  }, []);

  const isExpanded = React.useCallback((itemId: string) => expandedItems.has(itemId), [expandedItems]);

  // Actualizar cuando cambian los IDs - solo si realmente cambiaron
  React.useEffect(() => {
    setExpandedItems(prev => {
      const currentIds = new Set(memoizedItemIds);
      const newSet = new Set<string>();

      // Mantener elementos expandidos que aún existen
      memoizedItemIds.forEach(id => {
        if (prev.has(id)) {
          newSet.add(id);
        } else if (defaultExpanded) {
          newSet.add(id);
        }
      });

      // Solo actualizar si hay cambios
      const hasChanges = newSet.size !== prev.size ||
        Array.from(newSet).some(id => !prev.has(id)) ||
        Array.from(prev).some(id => !currentIds.has(id));

      return hasChanges ? newSet : prev;
    });
  }, [memoizedItemIds, defaultExpanded]);

  return {
    expandedCount,
    totalCount,
    toggleItem,
    expandAll,
    collapseAll,
    isExpanded,
    expandedItems: Array.from(expandedItems)
  };
}

// Componente wrapper que combina controles y lista
interface ManagedListProps {
  items: any[];
  renderItem: (item: any, isExpanded: boolean, onToggle: () => void) => React.ReactNode;
  getItemId: (item: any) => string;
  title?: string;
  icon?: React.ComponentType<{ className?: string }>;
  defaultExpanded?: boolean;
  showControls?: boolean;
  controlsVariant?: 'default' | 'compact';
  className?: string;
}

export function ManagedList({
  items,
  renderItem,
  getItemId,
  title,
  icon: Icon,
  defaultExpanded = false,
  showControls = true,
  controlsVariant = 'compact',
  className = ""
}: ManagedListProps) {
  // Memoizar itemIds para evitar re-renders
  const itemIds = React.useMemo(() => items.map(getItemId), [items, getItemId]);

  const {
    expandedCount,
    totalCount,
    toggleItem,
    expandAll,
    collapseAll,
    isExpanded
  } = useListControls(itemIds, defaultExpanded);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header con título */}
      {title && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              {Icon && <Icon className="w-5 h-5" />}
              <h3 className="font-semibold">{title} ({totalCount})</h3>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Controles */}
      {showControls && totalCount > 1 && (
        <ListControls
          totalCount={totalCount}
          expandedCount={expandedCount}
          onExpandAll={expandAll}
          onCollapseAll={collapseAll}
          variant={controlsVariant}
        />
      )}

      {/* Lista de elementos */}
      <div className="space-y-3">
        {items.map((item: any) => {
          const itemId = getItemId(item);
          return renderItem(
            item,
            isExpanded(itemId),
            () => toggleItem(itemId)
          );
        })}
      </div>
    </div>
  );
}
