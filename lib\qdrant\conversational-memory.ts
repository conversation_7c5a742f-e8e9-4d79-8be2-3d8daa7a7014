/**
 * Conversational Memory - Memoria Conversacional
 * 
 * Sistema que mantiene contexto entre consultas relacionadas,
 * permitiendo refinamiento progresivo y aprendizaje de preferencias.
 * 
 * Día 6 - Análisis de Contexto Avanzado
 */

import { ContextAnalysis } from './context-analyzer';
import { DisambiguationResult } from './query-disambiguator';

// Tipos para memoria conversacional
export interface ConversationEntry {
  id: string;
  timestamp: number;
  query: string;
  contextAnalysis?: ContextAnalysis;
  disambiguationResult?: DisambiguationResult;
  resultsCount: number;
  userFeedback?: 'positive' | 'negative' | 'neutral';
  refinements: string[];
}

export interface UserSession {
  sessionId: string;
  userId?: string;
  startTime: number;
  lastActivity: number;
  entries: ConversationEntry[];
  learnedPreferences: LearnedPreferences;
  conversationFlow: ConversationFlow;
}

export interface LearnedPreferences {
  preferredZones: { zone: string; confidence: number }[];
  preferredPropertyTypes: { type: string; confidence: number }[];
  preferredPriceRange: { min: number; max: number; confidence: number };
  preferredAmenities: { amenity: string; confidence: number }[];
  avoidedZones: { zone: string; confidence: number }[];
  budgetLevel: 'low' | 'medium' | 'high' | 'luxury';
  familyOriented: boolean;
  modernPreference: boolean;
  quietPreference: boolean;
}

export interface ConversationFlow {
  currentIntent: 'exploring' | 'narrowing' | 'comparing' | 'deciding';
  progressStage: 'initial' | 'refining' | 'finalizing';
  lastRefinementType: 'location' | 'price' | 'amenities' | 'type' | 'none';
  suggestedNextSteps: string[];
}

export interface ContextualQuery {
  originalQuery: string;
  enrichedQuery: string;
  appliedContext: {
    fromHistory: boolean;
    fromPreferences: boolean;
    fromFlow: boolean;
  };
  confidence: number;
}

/**
 * Sistema de memoria conversacional para búsquedas inmobiliarias
 */
export class ConversationalMemory {
  private sessions: Map<string, UserSession>;
  private maxSessionAge: number;
  private maxEntriesPerSession: number;
  private cleanupTimer?: NodeJS.Timeout;

  constructor() {
    this.sessions = new Map();
    this.maxSessionAge = 24 * 60 * 60 * 1000; // 24 horas
    this.maxEntriesPerSession = 50;
    
    // Iniciar limpieza automática
    this.startCleanupTimer();
  }

  /**
   * Obtiene o crea una sesión de usuario
   */
  getOrCreateSession(sessionId: string, userId?: string): UserSession {
    let session = this.sessions.get(sessionId);
    
    if (!session) {
      session = this.createNewSession(sessionId, userId);
      this.sessions.set(sessionId, session);
      console.log(`💭 Nueva sesión creada: ${sessionId}`);
    } else {
      session.lastActivity = Date.now();
    }
    
    return session;
  }

  /**
   * Enriquece una consulta con contexto conversacional
   */
  enrichQueryWithContext(
    sessionId: string,
    query: string,
    contextAnalysis?: ContextAnalysis,
    disambiguationResult?: DisambiguationResult
  ): ContextualQuery {
    const session = this.getOrCreateSession(sessionId);
    
    try {
      console.log(`💭 Enriqueciendo consulta con contexto: "${query}"`);

      // Analizar consulta en contexto de la conversación
      const enrichedQuery = this.buildEnrichedQuery(session, query, contextAnalysis);
      
      // Determinar qué contexto se aplicó
      const appliedContext = this.determineAppliedContext(session, query, enrichedQuery);
      
      // Calcular confianza del enriquecimiento
      const confidence = this.calculateEnrichmentConfidence(session, appliedContext);

      // Registrar entrada en la conversación
      this.addConversationEntry(session, query, contextAnalysis, disambiguationResult, 0);

      // Actualizar flujo conversacional
      this.updateConversationFlow(session, query);

      const result: ContextualQuery = {
        originalQuery: query,
        enrichedQuery,
        appliedContext,
        confidence,
      };

      console.log(`✅ Consulta enriquecida: "${enrichedQuery}" (confianza: ${confidence.toFixed(2)})`);
      return result;

    } catch (error) {
      console.error('Error enriqueciendo consulta:', error);
      return {
        originalQuery: query,
        enrichedQuery: query,
        appliedContext: { fromHistory: false, fromPreferences: false, fromFlow: false },
        confidence: 0.1,
      };
    }
  }

  /**
   * Registra feedback del usuario sobre los resultados
   */
  recordUserFeedback(
    sessionId: string,
    entryId: string,
    feedback: 'positive' | 'negative' | 'neutral',
    resultsCount: number
  ): void {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    const entry = session.entries.find(e => e.id === entryId);
    if (!entry) return;

    entry.userFeedback = feedback;
    entry.resultsCount = resultsCount;

    // Actualizar preferencias aprendidas basado en feedback
    this.updateLearnedPreferences(session, entry, feedback);

    console.log(`📝 Feedback registrado: ${feedback} para consulta "${entry.query}"`);
  }

  /**
   * Sugiere refinamientos basados en el historial
   */
  suggestRefinements(sessionId: string): string[] {
    const session = this.sessions.get(sessionId);
    if (!session || session.entries.length === 0) {
      return this.getDefaultRefinements();
    }

    const suggestions: string[] = [];
    const lastEntry = session.entries[session.entries.length - 1];
    const preferences = session.learnedPreferences;

    // Sugerencias basadas en preferencias aprendidas
    if (preferences.preferredZones.length > 0) {
      const topZone = preferences.preferredZones[0];
      if (topZone.confidence > 0.6) {
        suggestions.push(`Buscar en ${topZone.zone}`);
      }
    }

    // Sugerencias basadas en flujo conversacional
    switch (session.conversationFlow.currentIntent) {
      case 'exploring':
        suggestions.push('Especificar ubicación preferida');
        suggestions.push('Definir rango de precio');
        break;
      case 'narrowing':
        suggestions.push('Agregar amenidades específicas');
        suggestions.push('Ajustar número de habitaciones');
        break;
      case 'comparing':
        suggestions.push('Comparar por precio');
        suggestions.push('Comparar por ubicación');
        break;
    }

    // Sugerencias basadas en consultas anteriores
    if (lastEntry.resultsCount === 0) {
      suggestions.push('Ampliar criterios de búsqueda');
      suggestions.push('Probar ubicaciones alternativas');
    } else if (lastEntry.resultsCount > 20) {
      suggestions.push('Refinar criterios para menos resultados');
      suggestions.push('Especificar amenidades importantes');
    }

    return suggestions.slice(0, 5); // Máximo 5 sugerencias
  }

  /**
   * Obtiene estadísticas de la sesión
   */
  getSessionStats(sessionId: string): {
    totalQueries: number;
    avgResultsPerQuery: number;
    mostSearchedZones: string[];
    preferredPropertyTypes: string[];
    sessionDuration: number;
  } | null {
    const session = this.sessions.get(sessionId);
    if (!session) return null;

    const totalQueries = session.entries.length;
    const avgResults = totalQueries > 0 
      ? session.entries.reduce((sum, e) => sum + e.resultsCount, 0) / totalQueries 
      : 0;

    const zones = session.learnedPreferences.preferredZones
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, 3)
      .map(z => z.zone);

    const types = session.learnedPreferences.preferredPropertyTypes
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, 3)
      .map(t => t.type);

    return {
      totalQueries,
      avgResultsPerQuery: Math.round(avgResults),
      mostSearchedZones: zones,
      preferredPropertyTypes: types,
      sessionDuration: Date.now() - session.startTime,
    };
  }

  /**
   * Crea nueva sesión
   */
  private createNewSession(sessionId: string, userId?: string): UserSession {
    return {
      sessionId,
      userId,
      startTime: Date.now(),
      lastActivity: Date.now(),
      entries: [],
      learnedPreferences: this.createDefaultPreferences(),
      conversationFlow: {
        currentIntent: 'exploring',
        progressStage: 'initial',
        lastRefinementType: 'none',
        suggestedNextSteps: [],
      },
    };
  }

  /**
   * Construye consulta enriquecida con contexto
   */
  private buildEnrichedQuery(
    session: UserSession,
    query: string,
    contextAnalysis?: ContextAnalysis
  ): string {
    let enriched = query;
    const preferences = session.learnedPreferences;

    // Enriquecer con preferencias de ubicación
    if (preferences.preferredZones.length > 0 && !this.hasLocationInQuery(query)) {
      const topZone = preferences.preferredZones[0];
      if (topZone.confidence > 0.7) {
        enriched += ` en ${topZone.zone}`;
      }
    }

    // Enriquecer con tipo de propiedad preferido
    if (preferences.preferredPropertyTypes.length > 0 && !this.hasPropertyTypeInQuery(query)) {
      const topType = preferences.preferredPropertyTypes[0];
      if (topType.confidence > 0.6) {
        enriched = `${topType.type} ${enriched}`;
      }
    }

    // Enriquecer con amenidades preferidas
    if (preferences.preferredAmenities.length > 0) {
      const topAmenities = preferences.preferredAmenities
        .filter(a => a.confidence > 0.5)
        .slice(0, 2);
      
      if (topAmenities.length > 0 && !this.hasAmenitiesInQuery(query)) {
        const amenitiesText = topAmenities.map(a => a.amenity).join(' y ');
        enriched += ` con ${amenitiesText}`;
      }
    }

    return enriched;
  }

  /**
   * Determina qué contexto se aplicó
   */
  private determineAppliedContext(
    session: UserSession,
    originalQuery: string,
    enrichedQuery: string
  ): { fromHistory: boolean; fromPreferences: boolean; fromFlow: boolean } {
    const hasHistory = session.entries.length > 0;
    const hasPreferences = session.learnedPreferences.preferredZones.length > 0 ||
                          session.learnedPreferences.preferredPropertyTypes.length > 0;
    const wasEnriched = originalQuery !== enrichedQuery;

    return {
      fromHistory: hasHistory && wasEnriched,
      fromPreferences: hasPreferences && wasEnriched,
      fromFlow: session.conversationFlow.currentIntent !== 'exploring',
    };
  }

  /**
   * Calcula confianza del enriquecimiento
   */
  private calculateEnrichmentConfidence(
    session: UserSession,
    appliedContext: { fromHistory: boolean; fromPreferences: boolean; fromFlow: boolean }
  ): number {
    let confidence = 0.5; // Base

    if (appliedContext.fromHistory) confidence += 0.2;
    if (appliedContext.fromPreferences) confidence += 0.2;
    if (appliedContext.fromFlow) confidence += 0.1;

    // Boost por número de entradas (más historial = más confianza)
    const historyBoost = Math.min(0.2, session.entries.length * 0.02);
    confidence += historyBoost;

    return Math.min(1.0, confidence);
  }

  /**
   * Agrega entrada a la conversación
   */
  private addConversationEntry(
    session: UserSession,
    query: string,
    contextAnalysis?: ContextAnalysis,
    disambiguationResult?: DisambiguationResult,
    resultsCount: number = 0
  ): void {
    const entry: ConversationEntry = {
      id: `entry_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      query,
      contextAnalysis,
      disambiguationResult,
      resultsCount,
      refinements: [],
    };

    session.entries.push(entry);

    // Mantener límite de entradas
    if (session.entries.length > this.maxEntriesPerSession) {
      session.entries = session.entries.slice(-this.maxEntriesPerSession);
    }
  }

  /**
   * Actualiza flujo conversacional
   */
  private updateConversationFlow(session: UserSession, query: string): void {
    const flow = session.conversationFlow;
    const queryLower = query.toLowerCase();

    // Detectar intención actual
    if (queryLower.includes('comparar') || queryLower.includes('vs') || queryLower.includes('diferencia')) {
      flow.currentIntent = 'comparing';
    } else if (session.entries.length > 2) {
      flow.currentIntent = 'narrowing';
    } else {
      flow.currentIntent = 'exploring';
    }

    // Actualizar etapa de progreso
    if (session.entries.length === 0) {
      flow.progressStage = 'initial';
    } else if (session.entries.length < 5) {
      flow.progressStage = 'refining';
    } else {
      flow.progressStage = 'finalizing';
    }
  }

  /**
   * Actualiza preferencias aprendidas
   */
  private updateLearnedPreferences(
    session: UserSession,
    entry: ConversationEntry,
    feedback: 'positive' | 'negative' | 'neutral'
  ): void {
    if (feedback === 'neutral') return;

    const preferences = session.learnedPreferences;
    const boost = feedback === 'positive' ? 0.1 : -0.1;

    // Actualizar preferencias basado en contexto de la consulta
    if (entry.contextAnalysis) {
      const context = entry.contextAnalysis;

      // Actualizar zonas preferidas
      context.locationContext.preferredZones.forEach(zone => {
        this.updatePreferenceItem(preferences.preferredZones, zone, boost);
      });

      // Actualizar preferencias familiares
      if (context.familyContext.isFamilyOriented) {
        preferences.familyOriented = feedback === 'positive';
      }
    }
  }

  /**
   * Actualiza item de preferencia
   */
  private updatePreferenceItem(
    items: { zone?: string; type?: string; amenity?: string; confidence: number }[],
    value: string,
    boost: number
  ): void {
    const existing = items.find(item => 
      (item as any).zone === value || 
      (item as any).type === value || 
      (item as any).amenity === value
    );

    if (existing) {
      existing.confidence = Math.max(0, Math.min(1, existing.confidence + boost));
    } else if (boost > 0) {
      (items as any).push({ 
        [items === this.sessions.get('')?.learnedPreferences.preferredZones ? 'zone' : 
         items === this.sessions.get('')?.learnedPreferences.preferredPropertyTypes ? 'type' : 'amenity']: value, 
        confidence: 0.1 + boost 
      });
    }
  }

  /**
   * Verifica si la consulta tiene ubicación
   */
  private hasLocationInQuery(query: string): boolean {
    const locationTerms = ['zona', 'z', 'carretera', 'antigua', 'mixco', 'villa'];
    return locationTerms.some(term => query.toLowerCase().includes(term));
  }

  /**
   * Verifica si la consulta tiene tipo de propiedad
   */
  private hasPropertyTypeInQuery(query: string): boolean {
    const typeTerms = ['apartamento', 'casa', 'oficina', 'local', 'terreno', 'apto', 'depto'];
    return typeTerms.some(term => query.toLowerCase().includes(term));
  }

  /**
   * Verifica si la consulta tiene amenidades
   */
  private hasAmenitiesInQuery(query: string): boolean {
    const amenityTerms = ['piscina', 'gimnasio', 'parqueo', 'terraza', 'jardín', 'seguridad'];
    return amenityTerms.some(term => query.toLowerCase().includes(term));
  }

  /**
   * Crea preferencias por defecto
   */
  private createDefaultPreferences(): LearnedPreferences {
    return {
      preferredZones: [],
      preferredPropertyTypes: [],
      preferredPriceRange: { min: 200000, max: 1000000, confidence: 0.1 },
      preferredAmenities: [],
      avoidedZones: [],
      budgetLevel: 'medium',
      familyOriented: false,
      modernPreference: false,
      quietPreference: false,
    };
  }

  /**
   * Obtiene refinamientos por defecto
   */
  private getDefaultRefinements(): string[] {
    return [
      'Especificar ubicación (ej: Zona 14)',
      'Definir tipo de propiedad (apartamento/casa)',
      'Establecer rango de precio',
      'Agregar amenidades deseadas',
      'Especificar número de habitaciones',
    ];
  }

  /**
   * Inicia timer de limpieza
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanupExpiredSessions();
    }, 60 * 60 * 1000); // Cada hora
  }

  /**
   * Limpia sesiones expiradas
   */
  private cleanupExpiredSessions(): void {
    const now = Date.now();
    let cleaned = 0;

    for (const [sessionId, session] of this.sessions.entries()) {
      if (now - session.lastActivity > this.maxSessionAge) {
        this.sessions.delete(sessionId);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      console.log(`🧹 Limpiadas ${cleaned} sesiones expiradas`);
    }
  }

  /**
   * Destructor
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    this.sessions.clear();
  }
}
