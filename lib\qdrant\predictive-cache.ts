/**
 * Predictive Cache System - Sistema de Caché Predictivo
 * 
 * Pre-calcula análisis para consultas frecuentes y patrones comunes
 * para mejorar el tiempo de respuesta en consultas populares.
 * 
 * Día 5 - Optimización de Rendimiento
 */

import { IntelligentCache } from './intelligent-cache';
import { PropertyRAGEngine } from './search';

// Tipos para el sistema predictivo
export interface QueryPattern {
  pattern: string;
  frequency: number;
  lastSeen: number;
  variations: string[];
  priority: number;
}

export interface PredictiveStats {
  totalPatterns: number;
  precalculatedQueries: number;
  cacheHitImprovement: number;
  lastUpdateTime: number;
  topPatterns: QueryPattern[];
}

/**
 * Sistema de caché predictivo para consultas frecuentes
 */
export class PredictiveCache {
  private cache: IntelligentCache;
  private ragEngine: PropertyRAGEngine;
  private queryPatterns: Map<string, QueryPattern>;
  private isEnabled: boolean;
  private updateTimer?: NodeJS.Timeout;

  // Consultas frecuentes predefinidas para Guatemala
  private readonly COMMON_GUATEMALAN_QUERIES = [
    // Ubicaciones populares
    'apartamento zona 14',
    'casa zona 10',
    'apartamento zona 15',
    'casa zona 16',
    'apartamento carretera a El Salvador',
    'casa Antigua Guatemala',
    'apartamento zona 9',
    'casa zona 13',
    
    // Tipos de propiedad comunes
    'apartamento 2 habitaciones',
    'casa 3 habitaciones',
    'apartamento 1 habitación',
    'casa 4 habitaciones',
    'oficina comercial',
    'local comercial',
    
    // Amenidades populares
    'apartamento con piscina',
    'casa con jardín',
    'apartamento con gimnasio',
    'casa con terraza',
    'apartamento con parqueo',
    'casa con seguridad',
    
    // Presupuestos comunes
    'apartamento menos de Q500k',
    'casa menos de Q1M',
    'apartamento económico',
    'casa barata',
    'apartamento entre Q300k y Q800k',
    'casa entre Q800k y Q2M',
    
    // Combinaciones frecuentes
    'apartamento 2 hab zona 14',
    'casa 3 hab con piscina',
    'apartamento zona 10 con parqueo',
    'casa zona 15 con jardín',
    'apartamento nuevo zona 14',
    'casa moderna zona 10',
    
    // Intenciones comunes
    'quiero comprar apartamento',
    'quiero alquilar casa',
    'apartamento para comprar',
    'casa para alquilar',
    'propiedad en venta',
    'propiedad para rentar',
  ];

  constructor(cache: IntelligentCache, ragEngine: PropertyRAGEngine) {
    this.cache = cache;
    this.ragEngine = ragEngine;
    this.queryPatterns = new Map();
    this.isEnabled = process.env.PREDICTIVE_CACHE_ENABLED === 'true';
    
    if (this.isEnabled) {
      this.initializePredictiveCache();
    }
  }

  /**
   * Inicializa el caché predictivo
   */
  private async initializePredictiveCache(): Promise<void> {
    console.log('🔮 Inicializando caché predictivo...');
    
    try {
      // Pre-calcular consultas comunes
      await this.precalculateCommonQueries();
      
      // Iniciar actualización periódica
      this.startPeriodicUpdate();
      
      console.log(`✅ Caché predictivo inicializado con ${this.COMMON_GUATEMALAN_QUERIES.length} consultas`);
      
    } catch (error) {
      console.error('Error inicializando caché predictivo:', error);
    }
  }

  /**
   * Pre-calcula análisis para consultas comunes
   */
  private async precalculateCommonQueries(): Promise<void> {
    const batchSize = 5; // Procesar en lotes para no sobrecargar
    
    for (let i = 0; i < this.COMMON_GUATEMALAN_QUERIES.length; i += batchSize) {
      const batch = this.COMMON_GUATEMALAN_QUERIES.slice(i, i + batchSize);
      
      // Procesar lote en paralelo
      const promises = batch.map(async (query) => {
        try {
          // Verificar si ya está en caché
          const cached = await this.cache.getCachedAnalysis(query);
          if (cached) {
            console.log(`⚡ Ya en caché: "${query}"`);
            return;
          }

          // Pre-calcular análisis
          console.log(`🔮 Pre-calculando: "${query}"`);
          await this.ragEngine.searchPropertiesWithOptimizedCache({
            query,
            limit: 1, // Solo necesitamos el análisis, no muchos resultados
            useSemanticWeights: true,
          });

          // Registrar patrón
          this.registerQueryPattern(query);
          
        } catch (error) {
          console.warn(`Error pre-calculando "${query}":`, error);
        }
      });

      await Promise.allSettled(promises);
      
      // Pausa entre lotes
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  /**
   * Registra un patrón de consulta
   */
  private registerQueryPattern(query: string): void {
    const normalizedQuery = this.normalizeQuery(query);
    const existing = this.queryPatterns.get(normalizedQuery);
    
    if (existing) {
      existing.frequency++;
      existing.lastSeen = Date.now();
      if (!existing.variations.includes(query)) {
        existing.variations.push(query);
      }
    } else {
      this.queryPatterns.set(normalizedQuery, {
        pattern: normalizedQuery,
        frequency: 1,
        lastSeen: Date.now(),
        variations: [query],
        priority: this.calculatePriority(query),
      });
    }
  }

  /**
   * Calcula la prioridad de una consulta
   */
  private calculatePriority(query: string): number {
    let priority = 1;
    
    // Mayor prioridad para ubicaciones específicas
    if (/zona\s*\d+/i.test(query)) priority += 3;
    
    // Mayor prioridad para tipos de propiedad
    if (/(apartamento|casa|oficina)/i.test(query)) priority += 2;
    
    // Mayor prioridad para amenidades
    if (/(piscina|gimnasio|parqueo|jardín)/i.test(query)) priority += 2;
    
    // Mayor prioridad para presupuestos
    if (/(menos|entre|económico|barato)/i.test(query)) priority += 2;
    
    // Mayor prioridad para intenciones claras
    if (/(quiero|comprar|alquilar|venta|renta)/i.test(query)) priority += 1;
    
    return priority;
  }

  /**
   * Normaliza consulta para detección de patrones
   */
  private normalizeQuery(query: string): string {
    return query
      .toLowerCase()
      .replace(/\s+/g, ' ')
      .trim()
      .replace(/[^\w\s]/g, '');
  }

  /**
   * Actualiza el caché predictivo basado en patrones de uso
   */
  async updatePredictiveCache(): Promise<void> {
    if (!this.isEnabled) return;

    console.log('🔮 Actualizando caché predictivo...');
    
    try {
      // Obtener patrones más frecuentes
      const topPatterns = Array.from(this.queryPatterns.values())
        .sort((a, b) => (b.frequency * b.priority) - (a.frequency * a.priority))
        .slice(0, 50); // Top 50 patrones

      // Pre-calcular variaciones de patrones frecuentes
      for (const pattern of topPatterns) {
        for (const variation of pattern.variations.slice(0, 3)) { // Max 3 variaciones por patrón
          const cached = await this.cache.getCachedAnalysis(variation);
          if (!cached) {
            try {
              await this.ragEngine.searchPropertiesWithOptimizedCache({
                query: variation,
                limit: 1,
                useSemanticWeights: true,
              });
              console.log(`🔮 Pre-calculado: "${variation}" (freq: ${pattern.frequency})`);
            } catch (error) {
              console.warn(`Error pre-calculando variación "${variation}":`, error);
            }
          }
        }
      }

      console.log(`✅ Caché predictivo actualizado: ${topPatterns.length} patrones procesados`);
      
    } catch (error) {
      console.error('Error actualizando caché predictivo:', error);
    }
  }

  /**
   * Obtiene estadísticas del caché predictivo
   */
  getStats(): PredictiveStats {
    const patterns = Array.from(this.queryPatterns.values());
    const topPatterns = patterns
      .sort((a, b) => (b.frequency * b.priority) - (a.frequency * a.priority))
      .slice(0, 10);

    return {
      totalPatterns: patterns.length,
      precalculatedQueries: this.COMMON_GUATEMALAN_QUERIES.length,
      cacheHitImprovement: this.calculateCacheImprovement(),
      lastUpdateTime: Date.now(),
      topPatterns,
    };
  }

  /**
   * Calcula la mejora en cache hit ratio
   */
  private calculateCacheImprovement(): number {
    const cacheMetrics = this.cache.getMetrics();
    return cacheMetrics.hitRatio * 100; // Convertir a porcentaje
  }

  /**
   * Inicia actualización periódica
   */
  private startPeriodicUpdate(): void {
    const updateInterval = 6 * 60 * 60 * 1000; // 6 horas
    
    this.updateTimer = setInterval(() => {
      this.updatePredictiveCache();
    }, updateInterval);
  }

  /**
   * Detiene actualización periódica
   */
  stopPeriodicUpdate(): void {
    if (this.updateTimer) {
      clearInterval(this.updateTimer);
      this.updateTimer = undefined;
    }
  }

  /**
   * Sugiere consultas relacionadas
   */
  suggestRelatedQueries(query: string): string[] {
    const normalizedQuery = this.normalizeQuery(query);
    const related: string[] = [];

    // Buscar patrones similares
    for (const pattern of this.queryPatterns.values()) {
      if (this.isSimilarPattern(normalizedQuery, pattern.pattern)) {
        related.push(...pattern.variations.slice(0, 2));
      }
    }

    return related.slice(0, 5); // Máximo 5 sugerencias
  }

  /**
   * Verifica si dos patrones son similares
   */
  private isSimilarPattern(query1: string, query2: string): boolean {
    const words1 = query1.split(' ');
    const words2 = query2.split(' ');
    
    const commonWords = words1.filter(word => words2.includes(word));
    const similarity = commonWords.length / Math.max(words1.length, words2.length);
    
    return similarity > 0.5; // 50% de palabras en común
  }

  /**
   * Destructor
   */
  destroy(): void {
    this.stopPeriodicUpdate();
    this.queryPatterns.clear();
  }
}
