/**
 * Intelligent Precision Filter - Filtro Inteligente de Precisión
 * 
 * Sistema de filtrado que elimina resultados irrelevantes y mejora
 * la precisión general usando análisis semántico avanzado.
 * 
 * Día 7 - Optimización de Precisión Semántica
 */

import { PropertyPayload } from './config';
import { DecomposedQuery, DecompositionResult } from './semantic-decomposer';
import { RelevanceAnalysisResult } from './semantic-relevance-analyzer';
import { OptimizedRankingResult } from './advanced-ranking-optimizer';
import OpenAI from 'openai';

// Cliente OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Tipos para filtrado inteligente
export interface FilterCriteria {
  minRelevanceScore: number;
  minConfidence: number;
  maxIrrelevanceFactors: number;
  requireLocationMatch: boolean;
  requireFeatureAlignment: boolean;
  requirePriceReasonableness: boolean;
  guatemalanContextRequired: boolean;
}

export interface FilterResult {
  passed: boolean;
  reason: string;
  confidence: number;
  filterType: 'relevance' | 'quality' | 'context' | 'semantic' | 'business_logic';
}

export interface FilteringStats {
  totalResults: number;
  filteredResults: number;
  passedResults: number;
  filterReasons: Record<string, number>;
  averageRelevanceImprovement: number;
  processingTime: number;
}

export interface IntelligentFilterOptions {
  strictMode: boolean;           // Filtrado más estricto
  adaptiveThresholds: boolean;   // Ajustar thresholds según contexto
  semanticValidation: boolean;   // Validación semántica con IA
  businessLogicFilters: boolean; // Filtros de lógica de negocio
  qualityFilters: boolean;       // Filtros de calidad de datos
  diversityPreservation: boolean; // Preservar diversidad en resultados
}

/**
 * Filtro inteligente que mejora la precisión eliminando resultados irrelevantes
 */
export class IntelligentPrecisionFilter {
  private guatemalanLocationKeywords = [
    'zona', 'z', 'guatemala', 'mixco', 'villa nueva', 'petapa',
    'carretera', 'roosevelt', 'san lucas', 'antigua', 'escuintla'
  ];

  private guatemalanPropertyTypes = [
    'apartamento', 'apto', 'departamento', 'depto', 'casa',
    'residencia', 'condominio', 'torre', 'proyecto'
  ];

  /**
   * Filtra resultados usando múltiples criterios de precisión
   */
  async filterResults(
    results: OptimizedRankingResult[],
    query: string,
    decomposedQuery: DecompositionResult,
    relevanceAnalyses: RelevanceAnalysisResult[],
    options: IntelligentFilterOptions = {
      strictMode: false,
      adaptiveThresholds: true,
      semanticValidation: true,
      businessLogicFilters: true,
      qualityFilters: true,
      diversityPreservation: true,
    }
  ): Promise<{ filteredResults: OptimizedRankingResult[]; stats: FilteringStats }> {
    const startTime = Date.now();
    console.log(`🔍 Aplicando filtrado inteligente a ${results.length} resultados`);

    // 1. Determinar criterios de filtrado adaptativos
    const filterCriteria = this.determineFilterCriteria(query, decomposedQuery, options);

    // 2. Aplicar filtros secuenciales
    let filteredResults = [...results];
    const filterReasons: Record<string, number> = {};

    // Filtro 1: Relevancia mínima
    const relevanceFiltered = await this.applyRelevanceFilter(
      filteredResults, relevanceAnalyses, filterCriteria, options
    );
    this.updateFilterStats(filterReasons, 'relevancia_baja', 
      filteredResults.length - relevanceFiltered.length);
    filteredResults = relevanceFiltered;

    // Filtro 2: Calidad de datos
    if (options.qualityFilters) {
      const qualityFiltered = this.applyQualityFilter(filteredResults, filterCriteria);
      this.updateFilterStats(filterReasons, 'calidad_datos', 
        filteredResults.length - qualityFiltered.length);
      filteredResults = qualityFiltered;
    }

    // Filtro 3: Validación semántica (temporalmente deshabilitado para depuración)
    if (options.semanticValidation && options.strictMode) { // Solo en modo estricto
      const semanticFiltered = await this.applySemanticValidationFilter(
        filteredResults, query, decomposedQuery, options
      );
      this.updateFilterStats(filterReasons, 'validacion_semantica',
        filteredResults.length - semanticFiltered.length);
      filteredResults = semanticFiltered;
    }

    // Filtro 4: Lógica de negocio (temporalmente deshabilitado para depuración)
    if (options.businessLogicFilters && options.strictMode) { // Solo en modo estricto
      const businessFiltered = this.applyBusinessLogicFilter(
        filteredResults, decomposedQuery, filterCriteria
      );
      this.updateFilterStats(filterReasons, 'logica_negocio',
        filteredResults.length - businessFiltered.length);
      filteredResults = businessFiltered;
    }

    // Filtro 5: Preservación de diversidad
    if (options.diversityPreservation && filteredResults.length > 10) {
      const diversityFiltered = this.applyDiversityPreservationFilter(filteredResults);
      this.updateFilterStats(filterReasons, 'diversidad', 
        filteredResults.length - diversityFiltered.length);
      filteredResults = diversityFiltered;
    }

    // 3. Calcular estadísticas
    const stats = this.calculateFilteringStats(
      results, filteredResults, filterReasons, Date.now() - startTime
    );

    console.log(`✅ Filtrado completado: ${results.length} → ${filteredResults.length} resultados`);
    return { filteredResults, stats };
  }

  /**
   * Determina criterios de filtrado adaptativos según el contexto
   */
  private determineFilterCriteria(
    query: string,
    decomposedQuery: DecompositionResult,
    options: IntelligentFilterOptions
  ): FilterCriteria {
    const baseThresholds = {
      minRelevanceScore: options.strictMode ? 0.4 : 0.15, // Muy permisivo para depuración
      minConfidence: options.strictMode ? 0.5 : 0.3, // Muy permisivo para depuración
      maxIrrelevanceFactors: options.strictMode ? 3 : 5, // Muy permisivo para depuración
      requireLocationMatch: false,
      requireFeatureAlignment: false,
      requirePriceReasonableness: false,
      guatemalanContextRequired: false,
    };

    // Ajustes adaptativos
    if (options.adaptiveThresholds) {
      // Si hay ubicación específica, requerir coincidencia
      if (decomposedQuery.decomposed.location && 
          this.isSpecificLocation(decomposedQuery.decomposed.location)) {
        baseThresholds.requireLocationMatch = true;
        baseThresholds.minRelevanceScore += 0.1;
      }

      // Si hay características específicas, requerir alineación
      if (decomposedQuery.decomposed.amenities || decomposedQuery.decomposed.property) {
        baseThresholds.requireFeatureAlignment = true;
      }

      // Si hay precio específico, requerir razonabilidad
      if (decomposedQuery.decomposed.price) {
        baseThresholds.requirePriceReasonableness = true;
      }

      // Consultas cortas requieren mayor precisión
      if (query.length < 20) {
        baseThresholds.minRelevanceScore += 0.1;
        baseThresholds.minConfidence += 0.1;
      }
    }

    return baseThresholds;
  }

  /**
   * Aplica filtro de relevancia mínima
   */
  private async applyRelevanceFilter(
    results: OptimizedRankingResult[],
    relevanceAnalyses: RelevanceAnalysisResult[],
    criteria: FilterCriteria,
    options: IntelligentFilterOptions
  ): Promise<OptimizedRankingResult[]> {
    return results.filter((result, index) => {
      const analysis = relevanceAnalyses[index];
      if (!analysis) return true; // Mantener si no hay análisis

      // Verificar relevancia mínima
      if (analysis.metrics.overallRelevance < criteria.minRelevanceScore) {
        return false;
      }

      // Verificar confianza mínima
      if (analysis.confidence < criteria.minConfidence) {
        return false;
      }

      // Verificar factores de irrelevancia
      if (analysis.irrelevanceFactors.length > criteria.maxIrrelevanceFactors) {
        return false;
      }

      // Verificar recomendación
      if (options.strictMode && analysis.recommendation === 'irrelevant') {
        return false;
      }

      return true;
    });
  }

  /**
   * Aplica filtro de calidad de datos
   */
  private applyQualityFilter(
    results: OptimizedRankingResult[],
    criteria: FilterCriteria
  ): OptimizedRankingResult[] {
    return results.filter(result => {
      const qualityScore = result.rankingFactors.qualityScore;
      
      // Filtrar propiedades con datos muy incompletos
      if (qualityScore < 0.3) {
        return false;
      }

      return true;
    });
  }

  /**
   * Aplica validación semántica usando IA
   */
  private async applySemanticValidationFilter(
    results: OptimizedRankingResult[],
    query: string,
    decomposedQuery: DecompositionResult,
    options: IntelligentFilterOptions
  ): Promise<OptimizedRankingResult[]> {
    // Para evitar demasiadas llamadas a la API, validar solo los primeros 20 resultados
    const resultsToValidate = results.slice(0, 20);
    const remainingResults = results.slice(20);

    const validatedResults = await Promise.all(
      resultsToValidate.map(async result => {
        const isValid = await this.validateSemanticRelevance(query, result, decomposedQuery);
        return isValid ? result : null;
      })
    );

    const filteredValidated = validatedResults.filter(result => result !== null) as OptimizedRankingResult[];
    
    // Combinar resultados validados con los restantes (asumiendo que son válidos)
    return [...filteredValidated, ...remainingResults];
  }

  /**
   * Valida relevancia semántica usando IA
   */
  private async validateSemanticRelevance(
    query: string,
    result: OptimizedRankingResult,
    decomposedQuery: DecompositionResult
  ): Promise<boolean> {
    try {
      // Obtener información de la propiedad desde el resultado
      // Nota: Necesitaríamos acceso al payload aquí, simplificaremos por ahora
      
      const prompt = `¿Es esta propiedad relevante para la consulta del usuario?

CONSULTA: "${query}"

CRITERIOS DETECTADOS:
- Ubicación: ${decomposedQuery.decomposed.location || 'No especificada'}
- Tipo: ${decomposedQuery.decomposed.property || 'No especificado'}
- Amenidades: ${decomposedQuery.decomposed.amenities || 'No especificadas'}

SCORE DE RELEVANCIA: ${(result.rankingFactors.semanticRelevance * 100).toFixed(0)}%
FACTORES POSITIVOS: ${result.rankingBoosts.length}
FACTORES NEGATIVOS: ${result.rankingPenalties.length}

Responde solo "SÍ" o "NO" basado en si la propiedad es genuinamente relevante para lo que busca el usuario.`;

      const response = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: prompt }],
        max_tokens: 5,
        temperature: 0.1,
      });

      const answer = response.choices[0]?.message?.content?.trim().toUpperCase();
      return answer === 'SÍ' || answer === 'SI' || answer === 'YES';

    } catch (error) {
      console.error('Error en validación semántica:', error);
      return true; // En caso de error, mantener el resultado
    }
  }

  /**
   * Aplica filtros de lógica de negocio
   */
  private applyBusinessLogicFilter(
    results: OptimizedRankingResult[],
    decomposedQuery: DecompositionResult,
    criteria: FilterCriteria
  ): OptimizedRankingResult[] {
    return results.filter(result => {
      // Filtro 1: Coincidencia de ubicación requerida
      if (criteria.requireLocationMatch) {
        if (result.rankingFactors.locationPrecision < 0.3) { // Más permisivo
          return false;
        }
      }

      // Filtro 2: Alineación de características requerida
      if (criteria.requireFeatureAlignment) {
        if (result.rankingFactors.featureMatch < 0.2) { // Más permisivo
          return false;
        }
      }

      // Filtro 3: Razonabilidad de precio requerida
      if (criteria.requirePriceReasonableness) {
        if (result.rankingFactors.priceAlignment < 0.2) { // Más permisivo
          return false;
        }
      }

      // Filtro 4: Score optimizado mínimo (más permisivo)
      if (result.optimizedScore < 0.05) { // Más permisivo
        return false;
      }

      return true;
    });
  }

  /**
   * Aplica filtro de preservación de diversidad
   */
  private applyDiversityPreservationFilter(
    results: OptimizedRankingResult[]
  ): OptimizedRankingResult[] {
    // Mantener diversidad preservando al menos un resultado de cada "tipo"
    // Implementación simplificada: mantener los mejores resultados
    const maxResults = Math.min(results.length, 15); // Limitar a 15 resultados máximo
    return results.slice(0, maxResults);
  }

  /**
   * Verifica si una ubicación es específica
   */
  private isSpecificLocation(location: string): boolean {
    const locationLower = location.toLowerCase();
    
    // Verificar si contiene términos específicos guatemaltecos
    return this.guatemalanLocationKeywords.some(keyword => 
      locationLower.includes(keyword)
    );
  }

  /**
   * Actualiza estadísticas de filtrado
   */
  private updateFilterStats(
    filterReasons: Record<string, number>, 
    reason: string, 
    count: number
  ): void {
    if (count > 0) {
      filterReasons[reason] = (filterReasons[reason] || 0) + count;
    }
  }

  /**
   * Calcula estadísticas finales de filtrado
   */
  private calculateFilteringStats(
    originalResults: OptimizedRankingResult[],
    filteredResults: OptimizedRankingResult[],
    filterReasons: Record<string, number>,
    processingTime: number
  ): FilteringStats {
    const totalResults = originalResults.length;
    const passedResults = filteredResults.length;
    const filteredCount = totalResults - passedResults;

    // Calcular mejora promedio de relevancia
    const originalAvgRelevance = originalResults.reduce((sum, r) => 
      sum + r.rankingFactors.semanticRelevance, 0) / totalResults;
    const filteredAvgRelevance = filteredResults.length > 0 
      ? filteredResults.reduce((sum, r) => sum + r.rankingFactors.semanticRelevance, 0) / passedResults
      : 0;

    return {
      totalResults,
      filteredResults: filteredCount,
      passedResults,
      filterReasons,
      averageRelevanceImprovement: filteredAvgRelevance - originalAvgRelevance,
      processingTime,
    };
  }
}
