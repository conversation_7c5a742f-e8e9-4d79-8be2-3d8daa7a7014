#!/usr/bin/env node

/**
 * EJECUTOR DE PRUEBAS DE COMPARACIÓN
 * 
 * Script simple para ejecutar la comparación entre sistemas
 * Uso: node scripts/run-comparison.js
 */

// Verificar que tenemos Node.js con fetch
if (!globalThis.fetch) {
  try {
    globalThis.fetch = require('node-fetch');
  } catch (error) {
    console.error('❌ Error: node-fetch no está instalado');
    console.log('📦 Instalar con: npm install node-fetch');
    process.exit(1);
  }
}

// Cargar variables de entorno
require('dotenv').config();

// Importar el script de comparación
const { runComparison } = require('./test-search-comparison');

console.log('🚀 INICIANDO PRUEBA COMPARATIVA DE SISTEMAS DE BÚSQUEDA');
console.log('📅 Fecha:', new Date().toLocaleString());
console.log('🌐 API URL:', process.env.API_BASE_URL || process.env.VERCEL_URL || 'https://tu-proyecto.vercel.app');
console.log('');

// Ejecutar la comparación
runComparison()
  .then(() => {
    console.log('\n🎉 ¡Prueba completada exitosamente!');
    console.log('📄 Los resultados se han mostrado arriba');
  })
  .catch(error => {
    console.error('\n❌ Error durante la prueba:', error.message);
    console.error('🔍 Detalles:', error);
    process.exit(1);
  });
