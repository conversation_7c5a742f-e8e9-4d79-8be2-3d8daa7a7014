/**
 * Semantic Relevance Analyzer - Analizador de Relevancia Semántica Avanzada
 *
 * Sistema que analiza la relevancia semántica de los resultados de búsqueda
 * usando múltiples métricas y contexto regional adaptativo.
 *
 * Día 7 - Optimización de Precisión Semántica
 */

import { generateEmbedding } from './embeddings';
import { PropertyPayload } from './config';
import { DecomposedQuery, DecompositionResult } from './semantic-decomposer';
import OpenAI from 'openai';

// Cliente OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Tipos para análisis de relevancia
export interface RelevanceMetrics {
  semanticSimilarity: number;      // Similitud semántica directa
  contextualRelevance: number;     // Relevancia contextual
  locationAccuracy: number;        // Precisión de ubicación
  featureAlignment: number;        // Alineación de características
  priceRelevance: number;         // Relevancia de precio
  regionalContext: number;         // Contexto regional específico (adaptativo por país)
  overallRelevance: number;        // Relevancia general calculada
}

export interface RelevanceAnalysisResult {
  propertyId: string;
  originalScore: number;
  adjustedScore: number;
  metrics: RelevanceMetrics;
  relevanceFactors: string[];
  irrelevanceFactors: string[];
  confidence: number;
  recommendation: 'highly_relevant' | 'relevant' | 'moderately_relevant' | 'low_relevance' | 'irrelevant';
  processingTime: number;
}

export interface PropertyRelevanceContext {
  query: string;
  decomposedQuery: DecompositionResult;
  property: PropertyPayload;
  originalScore: number;
  userContext?: {
    familySize?: number;
    budget?: { min: number; max: number };
    urgency?: 'low' | 'medium' | 'high';
    preferences?: string[];
  };
}

/**
 * Analizador de relevancia semántica que evalúa qué tan relevante
 * es cada resultado para la consulta específica del usuario
 */
export class SemanticRelevanceAnalyzer {
  // Términos regionales por defecto (Guatemala como base del sistema)
  private regionalLocationTerms = [
    'zona', 'z', 'carretera', 'km', 'calzada', 'avenida', 'calle',
    'roosevelt', 'petapa', 'mixco', 'villa nueva', 'san lucas',
    'antigua', 'escuintla', 'chimaltenango', 'sacatepéquez'
  ];

  private regionalPropertyTerms = [
    'apartamento', 'apto', 'departamento', 'depto', 'casa', 'residencia',
    'condominio', 'torre', 'edificio', 'proyecto', 'complejo'
  ];

  private regionalAmenityTerms = [
    'piscina', 'gimnasio', 'parqueo', 'seguridad', 'área social',
    'salón de eventos', 'cancha', 'jardín', 'terraza', 'balcón'
  ];

  /**
   * Analiza la relevancia de una propiedad para una consulta específica
   */
  async analyzeRelevance(context: PropertyRelevanceContext): Promise<RelevanceAnalysisResult> {
    const startTime = Date.now();

    try {
      // 1. Calcular métricas de relevancia
      const metrics = await this.calculateRelevanceMetrics(context);

      // 2. Identificar factores de relevancia e irrelevancia
      const { relevanceFactors, irrelevanceFactors } = this.identifyRelevanceFactors(context, metrics);

      // 3. Calcular score ajustado
      const adjustedScore = this.calculateAdjustedScore(context.originalScore, metrics);

      // 4. Determinar recomendación
      const recommendation = this.determineRecommendation(metrics);

      // 5. Calcular confianza
      const confidence = this.calculateConfidence(metrics, relevanceFactors.length, irrelevanceFactors.length);

      return {
        propertyId: context.property.propertyId || 'unknown',
        originalScore: context.originalScore,
        adjustedScore,
        metrics,
        relevanceFactors,
        irrelevanceFactors,
        confidence,
        recommendation,
        processingTime: Date.now() - startTime,
      };

    } catch (error) {
      console.error('Error en análisis de relevancia:', error);
      
      // Fallback: retornar análisis básico
      return this.createFallbackAnalysis(context, Date.now() - startTime);
    }
  }

  /**
   * Calcula múltiples métricas de relevancia
   */
  private async calculateRelevanceMetrics(context: PropertyRelevanceContext): Promise<RelevanceMetrics> {
    const { query, decomposedQuery, property } = context;

    // 1. Similitud semántica directa
    const semanticSimilarity = await this.calculateSemanticSimilarity(query, property);

    // 2. Relevancia contextual
    const contextualRelevance = await this.calculateContextualRelevance(decomposedQuery, property);

    // 3. Precisión de ubicación
    const locationAccuracy = this.calculateLocationAccuracy(decomposedQuery.decomposed.location, property);

    // 4. Alineación de características
    const featureAlignment = this.calculateFeatureAlignment(decomposedQuery, property);

    // 5. Relevancia de precio
    const priceRelevance = this.calculatePriceRelevance(decomposedQuery.decomposed.price, property);

    // 6. Contexto regional
    const regionalContext = this.calculateRegionalContext(query, property);

    // 7. Relevancia general (combinación ponderada)
    const overallRelevance = this.calculateOverallRelevance({
      semanticSimilarity,
      contextualRelevance,
      locationAccuracy,
      featureAlignment,
      priceRelevance,
      regionalContext,
    });

    return {
      semanticSimilarity,
      contextualRelevance,
      locationAccuracy,
      featureAlignment,
      priceRelevance,
      regionalContext,
      overallRelevance,
    };
  }

  /**
   * Calcula similitud semántica directa usando embeddings
   */
  private async calculateSemanticSimilarity(query: string, property: PropertyPayload): Promise<number> {
    try {
      // Crear texto descriptivo de la propiedad
      const propertyText = this.createPropertyDescription(property);
      
      // Generar embeddings
      const [queryEmbedding, propertyEmbedding] = await Promise.all([
        generateEmbedding(query),
        generateEmbedding(propertyText)
      ]);

      // Calcular similitud coseno
      return this.calculateCosineSimilarity(queryEmbedding, propertyEmbedding);

    } catch (error) {
      console.error('Error calculando similitud semántica:', error);
      return 0.5; // Valor neutral en caso de error
    }
  }

  /**
   * Calcula relevancia contextual usando análisis de IA
   */
  private async calculateContextualRelevance(decomposedQuery: DecompositionResult, property: PropertyPayload): Promise<number> {
    try {
      const prompt = `Analiza qué tan relevante es esta propiedad para la consulta del usuario.

CONSULTA DESCOMPUESTA:
- Ubicación: ${decomposedQuery.decomposed.location || 'No especificada'}
- Tipo: ${decomposedQuery.decomposed.property || 'No especificado'}
- Amenidades: ${decomposedQuery.decomposed.amenities || 'No especificadas'}
- Precio: ${decomposedQuery.decomposed.price || 'No especificado'}
- Intención: ${decomposedQuery.decomposed.intent || 'No especificada'}

PROPIEDAD:
- Ubicación: ${property.level3 || property.level4 || 'No especificada'}
- Tipo: ${property.type || 'No especificado'}
- Precio: Q${property.price?.toLocaleString() || 'No especificado'}
- Habitaciones: ${property.bedrooms || 'No especificado'}
- Baños: ${property.bathrooms || 'No especificado'}
- Área: ${property.area || 'No especificada'}m²
- Estado: ${property.status || 'No especificado'}

Evalúa la relevancia contextual en una escala de 0.0 a 1.0, considerando:
1. Coincidencia de ubicación específica
2. Coincidencia de tipo de propiedad
3. Alineación con intención (compra/alquiler)
4. Coherencia con criterios implícitos

Responde solo con un número decimal entre 0.0 y 1.0.`;

      const response = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: prompt }],
        max_tokens: 10,
        temperature: 0.1,
      });

      const relevanceText = response.choices[0]?.message?.content?.trim();
      const relevance = parseFloat(relevanceText || '0.5');
      
      return Math.max(0, Math.min(1, relevance)); // Clamp entre 0 y 1

    } catch (error) {
      console.error('Error calculando relevancia contextual:', error);
      return 0.5; // Valor neutral en caso de error
    }
  }

  /**
   * Calcula precisión de ubicación
   */
  private calculateLocationAccuracy(queryLocation: string, property: PropertyPayload): number {
    if (!queryLocation) return 0.5; // Neutral si no hay ubicación en query

    const queryLower = queryLocation.toLowerCase();
    const propertyLocations = [
      property.level1,
      property.level2,
      property.level3,
      property.level4,
      property.neighborhood
    ].filter(Boolean).map(loc => loc!.toLowerCase());

    // Buscar coincidencias exactas
    for (const location of propertyLocations) {
      if (location.includes(queryLower) || queryLower.includes(location)) {
        return 1.0; // Coincidencia exacta
      }
    }

    // Buscar coincidencias parciales con términos regionales
    for (const term of this.regionalLocationTerms) {
      if (queryLower.includes(term)) {
        for (const location of propertyLocations) {
          if (location.includes(term)) {
            return 0.7; // Coincidencia parcial
          }
        }
      }
    }

    return 0.2; // Baja coincidencia
  }

  /**
   * Calcula alineación de características
   */
  private calculateFeatureAlignment(decomposedQuery: DecompositionResult, property: PropertyPayload): number {
    let alignmentScore = 0;
    let totalFactors = 0;

    // Verificar tipo de propiedad
    if (decomposedQuery.decomposed.property) {
      totalFactors++;
      const queryType = decomposedQuery.decomposed.property.toLowerCase();
      const propertyType = (property.type || '').toLowerCase();
      
      if (propertyType.includes(queryType) || queryType.includes(propertyType)) {
        alignmentScore += 1;
      } else {
        // Verificar sinónimos guatemaltecos
        const synonyms = this.getPropertyTypeSynonyms(queryType);
        if (synonyms.some(syn => propertyType.includes(syn))) {
          alignmentScore += 0.8;
        }
      }
    }

    // Verificar amenidades
    if (decomposedQuery.decomposed.amenities) {
      totalFactors++;
      const queryAmenities = decomposedQuery.decomposed.amenities.toLowerCase();
      const propertyDescription = (property.description || '').toLowerCase();
      
      let amenityMatches = 0;
      for (const amenity of this.regionalAmenityTerms) {
        if (queryAmenities.includes(amenity) && propertyDescription.includes(amenity)) {
          amenityMatches++;
        }
      }
      
      if (amenityMatches > 0) {
        alignmentScore += Math.min(1, amenityMatches / 3); // Máximo 1, escalado por número de coincidencias
      }
    }

    return totalFactors > 0 ? alignmentScore / totalFactors : 0.5;
  }

  /**
   * Calcula relevancia de precio
   */
  private calculatePriceRelevance(queryPrice: string, property: PropertyPayload): number {
    if (!queryPrice || !property.price) return 0.5; // Neutral si no hay información de precio

    // Extraer rangos de precio de la consulta
    const priceNumbers = queryPrice.match(/[\d,]+/g);
    if (!priceNumbers) return 0.5;

    const queryPriceValue = parseInt(priceNumbers[0].replace(/,/g, ''));
    const propertyPrice = property.price;

    // Calcular diferencia relativa
    const difference = Math.abs(propertyPrice - queryPriceValue) / queryPriceValue;
    
    if (difference <= 0.1) return 1.0;      // Dentro del 10%
    if (difference <= 0.2) return 0.8;      // Dentro del 20%
    if (difference <= 0.3) return 0.6;      // Dentro del 30%
    if (difference <= 0.5) return 0.4;      // Dentro del 50%
    
    return 0.2; // Fuera del rango razonable
  }

  /**
   * Calcula contexto regional específico (adaptativo por país)
   */
  private calculateRegionalContext(query: string, property: PropertyPayload): number {
    const queryLower = query.toLowerCase();
    let contextScore = 0;
    let totalFactors = 0;

    // Verificar términos de ubicación regionales
    totalFactors++;
    const locationTermsFound = this.regionalLocationTerms.filter(term =>
      queryLower.includes(term)
    ).length;
    contextScore += Math.min(1, locationTermsFound / 3);

    // Verificar términos de propiedad regionales
    totalFactors++;
    const propertyTermsFound = this.regionalPropertyTerms.filter(term =>
      queryLower.includes(term)
    ).length;
    contextScore += Math.min(1, propertyTermsFound / 2);

    // Verificar términos de amenidades regionales
    totalFactors++;
    const amenityTermsFound = this.regionalAmenityTerms.filter(term =>
      queryLower.includes(term)
    ).length;
    contextScore += Math.min(1, amenityTermsFound / 3);

    return contextScore / totalFactors;
  }

  /**
   * Calcula relevancia general combinando todas las métricas
   */
  private calculateOverallRelevance(metrics: Omit<RelevanceMetrics, 'overallRelevance'>): number {
    // Pesos para cada métrica (deben sumar 1.0)
    const weights = {
      semanticSimilarity: 0.25,
      contextualRelevance: 0.25,
      locationAccuracy: 0.20,
      featureAlignment: 0.15,
      priceRelevance: 0.10,
      regionalContext: 0.05,
    };

    return (
      metrics.semanticSimilarity * weights.semanticSimilarity +
      metrics.contextualRelevance * weights.contextualRelevance +
      metrics.locationAccuracy * weights.locationAccuracy +
      metrics.featureAlignment * weights.featureAlignment +
      metrics.priceRelevance * weights.priceRelevance +
      metrics.regionalContext * weights.regionalContext
    );
  }

  /**
   * Identifica factores específicos de relevancia e irrelevancia
   */
  private identifyRelevanceFactors(
    context: PropertyRelevanceContext,
    metrics: RelevanceMetrics
  ): { relevanceFactors: string[]; irrelevanceFactors: string[] } {
    const relevanceFactors: string[] = [];
    const irrelevanceFactors: string[] = [];

    // Factores de ubicación
    if (metrics.locationAccuracy >= 0.8) {
      relevanceFactors.push(`Ubicación muy precisa (${(metrics.locationAccuracy * 100).toFixed(0)}%)`);
    } else if (metrics.locationAccuracy <= 0.3) {
      irrelevanceFactors.push(`Ubicación no coincide (${(metrics.locationAccuracy * 100).toFixed(0)}%)`);
    }

    // Factores de características
    if (metrics.featureAlignment >= 0.8) {
      relevanceFactors.push(`Características alineadas (${(metrics.featureAlignment * 100).toFixed(0)}%)`);
    } else if (metrics.featureAlignment <= 0.3) {
      irrelevanceFactors.push(`Características no coinciden (${(metrics.featureAlignment * 100).toFixed(0)}%)`);
    }

    // Factores de precio
    if (metrics.priceRelevance >= 0.8) {
      relevanceFactors.push(`Precio dentro del rango esperado`);
    } else if (metrics.priceRelevance <= 0.3) {
      irrelevanceFactors.push(`Precio fuera del rango esperado`);
    }

    // Factores semánticos
    if (metrics.semanticSimilarity >= 0.7) {
      relevanceFactors.push(`Alta similitud semántica`);
    } else if (metrics.semanticSimilarity <= 0.4) {
      irrelevanceFactors.push(`Baja similitud semántica`);
    }

    // Factores contextuales
    if (metrics.contextualRelevance >= 0.7) {
      relevanceFactors.push(`Contexto muy relevante`);
    } else if (metrics.contextualRelevance <= 0.4) {
      irrelevanceFactors.push(`Contexto poco relevante`);
    }

    return { relevanceFactors, irrelevanceFactors };
  }

  /**
   * Calcula score ajustado basado en métricas de relevancia
   */
  private calculateAdjustedScore(originalScore: number, metrics: RelevanceMetrics): number {
    // Factor de ajuste basado en relevancia general
    const relevanceFactor = metrics.overallRelevance;

    // Aplicar ajuste no lineal para amplificar diferencias
    let adjustmentMultiplier = 1.0;

    if (relevanceFactor >= 0.8) {
      adjustmentMultiplier = 1.3; // Boost significativo para alta relevancia
    } else if (relevanceFactor >= 0.6) {
      adjustmentMultiplier = 1.1; // Boost moderado
    } else if (relevanceFactor <= 0.3) {
      adjustmentMultiplier = 0.7; // Penalización para baja relevancia
    } else if (relevanceFactor <= 0.4) {
      adjustmentMultiplier = 0.85; // Penalización leve
    }

    // Ajustes específicos adicionales
    if (metrics.locationAccuracy >= 0.9) {
      adjustmentMultiplier *= 1.1; // Bonus por ubicación muy precisa
    }

    if (metrics.featureAlignment >= 0.9) {
      adjustmentMultiplier *= 1.05; // Bonus por características perfectas
    }

    return Math.min(originalScore * adjustmentMultiplier, 1.0); // Clamp máximo en 1.0
  }

  /**
   * Determina recomendación basada en métricas
   */
  private determineRecommendation(metrics: RelevanceMetrics): 'highly_relevant' | 'relevant' | 'moderately_relevant' | 'low_relevance' | 'irrelevant' {
    const overall = metrics.overallRelevance;

    if (overall >= 0.8) return 'highly_relevant';
    if (overall >= 0.6) return 'relevant';
    if (overall >= 0.4) return 'moderately_relevant';
    if (overall >= 0.2) return 'low_relevance';
    return 'irrelevant';
  }

  /**
   * Calcula confianza del análisis
   */
  private calculateConfidence(
    metrics: RelevanceMetrics,
    relevanceFactorsCount: number,
    irrelevanceFactorsCount: number
  ): number {
    // Factores que afectan la confianza
    const consistencyFactor = this.calculateMetricsConsistency(metrics);
    const evidenceFactor = Math.min(1, (relevanceFactorsCount + irrelevanceFactorsCount) / 5);
    const clarityFactor = relevanceFactorsCount > irrelevanceFactorsCount ? 0.8 : 0.6;

    return (consistencyFactor * 0.5) + (evidenceFactor * 0.3) + (clarityFactor * 0.2);
  }

  /**
   * Calcula consistencia entre métricas
   */
  private calculateMetricsConsistency(metrics: RelevanceMetrics): number {
    const values = [
      metrics.semanticSimilarity,
      metrics.contextualRelevance,
      metrics.locationAccuracy,
      metrics.featureAlignment,
      metrics.priceRelevance,
    ];

    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;

    // Menor varianza = mayor consistencia
    return Math.max(0, 1 - variance);
  }

  /**
   * Crea descripción textual de la propiedad para análisis semántico
   */
  private createPropertyDescription(property: PropertyPayload): string {
    const parts = [];

    // Tipo y ubicación
    if (property.type) parts.push(property.type);
    if (property.level3) parts.push(`en ${property.level3}`);
    if (property.level4) parts.push(property.level4);

    // Características básicas
    if (property.bedrooms) parts.push(`${property.bedrooms} habitaciones`);
    if (property.bathrooms) parts.push(`${property.bathrooms} baños`);
    if (property.area) parts.push(`${property.area} metros cuadrados`);

    // Precio y estado
    if (property.price) parts.push(`Q${property.price.toLocaleString()}`);
    if (property.status) parts.push(property.status);

    // Descripción adicional
    if (property.description) parts.push(property.description);

    return parts.join(' ');
  }

  /**
   * Calcula similitud coseno entre dos vectores
   */
  private calculateCosineSimilarity(vectorA: number[], vectorB: number[]): number {
    if (vectorA.length !== vectorB.length) return 0;

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < vectorA.length; i++) {
      dotProduct += vectorA[i] * vectorB[i];
      normA += vectorA[i] * vectorA[i];
      normB += vectorB[i] * vectorB[i];
    }

    if (normA === 0 || normB === 0) return 0;

    return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
  }

  /**
   * Obtiene sinónimos guatemaltecos para tipos de propiedad
   */
  private getPropertyTypeSynonyms(type: string): string[] {
    const synonymMap: Record<string, string[]> = {
      'apartamento': ['apto', 'departamento', 'depto'],
      'apto': ['apartamento', 'departamento', 'depto'],
      'departamento': ['apartamento', 'apto', 'depto'],
      'depto': ['apartamento', 'apto', 'departamento'],
      'casa': ['residencia', 'vivienda'],
      'residencia': ['casa', 'vivienda'],
      'condominio': ['complejo', 'proyecto'],
      'complejo': ['condominio', 'proyecto'],
    };

    return synonymMap[type] || [];
  }

  /**
   * Crea análisis de fallback en caso de error
   */
  private createFallbackAnalysis(context: PropertyRelevanceContext, processingTime: number): RelevanceAnalysisResult {
    const neutralMetrics: RelevanceMetrics = {
      semanticSimilarity: 0.5,
      contextualRelevance: 0.5,
      locationAccuracy: 0.5,
      featureAlignment: 0.5,
      priceRelevance: 0.5,
      regionalContext: 0.5,
      overallRelevance: 0.5,
    };

    return {
      propertyId: context.property.propertyId || 'unknown',
      originalScore: context.originalScore,
      adjustedScore: context.originalScore,
      metrics: neutralMetrics,
      relevanceFactors: ['Análisis básico aplicado'],
      irrelevanceFactors: [],
      confidence: 0.3,
      recommendation: 'moderately_relevant',
      processingTime,
    };
  }
}
