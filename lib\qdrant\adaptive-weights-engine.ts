/**
 * Adaptive Weights Engine - Motor de Pesos Adaptativos Dinámicos
 * 
 * Sistema que ajusta automáticamente los pesos semánticos según el perfil
 * de consulta detectado, optimizando la precisión de búsqueda.
 * 
 * Día 4 - Optimización de Pesos Semánticos
 */

import { QueryProfileDetector, ProfileDetectionResult } from './query-profile-detector';
import { SemanticWeights } from './unified-config';
import { WeightProfile, SEMANTIC_WEIGHT_PROFILES } from './weight-config';

// Tipos para el motor adaptativo
export interface AdaptiveWeightsResult {
  originalQuery: string;
  detectedProfile: WeightProfile;
  finalWeights: SemanticWeights;
  adaptiveAdjustments: boolean;
  profileDetection: ProfileDetectionResult;
  optimizationApplied: string[];
  processingTime: number;
  confidence: number;
}

export interface WeightOptimizationRule {
  name: string;
  condition: (analysis: ProfileDetectionResult) => boolean;
  adjustment: (weights: SemanticWeights) => SemanticWeights;
  description: string;
}

/**
 * Motor de pesos adaptativos que optimiza automáticamente los pesos semánticos
 */
export class AdaptiveWeightsEngine {
  private profileDetector: QueryProfileDetector;
  private optimizationRules: WeightOptimizationRule[];

  constructor() {
    this.profileDetector = new QueryProfileDetector();
    this.optimizationRules = this.initializeOptimizationRules();
  }

  /**
   * Calcula los pesos óptimos para una consulta específica
   */
  async calculateOptimalWeights(query: string): Promise<AdaptiveWeightsResult> {
    const startTime = Date.now();

    try {
      // 1. Detectar perfil de consulta
      const profileDetection = await this.profileDetector.detectProfile(query);

      // 2. Obtener pesos base del perfil detectado
      let finalWeights = { ...profileDetection.profile.weights };

      // 3. Aplicar pesos adaptativos si están disponibles
      if (profileDetection.adaptiveWeights) {
        finalWeights = { ...profileDetection.adaptiveWeights };
      }

      // 4. Aplicar reglas de optimización
      const optimizationApplied: string[] = [];
      for (const rule of this.optimizationRules) {
        if (rule.condition(profileDetection)) {
          finalWeights = rule.adjustment(finalWeights);
          optimizationApplied.push(rule.name);
        }
      }

      // 5. Normalizar pesos para asegurar que sumen 1.0
      finalWeights = this.normalizeWeights(finalWeights);

      // 6. Validar resultado
      this.validateWeights(finalWeights);

      return {
        originalQuery: query,
        detectedProfile: profileDetection.profile,
        finalWeights,
        adaptiveAdjustments: profileDetection.adaptiveWeights !== undefined,
        profileDetection,
        optimizationApplied,
        processingTime: Date.now() - startTime,
        confidence: profileDetection.analysis.confidence,
      };

    } catch (error) {
      console.error('Error en cálculo de pesos adaptativos:', error);
      return this.createFallbackResult(query, Date.now() - startTime);
    }
  }

  /**
   * Inicializa las reglas de optimización
   */
  private initializeOptimizationRules(): WeightOptimizationRule[] {
    return [
      // Regla 1: Boost para ubicaciones específicas (regional)
      {
        name: 'location_boost',
        condition: (result) =>
          result.analysis.detectedCriteria.location &&
          result.analysis.confidence > 0.8,
        adjustment: (weights) => ({
          ...weights,
          location: Math.min(weights.location + 0.1, 0.7), // Max 70%
          amenities: Math.max(weights.amenities - 0.05, 0.05),
          price: Math.max(weights.price - 0.05, 0.05),
        }),
        description: 'Boost para ubicaciones específicas con alta confianza (adaptativo por región)',
      },

      // Regla 2: Optimización para consultas de presupuesto
      {
        name: 'budget_optimization',
        condition: (result) => 
          result.analysis.detectedCriteria.budget && 
          result.analysis.primaryCriteria.includes('budget'),
        adjustment: (weights) => ({
          ...weights,
          price: Math.min(weights.price + 0.15, 0.5), // Max 50%
          location: Math.max(weights.location - 0.1, 0.2),
          amenities: Math.max(weights.amenities - 0.05, 0.05),
        }),
        description: 'Optimización para consultas enfocadas en presupuesto',
      },

      // Regla 3: Balance para consultas de amenidades múltiples
      {
        name: 'multiple_amenities_balance',
        condition: (result) => 
          result.analysis.detectedCriteria.amenities && 
          result.analysis.criteriaCount >= 2,
        adjustment: (weights) => ({
          ...weights,
          amenities: Math.min(weights.amenities + 0.1, 0.5),
          property: Math.max(weights.property - 0.05, 0.15),
          price: Math.max(weights.price - 0.05, 0.05),
        }),
        description: 'Balance para consultas con múltiples amenidades',
      },

      // Regla 4: Optimización para especificaciones de habitaciones
      {
        name: 'room_specs_optimization',
        condition: (result) => 
          result.analysis.detectedCriteria.rooms && 
          result.analysis.confidence > 0.7,
        adjustment: (weights) => ({
          ...weights,
          property: Math.min(weights.property + 0.1, 0.6),
          amenities: Math.max(weights.amenities - 0.05, 0.1),
          price: Math.max(weights.price - 0.05, 0.05),
        }),
        description: 'Optimización para especificaciones de habitaciones',
      },

      // Regla 5: Ajuste para consultas de baja confianza
      {
        name: 'low_confidence_fallback',
        condition: (result) => result.analysis.confidence < 0.5,
        adjustment: (weights) => {
          // Usar pesos más balanceados para consultas ambiguas
          const balanced = SEMANTIC_WEIGHT_PROFILES.GENERAL_SEARCH.weights;
          return {
            location: (weights.location + balanced.location) / 2,
            property: (weights.property + balanced.property) / 2,
            amenities: (weights.amenities + balanced.amenities) / 2,
            characteristics: (weights.characteristics + balanced.characteristics) / 2,
            price: (weights.price + balanced.price) / 2,
          };
        },
        description: 'Fallback balanceado para consultas de baja confianza',
      },

      // Regla 6: Boost para intención de compra/renta clara
      {
        name: 'clear_intent_boost',
        condition: (result) => 
          result.analysis.detectedCriteria.status && 
          result.analysis.confidence > 0.8,
        adjustment: (weights) => ({
          ...weights,
          property: Math.min(weights.property + 0.05, 0.5),
          location: Math.min(weights.location + 0.05, 0.6),
          price: Math.max(weights.price - 0.1, 0.05),
        }),
        description: 'Boost para intención de compra/renta clara',
      },
    ];
  }

  /**
   * Normaliza los pesos para que sumen exactamente 1.0
   */
  private normalizeWeights(weights: SemanticWeights): SemanticWeights {
    const total = weights.location + weights.property + weights.amenities + weights.characteristics + weights.price;
    
    if (Math.abs(total - 1.0) < 0.001) {
      return weights; // Ya están normalizados
    }

    return {
      location: weights.location / total,
      property: weights.property / total,
      amenities: weights.amenities / total,
      characteristics: weights.characteristics / total,
      price: weights.price / total,
    };
  }

  /**
   * Valida que los pesos sean correctos
   */
  private validateWeights(weights: SemanticWeights): void {
    const total = weights.location + weights.property + weights.amenities + weights.characteristics + weights.price;
    const tolerance = 0.001;

    if (Math.abs(total - 1.0) > tolerance) {
      throw new Error(`Pesos no suman 1.0: ${total.toFixed(4)}`);
    }

    // Validar rangos individuales
    Object.entries(weights).forEach(([key, value]) => {
      if (value < 0 || value > 1) {
        throw new Error(`Peso ${key} fuera de rango: ${value}`);
      }
    });
  }

  /**
   * Crea resultado de fallback en caso de error
   */
  private createFallbackResult(query: string, processingTime: number): AdaptiveWeightsResult {
    const fallbackProfile = SEMANTIC_WEIGHT_PROFILES.GENERAL_SEARCH;
    
    return {
      originalQuery: query,
      detectedProfile: fallbackProfile,
      finalWeights: { ...fallbackProfile.weights },
      adaptiveAdjustments: false,
      profileDetection: {
        profile: fallbackProfile,
        analysis: {
          detectedCriteria: {
            location: false,
            propertyType: false,
            rooms: false,
            size: false,
            amenities: false,
            budget: false,
            status: false,
            age: false,
            features: false,
          },
          criteriaCount: 0,
          primaryCriteria: [],
          confidence: 0.1,
          reasoning: 'Fallback por error',
        },
        fallbackUsed: true,
        processingTime: 0,
      },
      optimizationApplied: ['fallback'],
      processingTime,
      confidence: 0.1,
    };
  }

  /**
   * Obtiene estadísticas del motor adaptativo
   */
  getEngineStats(): {
    totalRules: number;
    ruleNames: string[];
    profilesAvailable: number;
  } {
    return {
      totalRules: this.optimizationRules.length,
      ruleNames: this.optimizationRules.map(rule => rule.name),
      profilesAvailable: Object.keys(SEMANTIC_WEIGHT_PROFILES).length,
    };
  }
}
