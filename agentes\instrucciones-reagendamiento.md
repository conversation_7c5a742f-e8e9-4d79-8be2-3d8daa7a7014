# 🔄 INSTRUCCIONES PARA AGREGAR AL AGENTE N8N

## 📍 UBICACIÓN EN N8N:
**Nodo:** `AI Agent1`  
**Campo:** `System Message`

## 🔍 BUSCAR ESTA LÍNEA:
```
- **"rejected"** → "Su cita fue rechazada. ¿Le gustaría otra fecha?"
```

## ➕ AGREGAR DESPUÉS DE ESA LÍNEA:

```
### 🔄 FLUJO DE REAGENDAMIENTO DESPUÉS DE RECHAZO

**CUANDO UNA CITA FUE RECHAZADA Y USUARIO SOLICITA NUEVA FECHA:**

#### PROTOCOLO ESPECÍFICO:
1. **DETECTAR CONTEXTO:** Si hay cita rechazada en el historial
2. **MOSTRAR DISPONIBILIDAD:** Usar verificar_disponibilidad_propiedad
3. **USUARIO ELIGE HORARIO:** "viernes a las 3:15", "miércoles 2pm", etc.
4. **CREAR NUEVA CITA:** Usar crear_solicitud_cita con datos existentes

#### REGLAS CRÍTICAS PARA REAGENDAMIENTO:
- **NO repetir** que la cita fue rechazada si ya se comunicó
- **PROCEDER DIRECTAMENTE** a crear nueva cita cuando usuario elige horario
- **USAR DATOS EXISTENTES:** Nombre, email, teléfono del historial
- **MISMA PROPIEDAD:** Usar el mismo propertyId de la cita rechazada

#### FRASES QUE INDICAN REAGENDAMIENTO:
- "viernes a las 3:15 entonces"
- "miércoles 2pm está bien"
- "el lunes mejor"
- "esa fecha me conviene"
- "vienes a las [hora]"
- "ese horario está bien"
- "perfecto, el [día] a las [hora]"

#### FLUJO CORRECTO:
```
Cita rechazada → Usuario pide nueva fecha → Mostrar disponibilidad → 
Usuario elige horario → CREAR NUEVA CITA (NO repetir rechazo)
```

#### VALIDACIONES:
- Extraer fecha/hora de la respuesta del usuario
- Usar formato ISO: "2025-07-04T15:15:00-06:00"
- Confirmar con: "Perfecto, procedo a agendar su visita para [fecha/hora]"
- NUNCA volver a mencionar el rechazo anterior

#### EJEMPLOS DE RESPUESTAS CORRECTAS:
**Usuario:** "Viernes a las 3:15 entonces"
**Agente:** "Perfecto, procedo a agendar su visita para el viernes 4 de julio a las 3:15 PM"

**Usuario:** "Ese horario está bien"
**Agente:** "Excelente, confirmo su cita para [fecha/hora específica]"
```

---

## 📋 INSTRUCCIONES PASO A PASO:

### 1. **Abrir N8N**
- Ve a tu workflow del agente inmobiliario

### 2. **Localizar el nodo**
- Busca el nodo `AI Agent1`
- Haz clic para editarlo

### 3. **Editar System Message**
- Busca el campo `System Message`
- Usa Ctrl+F para buscar: `"rejected"** → "Su cita fue rechazada. ¿Le gustaría otra fecha?"`

### 4. **Agregar nueva sección**
- Después de esa línea, agrega todo el texto del bloque de arriba
- Asegúrate de mantener el formato y las comillas

### 5. **Guardar y activar**
- Guarda los cambios
- Activa el workflow

---

## 🎯 RESULTADO ESPERADO:

### **ANTES (Problema actual):**
```
Usuario: "Viernes a las 3:15 entonces"
Agente: "Su cita fue rechazada... ¿Le gustaría otra fecha?" ❌
```

### **DESPUÉS (Con el cambio):**
```
Usuario: "Viernes a las 3:15 entonces"
Agente: "Perfecto, procedo a agendar su visita para viernes 4 de julio a las 3:15 PM" ✅
```

---

## 🚨 NOTAS IMPORTANTES:

1. **Mantén el formato:** Asegúrate de que las comillas y saltos de línea estén correctos
2. **Ubicación exacta:** Debe ir después de la línea de "rejected" pero antes de la siguiente sección
3. **Prueba inmediatamente:** Después de guardar, prueba con una conversación de reagendamiento
4. **Backup:** Guarda una copia del workflow antes de hacer cambios

---

## 🔧 SOLUCIÓN AL PROBLEMA:

El agente actualmente tiene todas las herramientas correctas pero le falta **contexto específico** sobre cómo manejar el flujo después de mostrar horarios de reagendamiento.

**Con este cambio, el agente entenderá que cuando muestra horarios después de un rechazo y el usuario elige uno, debe proceder a crear la nueva cita directamente sin repetir el mensaje de rechazo.**
