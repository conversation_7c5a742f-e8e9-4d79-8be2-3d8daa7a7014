{"name": "Inmo Agent Learning System", "nodes": [{"parameters": {"rule": {"interval": [{"triggerAtHour": 10}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [0, 0], "id": "794d1146-6d06-4bbc-9051-2d2e70a273c6", "name": "Schedule Trigger"}, {"parameters": {"method": "POST", "url": "https://capable-cod-213.convex.site/getUnanalyzedConversations", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "x-api-key", "value": "dde2fae93649d4550684edd565a9515f"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"limit\": 50,\n  \"daysBack\": 1\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [220, 0], "id": "f7771a5e-a6df-485a-98e1-db5ecd5afbc1", "name": "Obtener Conversaciones"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "1088ea19-4a81-4e6f-b455-f1e1100dd6f4", "leftValue": 0, "rightValue": "={{ $json.data.totalConversations }}", "operator": {"type": "number", "operation": "lt"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [440, 0], "id": "5d26c652-c454-4464-8db9-7d399d871ded", "name": "If"}, {"parameters": {"jsCode": "// Obtener datos del nodo anterior\nvar inputData = $input.all()[0].json;\nvar conversations = inputData.data.conversations || [];\n\n// Verificar si hay conversaciones\nif (!conversations || conversations.length === 0) {\n  return {\n    prompt: \"No hay conversaciones para analizar\",\n    messageIds: [],\n    conversationsCount: 0,\n    totalMessages: 0\n  };\n}\n\n// Procesar conversaciones para análisis\nvar processedConversations = [];\nvar allMessageIds = [];\n\nfor (var i = 0; i < conversations.length; i++) {\n  var conversation = conversations[i];\n  var processedMessages = [];\n  \n  if (conversation.messages && Array.isArray(conversation.messages)) {\n    for (var j = 0; j < conversation.messages.length; j++) {\n      var message = conversation.messages[j];\n      allMessageIds.push(message.messageId);\n      \n      processedMessages.push({\n        timestamp: message.timestamp,\n        sender: message.messageType === 'user' ? 'cliente' : 'agente',\n        content: message.content,\n        messageType: message.messageType\n      });\n    }\n  }\n  \n  processedConversations.push({\n    conversationId: conversation.chatId,\n    clientPhone: conversation.chatId,\n    messages: processedMessages,\n    totalMessages: processedMessages.length\n  });\n}\n\nvar prompt = `Eres un experto en análisis de conversaciones de agentes inmobiliarios de IA.\n\nAnaliza las siguientes conversaciones de WhatsApp entre usuarios y nuestro agente inmobiliario de IA.\n\nCONVERSACIONES A ANALIZAR:\n${JSON.stringify(processedConversations, null, 2)}\n\nEvalúa la calidad general de las conversaciones y proporciona observaciones específicas sobre el desempeño del agente.\n\nProporciona un análisis en formato JSON con la siguiente estructura:\n\n{\n  \"qualityScore\": [número del 1-10],\n  \"observations\": \"Análisis detallado de la calidad de las conversaciones. Describe qué hizo bien el agente y qué problemas específicos identificaste. Sé específico con ejemplos de la conversación.\",\n  \"improvements\": \"Recomendaciones específicas para mejorar el desempeño del agente. Incluye acciones concretas que se pueden implementar.\",\n  \"analysisDate\": \"${new Date().toISOString().split('T')[0]}\"\n}\n\nEnfócate en:\n- Calidad y precisión de las respuestas del agente\n- Efectividad en la búsqueda y presentación de propiedades\n- Gestión adecuada de citas y seguimiento\n- Uso correcto de herramientas disponibles\n- Experiencia general del usuario\n- Detección de errores, repeticiones o inconsistencias\n\nSé específico y menciona ejemplos concretos de las conversaciones analizadas.\n\nResponde SOLO con el JSON, sin texto adicional.`;\n\nreturn {\n  prompt: prompt,\n  messageIds: allMessageIds,\n  conversationsCount: processedConversations.length,\n  totalMessages: allMessageIds.length\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [660, -100], "id": "041f9dbf-c9b6-4b7d-b565-fcf44192dffa", "name": "Code"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "GPT-4O"}, "messages": {"values": [{"content": "={{ $json.prompt }}"}]}, "options": {"maxTokens": 2000, "temperature": 0.3}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [880, -100], "id": "5a37f416-1ec5-448a-adf2-034996f13e35", "name": "OpenAI", "credentials": {"openAiApi": {"id": "KSAvlJBgncGdJAJg", "name": "OpenAi account"}}}, {"parameters": {"jsCode": "// Obtener el análisis de OpenAI\nvar openAIResponse = $input.all()[0].json;\nvar analysisContent = openAIResponse.message.content;\n\n// Parsear el JSON del análisis\nvar analysis;\ntry {\n  analysis = JSON.parse(analysisContent);\n} catch (error) {\n  console.log('Error parseando JSON:', error);\n  return {\n    error: 'No se pudo parsear el análisis de OpenAI',\n    rawContent: analysisContent\n  };\n}\n\n// Extraer consideraciones del campo improvements\nvar improvementsText = analysis.improvements || '';\n\nif (!improvementsText || improvementsText.trim().length === 0) {\n  return {\n    considerations: [],\n    message: 'No se encontraron mejoras en el análisis'\n  };\n}\n\n// Dividir las mejoras en consideraciones individuales\n// Buscar patrones como: \"1.\", \"2.\", \"-\", \"•\", o frases que empiecen con \"El agente\", \"Se recomienda\", etc.\nvar considerationPatterns = [\n  /\\d+\\.[^\\d]+/g,  // \"1. texto\", \"2. texto\"\n  /[-•][^-•]+/g,   // \"- texto\", \"• texto\"\n  /El agente[^.]+\\./g,  // \"El agente debería...\"\n  /Se recomienda[^.]+\\./g,  // \"Se recomienda...\"\n  /Debería[^.]+\\./g,  // \"Debería implementar...\"\n  /Es importante[^.]+\\./g,  // \"Es importante...\"\n  /Sería beneficioso[^.]+\\./g  // \"Sería beneficioso...\"\n];\n\nvar extractedConsiderations = [];\nvar processedText = improvementsText;\n\n// Intentar extraer usando patrones\nfor (var i = 0; i < considerationPatterns.length; i++) {\n  var matches = processedText.match(considerationPatterns[i]);\n  if (matches) {\n    for (var j = 0; j < matches.length; j++) {\n      var consideration = matches[j]\n        .replace(/^\\d+\\.\\s*/, '')  // Remover \"1. \"\n        .replace(/^[-•]\\s*/, '')   // Remover \"- \" o \"• \"\n        .trim();\n      \n      if (consideration.length > 20) {  // Solo consideraciones con contenido sustancial\n        extractedConsiderations.push({\n          consideration: consideration,\n          category: determineCategory(consideration),\n          priority: determinePriority(consideration, analysis.qualityScore),\n          sourceAnalysis: [`analysis_${new Date().toISOString().split('T')[0]}_${Date.now()}`]\n        });\n      }\n    }\n  }\n}\n\n// Si no se encontraron consideraciones con patrones, dividir por oraciones\nif (extractedConsiderations.length === 0) {\n  var sentences = improvementsText.split(/[.!?]+/).filter(s => s.trim().length > 20);\n  \n  for (var k = 0; k < sentences.length && k < 5; k++) {  // Máximo 5 consideraciones\n    var sentence = sentences[k].trim();\n    if (sentence.length > 0) {\n      extractedConsiderations.push({\n        consideration: sentence,\n        category: determineCategory(sentence),\n        priority: determinePriority(sentence, analysis.qualityScore),\n        sourceAnalysis: [`analysis_${new Date().toISOString().split('T')[0]}_${Date.now()}`]\n      });\n    }\n  }\n}\n\n// Función para determinar categoría basada en palabras clave\nfunction determineCategory(text) {\n  var lowerText = text.toLowerCase();\n  \n  if (lowerText.includes('respuesta') || lowerText.includes('comunicación') || lowerText.includes('saludo') || lowerText.includes('pregunta')) {\n    return 'communication';\n  }\n  if (lowerText.includes('búsqueda') || lowerText.includes('propiedad') || lowerText.includes('buscar') || lowerText.includes('filtro')) {\n    return 'search';\n  }\n  if (lowerText.includes('cita') || lowerText.includes('horario') || lowerText.includes('agenda') || lowerText.includes('disponibilidad')) {\n    return 'appointments';\n  }\n  if (lowerText.includes('datos') || lowerText.includes('información') || lowerText.includes('verificación') || lowerText.includes('contacto')) {\n    return 'data_management';\n  }\n  \n  return 'general';\n}\n\n// Función para determinar prioridad basada en palabras clave y score de calidad\nfunction determinePriority(text, qualityScore) {\n  var lowerText = text.toLowerCase();\n  \n  // Prioridad alta para problemas críticos\n  if (lowerText.includes('error') || lowerText.includes('fallo') || lowerText.includes('problema') || \n      lowerText.includes('crítico') || lowerText.includes('urgente') || qualityScore <= 4) {\n    return 'high';\n  }\n  \n  // Prioridad media para mejoras importantes\n  if (lowerText.includes('debería') || lowerText.includes('importante') || lowerText.includes('mejorar') ||\n      lowerText.includes('optimizar') || qualityScore <= 6) {\n    return 'medium';\n  }\n  \n  // Prioridad baja para sugerencias menores\n  return 'low';\n}\n\nreturn {\n  considerations: extractedConsiderations,\n  totalExtracted: extractedConsiderations.length,\n  originalAnalysis: analysis,\n  extractionMethod: extractedConsiderations.length > 0 ? 'pattern_based' : 'sentence_based'\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1100, -100], "id": "extract-considerations-code", "name": "Extract Considerations"}, {"parameters": {"method": "POST", "url": "https://capable-cod-213.convex.site/saveKnowledgeBaseConsiderations", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "x-api-key", "value": "dde2fae93649d4550684edd565a9515f"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"considerations\": {{ $json.considerations }},\n  \"skipDuplicateCheck\": false\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1320, -100], "id": "save-to-knowledge-base", "name": "Save to Knowledge Base"}], "pinData": {}, "connections": {"Schedule Trigger": {"main": [[{"node": "Obtener Conversaciones", "type": "main", "index": 0}]]}, "Obtener Conversaciones": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "Extract Considerations", "type": "main", "index": 0}]]}, "Extract Considerations": {"main": [[{"node": "Save to Knowledge Base", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "1d41a108-4e06-4ea8-88ee-ccb8272833df", "meta": {"templateCredsSetupCompleted": true, "instanceId": "07b1f54ce63233c892bab2194482dcfcc49ccf3275504c189d498853ae39ba61"}, "id": "qmA3wlWpQ6MXMQJj", "tags": []}