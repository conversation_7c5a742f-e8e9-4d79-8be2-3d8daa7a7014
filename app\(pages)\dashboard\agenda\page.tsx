"use client";

import React, { useState } from 'react';
import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { useUser } from "@clerk/nextjs";
import { Calendar, Clock, Users, Plus, Settings, Bell, Eye, X } from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { format, startOfMonth, endOfMonth, isToday, isTomorrow, isPast } from 'date-fns';
import { es } from 'date-fns/locale';
import { CreditsDisplay } from "@/components/ui/credits-display";
import { AppointmentCard } from './components/AppointmentCard';
import { CalendarView } from './components/CalendarView';
import { RequestsView } from './components/RequestsView';
import { RejectedRequestsView } from './components/RejectedRequestsView';
import { AvailabilitySettings } from './components/AvailabilitySettings';
import { CreateAppointmentModal } from './components/CreateAppointmentModal';
import { ManagedList } from "@/components/ui/list-controls";

export default function AgendaPage() {
  const { user } = useUser();
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [activeTab, setActiveTab] = useState("agenda");

  // Queries
  const appointments = useQuery(api.appointments.getUserAppointments, 
    user ? { userId: user.id } : "skip"
  );

  const pendingRequests = useQuery(api.appointments.getPendingRequests,
    user ? { hostId: user.id } : "skip"
  );

  const rejectedRequests = useQuery(api.appointments.getRejectedRequests,
    user ? { hostId: user.id } : "skip"
  );

  const stats = useQuery(api.appointments.getAgendaStats,
    user ? { userId: user.id } : "skip"
  );

  // Filtrar citas por fecha
  const todayAppointments = appointments?.filter((apt: any) =>
    isToday(new Date(apt.startTime))
  ) || [];

  const upcomingAppointments = appointments?.filter((apt: any) =>
    !isPast(new Date(apt.startTime)) && apt.status !== 'cancelled'
  ) || [];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-green-100 text-green-800';
      case 'scheduled': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-gray-100 text-gray-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-yellow-100 text-yellow-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'property_viewing': return '🏠';
      case 'consultation': return '💼';
      case 'negotiation': return '🤝';
      case 'document_signing': return '📄';
      default: return '📅';
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'property_viewing': return 'Visita de Propiedad';
      case 'consultation': return 'Consulta';
      case 'negotiation': return 'Negociación';
      case 'document_signing': return 'Firma de Documentos';
      case 'other': return 'Otro';
      default: return type;
    }
  };

  if (!user) {
    return <div className="flex items-center justify-center h-64">Cargando...</div>;
  }

  return (
    <div className="flex flex-col gap-6 p-4 md:p-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Mi Agenda</h1>
          <p className="text-gray-600">Gestiona tus citas y disponibilidad</p>
        </div>
        <div className="flex items-center gap-4">
          <CreditsDisplay variant="compact" />
          <Button 
            onClick={() => setShowCreateModal(true)}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Plus className="w-4 h-4 mr-2" />
            Nueva Cita
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Próximas Citas</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {stats.upcomingAppointments}
                  </p>
                </div>
                <Calendar className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Completadas</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {stats.completedAppointments}
                  </p>
                </div>
                <Clock className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Solicitudes</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {stats.pendingRequests}
                  </p>
                </div>
                <Bell className="w-8 h-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Rechazadas</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {rejectedRequests?.length || 0}
                  </p>
                </div>
                <X className="w-8 h-8 text-red-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Este Mes</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {stats.totalAppointments}
                  </p>
                </div>
                <Users className="w-8 h-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="agenda">Agenda</TabsTrigger>
          <TabsTrigger value="calendar">Calendario</TabsTrigger>
          <TabsTrigger value="requests" className="relative">
            Pendientes
            {pendingRequests && pendingRequests.length > 0 && (
              <Badge className="ml-2 bg-red-500 text-white text-xs px-1.5 py-0.5">
                {pendingRequests.length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="rejected" className="relative">
            Rechazadas
            {rejectedRequests && rejectedRequests.length > 0 && (
              <Badge className="ml-2 bg-gray-500 text-white text-xs px-1.5 py-0.5">
                {rejectedRequests.length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="settings">Configuración</TabsTrigger>
        </TabsList>

        <TabsContent value="agenda">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Citas de Hoy */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="w-5 h-5" />
                  Citas de Hoy ({format(new Date(), 'dd MMMM', { locale: es })})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {todayAppointments.length === 0 ? (
                  <div className="text-center py-8">
                    <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">No tienes citas programadas para hoy</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {todayAppointments.map((appointment: any) => (
                      <AppointmentCard
                        key={appointment._id}
                        appointment={appointment}
                        onUpdate={() => {}} // Refetch se hará automáticamente
                        isCollapsible={true}
                        defaultExpanded={false}
                      />
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Próximas Citas */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="w-5 h-5" />
                  Próximas Citas
                </CardTitle>
              </CardHeader>
              <CardContent>
                {upcomingAppointments.length === 0 ? (
                  <div className="text-center py-4">
                    <p className="text-gray-500 text-sm">Sin citas próximas</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {upcomingAppointments.slice(0, 5).map((appointment: any) => (
                      <div
                        key={appointment._id}
                        className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg"
                      >
                        <div className="text-xl">
                          {getTypeIcon(appointment.type)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="font-medium text-sm truncate">
                            {getTypeText(appointment.type)}
                          </p>
                          <p className="text-xs text-gray-600">
                            {appointment.guestName}
                          </p>
                          <p className="text-xs text-gray-500">
                            {format(new Date(appointment.startTime), 'dd MMM, HH:mm', { locale: es })}
                          </p>
                        </div>
                        <Badge className={getStatusColor(appointment.status)}>
                          {appointment.status}
                        </Badge>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Lista completa de citas */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Todas las Citas</CardTitle>
            </CardHeader>
            <CardContent>
              {appointments && appointments.length === 0 ? (
                <div className="text-center py-8">
                  <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No tienes citas programadas</p>
                  <Button 
                    onClick={() => setShowCreateModal(true)}
                    className="mt-4"
                    variant="outline"
                  >
                    Crear Primera Cita
                  </Button>
                </div>
              ) : (
                <ManagedList
                  items={appointments || []}
                  getItemId={(appointment) => appointment._id}
                  defaultExpanded={false}
                  showControls={true}
                  controlsVariant="compact"
                  renderItem={(appointment, isExpanded, onToggle) => (
                    <AppointmentCard
                      key={appointment._id}
                      appointment={appointment}
                      showProperty={true}
                      onUpdate={() => {}}
                      isCollapsible={true}
                      defaultExpanded={isExpanded}
                    />
                  )}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="calendar" className="mt-6">
          <CalendarView 
            appointments={appointments || []}
            onDateSelect={setSelectedDate}
            selectedDate={selectedDate}
          />
        </TabsContent>

        <TabsContent value="requests" className="mt-6">
          <RequestsView requests={pendingRequests || []} />
        </TabsContent>

        <TabsContent value="rejected" className="mt-6">
          <RejectedRequestsView requests={rejectedRequests || []} />
        </TabsContent>

        <TabsContent value="settings" className="mt-6">
          <AvailabilitySettings userId={user.id} />
        </TabsContent>
      </Tabs>

      {/* Modal para crear cita */}
      {showCreateModal && (
        <CreateAppointmentModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          hostId={user.id}
        />
      )}
    </div>
  );
} 