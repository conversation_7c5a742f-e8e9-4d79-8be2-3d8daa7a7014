"use client";

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  MessageCircle, 
  Calendar, 
  Phone, 
  Mail, 
  User, 
  Building2,
  ExternalLink
} from 'lucide-react';
import { toast } from 'sonner';
import Link from 'next/link';

interface ContactInfo {
  agent?: {
    name: string;
    phone?: string;
    email?: string;
    company?: string;
    license?: string;
  } | null;
  owner?: {
    name: string;
    phone?: string;
    email?: string;
    company?: string;
  } | null;
  primaryContact?: {
    name: string;
    phone?: string;
    email?: string;
    company?: string;
    role: 'agent' | 'owner';
  } | null;
}

interface ContactActionsProps {
  propertyId: string;
  propertyTitle: string;
  contactInfo?: ContactInfo;
  compact?: boolean;
}

export function ContactActions({ 
  propertyId, 
  propertyTitle, 
  contactInfo, 
  compact = false 
}: ContactActionsProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  const primaryContact = contactInfo?.primaryContact;
  
  if (!primaryContact) {
    return (
      <div className="text-sm text-gray-500 italic">
        Información de contacto no disponible
      </div>
    );
  }

  const handlePhoneClick = (phone: string) => {
    window.open(`tel:${phone}`, '_self');
    toast.success(`Llamando a ${primaryContact.name}...`);
  };

  const handleEmailClick = (email: string) => {
    window.open(`mailto:${email}?subject=Consulta sobre ${propertyTitle}`, '_self');
    toast.success(`Abriendo cliente de email...`);
  };

  if (compact) {
    return (
      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border">
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              {primaryContact.role === 'agent' ? (
                <Building2 className="h-4 w-4 text-blue-600" />
              ) : (
                <User className="h-4 w-4 text-blue-600" />
              )}
            </div>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-900">
              {primaryContact.name}
            </p>
            <p className="text-xs text-gray-500">
              {primaryContact.role === 'agent' ? 'Agente' : 'Propietario'}
              {primaryContact.company && ` • ${primaryContact.company}`}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {primaryContact.phone && (
            <Button
              size="sm"
              variant="outline"
              onClick={() => handlePhoneClick(primaryContact.phone!)}
              className="h-8 w-8 p-0"
            >
              <Phone className="h-3 w-3" />
            </Button>
          )}
          
          <Link href={`/properties/${propertyId}`}>
            <Button size="sm" variant="outline" className="h-8 px-3">
              <MessageCircle className="h-3 w-3 mr-1" />
              Contactar
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="border rounded-lg p-4 bg-white shadow-sm">
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
              {primaryContact.role === 'agent' ? (
                <Building2 className="h-5 w-5 text-blue-600" />
              ) : (
                <User className="h-5 w-5 text-blue-600" />
              )}
            </div>
          </div>
          <div>
            <h4 className="font-semibold text-gray-900">
              {primaryContact.name}
            </h4>
            <div className="flex items-center space-x-2">
              <Badge variant={primaryContact.role === 'agent' ? 'default' : 'secondary'}>
                {primaryContact.role === 'agent' ? 'Agente Inmobiliario' : 'Propietario'}
              </Badge>
            </div>
            {primaryContact.company && (
              <p className="text-sm text-gray-600 mt-1">
                {primaryContact.company}
              </p>
            )}
          </div>
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsExpanded(!isExpanded)}
          className="text-blue-600 hover:text-blue-700"
        >
          {isExpanded ? 'Menos info' : 'Más info'}
        </Button>
      </div>

      {/* Información adicional expandible */}
      {isExpanded && (
        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
          <div className="space-y-2 text-sm">
            {primaryContact.phone && (
              <div className="flex items-center space-x-2">
                <Phone className="h-4 w-4 text-gray-400" />
                <span className="text-gray-600">Teléfono:</span>
                <span className="font-medium">{primaryContact.phone}</span>
              </div>
            )}
            {primaryContact.email && (
              <div className="flex items-center space-x-2">
                <Mail className="h-4 w-4 text-gray-400" />
                <span className="text-gray-600">Email:</span>
                <span className="font-medium">{primaryContact.email}</span>
              </div>
            )}
            {contactInfo?.agent?.license && (
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className="text-xs">
                  Licencia: {contactInfo.agent.license}
                </Badge>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Botones de acción */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
        {primaryContact.phone && (
          <Button
            variant="outline"
            onClick={() => handlePhoneClick(primaryContact.phone!)}
            className="flex items-center justify-center space-x-2"
          >
            <Phone className="h-4 w-4" />
            <span>Llamar</span>
          </Button>
        )}
        
        {primaryContact.email && (
          <Button
            variant="outline"
            onClick={() => handleEmailClick(primaryContact.email!)}
            className="flex items-center justify-center space-x-2"
          >
            <Mail className="h-4 w-4" />
            <span>Email</span>
          </Button>
        )}
        
        <Link href={`/properties/${propertyId}`}>
          <Button className="w-full flex items-center justify-center space-x-2">
            <MessageCircle className="h-4 w-4" />
            <span>Ver propiedad</span>
            <ExternalLink className="h-3 w-3" />
          </Button>
        </Link>
      </div>
      
      <div className="mt-3 pt-3 border-t">
        <p className="text-xs text-gray-500 text-center">
          💡 Desde la página de la propiedad puedes enviar mensajes, solicitar visitas y hacer ofertas
        </p>
      </div>
    </div>
  );
}
