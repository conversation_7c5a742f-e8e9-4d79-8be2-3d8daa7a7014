{"buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm install --legacy-peer-deps", "framework": "nextjs", "regions": ["iad1"], "functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}], "rewrites": [{"source": "/api/search", "destination": "/api/v1/search"}]}