/**
 * Guatemala Edge Cases Handler - Manejador de Casos Edge Guatemaltecos
 * 
 * Sistema especializado para manejar casos específicos del mercado
 * inmobiliario guatemalteco que requieren tratamiento especial.
 * 
 * Día 6 - Análisis de Contexto Avanzado
 */

// Tipos para casos edge guatemaltecos
export interface GuatemalaEdgeCase {
  type: 'similar_zones' | 'local_terms' | 'price_ranges' | 'regional_types' | 'cultural_context';
  originalInput: string;
  detectedIssue: string;
  suggestedCorrection: string;
  confidence: number;
  alternatives: string[];
}

export interface EdgeCaseResult {
  hasEdgeCase: boolean;
  edgeCases: GuatemalaEdgeCase[];
  correctedQuery: string;
  warnings: string[];
  suggestions: string[];
}

/**
 * Manejador de casos edge específicos de Guatemala
 */
export class GuatemalaEdgeCasesHandler {
  
  // Zonas con nombres similares que causan confusión
  private readonly SIMILAR_ZONES = {
    'zona 1': {
      alternatives: ['centro histórico', 'centro'],
      warnings: ['Zona 1 es el centro histórico, puede ser muy comercial para vivienda'],
      suggestions: ['Considera Zona 2 o Zona 10 para residencial']
    },
    'zona 4': {
      alternatives: ['centro comercial', 'centro'],
      warnings: ['Zona 4 es muy comercial, limitadas opciones residenciales'],
      suggestions: ['Para residencial cerca del centro, considera Zona 9 o Zona 10']
    },
    'zona 7': {
      alternatives: ['kaminal juyú', 'centro américa'],
      warnings: ['Zona 7 tiene áreas muy variadas en precio y seguridad'],
      suggestions: ['Especifica el sector: Kaminal Juyú (alto) o Centro América (medio)']
    },
    'zona 12': {
      alternatives: ['zona 12', 'sur'],
      warnings: ['Zona 12 es muy extensa con grandes diferencias de precio'],
      suggestions: ['Especifica sector: 4 Grados Norte, Trébol, o Centroamérica']
    }
  };

  // Términos locales guatemaltecos y sus equivalencias
  private readonly LOCAL_TERMS = {
    // Abreviaciones comunes
    'apto': 'apartamento',
    'depto': 'departamento',
    'dpto': 'departamento',
    'hab': 'habitaciones',
    'habs': 'habitaciones',
    'rec': 'recámaras',
    'recs': 'recámaras',
    'baño': 'baños',
    'mt2': 'metros cuadrados',
    'm2': 'metros cuadrados',
    'mts2': 'metros cuadrados',
    'parqueo': 'estacionamiento',
    'cochera': 'garaje',
    'bodega': 'cuarto de servicio',
    
    // Zonas abreviadas
    'z1': 'zona 1', 'z2': 'zona 2', 'z3': 'zona 3', 'z4': 'zona 4', 'z5': 'zona 5',
    'z6': 'zona 6', 'z7': 'zona 7', 'z8': 'zona 8', 'z9': 'zona 9', 'z10': 'zona 10',
    'z11': 'zona 11', 'z12': 'zona 12', 'z13': 'zona 13', 'z14': 'zona 14', 'z15': 'zona 15',
    'z16': 'zona 16', 'z17': 'zona 17', 'z18': 'zona 18', 'z19': 'zona 19', 'z20': 'zona 20',
    'z21': 'zona 21', 'z22': 'zona 22', 'z23': 'zona 23', 'z24': 'zona 24', 'z25': 'zona 25',
    
    // Áreas específicas
    'antigua': 'antigua guatemala',
    'carretera salvador': 'carretera a el salvador',
    'carretera pacifico': 'carretera al pacífico',
    'ces': 'carretera a el salvador',
    'fraija': 'fraijanes',
    'cata': 'santa catarina pinula',
    'san jose': 'san josé pinula',
    
    // Términos de precio
    'quetzales': 'Q',
    'gtq': 'Q',
    'dolares': 'USD',
    'usd': 'USD',
    'mil': '000',
    'millon': '000000',
    'millones': '000000',
    
    // Términos de amenidades guatemaltecos
    'rancho': 'área de parrilla',
    'lavanderia': 'área de lavado',
    'cuarto servicio': 'cuarto de empleada',
    'empleada': 'cuarto de servicio',
    'cisterna': 'tanque de agua',
    'tinaco': 'tanque elevado',
    'garita': 'caseta de seguridad'
  };

  // Rangos de precio típicos por zona (en USD)
  private readonly PRICE_RANGES_BY_ZONE = {
    'zona 10': { min: 800000, max: 5000000, type: 'luxury', note: 'Zona premium, precios altos' },
    'zona 14': { min: 600000, max: 3000000, type: 'high', note: 'Zona comercial y residencial exclusiva' },
    'zona 15': { min: 400000, max: 2000000, type: 'high', note: 'Zona residencial consolidada' },
    'zona 16': { min: 300000, max: 1500000, type: 'medium-high', note: 'Zona en crecimiento' },
    'zona 13': { min: 250000, max: 1200000, type: 'medium', note: 'Zona mixta, variedad de precios' },
    'zona 9': { min: 200000, max: 1000000, type: 'medium', note: 'Zona central, precios moderados' },
    'zona 11': { min: 150000, max: 800000, type: 'medium', note: 'Zona residencial establecida' },
    'zona 12': { min: 100000, max: 600000, type: 'medium-low', note: 'Zona extensa, precios variables' },
    'zona 7': { min: 80000, max: 500000, type: 'low-medium', note: 'Zona popular, precios accesibles' },
    'zona 18': { min: 60000, max: 400000, type: 'low', note: 'Zona en desarrollo' },
    'antigua guatemala': { min: 300000, max: 2000000, type: 'tourist', note: 'Precios premium por turismo' },
    'carretera a el salvador': { min: 200000, max: 1500000, type: 'suburban', note: 'Zona suburbana, casas grandes' },
    'fraijanes': { min: 250000, max: 1200000, type: 'suburban', note: 'Zona residencial, clima fresco' },
    'santa catarina pinula': { min: 300000, max: 1800000, type: 'suburban', note: 'Zona exclusiva, casas grandes' }
  };

  // Tipos de propiedad regionales específicos
  private readonly REGIONAL_PROPERTY_TYPES = {
    'antigua guatemala': {
      typical: ['casa colonial', 'casa de patio', 'apartamento en casa antigua'],
      avoid: ['edificios modernos', 'torres'],
      note: 'Arquitectura colonial protegida por patrimonio'
    },
    'zona 10': {
      typical: ['apartamento de lujo', 'penthouse', 'oficina ejecutiva'],
      avoid: ['casas individuales'],
      note: 'Zona principalmente de apartamentos y oficinas'
    },
    'carretera a el salvador': {
      typical: ['casa con jardín', 'casa en condominio', 'casa de campo'],
      avoid: ['apartamentos pequeños'],
      note: 'Zona suburbana, predominan casas grandes'
    },
    'fraijanes': {
      typical: ['casa de montaña', 'casa con chimenea', 'casa de fin de semana'],
      avoid: ['apartamentos'],
      note: 'Zona de clima fresco, casas para descanso'
    }
  };

  /**
   * Analiza y maneja casos edge guatemaltecos
   */
  analyzeGuatemalaEdgeCases(query: string): EdgeCaseResult {
    console.log(`🇬🇹 Analizando casos edge guatemaltecos: "${query}"`);

    const edgeCases: GuatemalaEdgeCase[] = [];
    let correctedQuery = query;
    const warnings: string[] = [];
    const suggestions: string[] = [];

    // 1. Detectar y corregir términos locales
    const termCorrections = this.detectLocalTerms(query);
    if (termCorrections.length > 0) {
      edgeCases.push(...termCorrections);
      correctedQuery = this.applyTermCorrections(correctedQuery, termCorrections);
    }

    // 2. Detectar zonas similares problemáticas
    const zoneIssues = this.detectSimilarZoneIssues(correctedQuery);
    if (zoneIssues.length > 0) {
      edgeCases.push(...zoneIssues);
      warnings.push(...this.getZoneWarnings(correctedQuery));
      suggestions.push(...this.getZoneSuggestions(correctedQuery));
    }

    // 3. Validar rangos de precio por zona
    const priceIssues = this.detectPriceRangeIssues(correctedQuery);
    if (priceIssues.length > 0) {
      edgeCases.push(...priceIssues);
      warnings.push(...this.getPriceWarnings(correctedQuery));
    }

    // 4. Detectar tipos de propiedad regionales
    const typeIssues = this.detectRegionalTypeIssues(correctedQuery);
    if (typeIssues.length > 0) {
      edgeCases.push(...typeIssues);
      suggestions.push(...this.getTypeSuggestions(correctedQuery));
    }

    const result: EdgeCaseResult = {
      hasEdgeCase: edgeCases.length > 0,
      edgeCases,
      correctedQuery,
      warnings,
      suggestions,
    };

    if (result.hasEdgeCase) {
      console.log(`⚠️  ${edgeCases.length} casos edge detectados`);
    }

    return result;
  }

  /**
   * Detecta términos locales que necesitan corrección
   */
  private detectLocalTerms(query: string): GuatemalaEdgeCase[] {
    const cases: GuatemalaEdgeCase[] = [];
    const queryLower = query.toLowerCase();

    for (const [localTerm, standardTerm] of Object.entries(this.LOCAL_TERMS)) {
      const regex = new RegExp(`\\b${localTerm}\\b`, 'gi');
      if (regex.test(queryLower)) {
        cases.push({
          type: 'local_terms',
          originalInput: localTerm,
          detectedIssue: `Término local "${localTerm}" detectado`,
          suggestedCorrection: standardTerm,
          confidence: 0.95,
          alternatives: [standardTerm],
        });
      }
    }

    return cases;
  }

  /**
   * Detecta problemas con zonas similares
   */
  private detectSimilarZoneIssues(query: string): GuatemalaEdgeCase[] {
    const cases: GuatemalaEdgeCase[] = [];
    const queryLower = query.toLowerCase();

    for (const [zone, info] of Object.entries(this.SIMILAR_ZONES)) {
      if (queryLower.includes(zone)) {
        cases.push({
          type: 'similar_zones',
          originalInput: zone,
          detectedIssue: `Zona "${zone}" puede ser ambigua o problemática`,
          suggestedCorrection: `Especificar sector dentro de ${zone}`,
          confidence: 0.8,
          alternatives: info.alternatives,
        });
      }
    }

    return cases;
  }

  /**
   * Detecta problemas de rango de precio por zona
   */
  private detectPriceRangeIssues(query: string): GuatemalaEdgeCase[] {
    const cases: GuatemalaEdgeCase[] = [];
    const queryLower = query.toLowerCase();

    // Extraer precio mencionado
    const priceMatch = queryLower.match(/(\d+)\s*(k|mil|millon|millones|q|usd)/);
    if (!priceMatch) return cases;

    let price = parseInt(priceMatch[1]);
    const unit = priceMatch[2];

    // Convertir a USD
    if (unit === 'k' || unit === 'mil') price *= 1000;
    if (unit === 'millon' || unit === 'millones') price *= 1000000;
    if (unit === 'q') price = price / 7.8; // Conversión aproximada GTQ a USD

    // Verificar contra rangos por zona
    for (const [zone, range] of Object.entries(this.PRICE_RANGES_BY_ZONE)) {
      if (queryLower.includes(zone)) {
        if (price < range.min || price > range.max) {
          cases.push({
            type: 'price_ranges',
            originalInput: `${price} USD en ${zone}`,
            detectedIssue: `Precio fuera del rango típico para ${zone}`,
            suggestedCorrection: `Rango típico: $${range.min.toLocaleString()} - $${range.max.toLocaleString()}`,
            confidence: 0.7,
            alternatives: [`Ajustar precio al rango $${range.min.toLocaleString()}-$${range.max.toLocaleString()}`],
          });
        }
      }
    }

    return cases;
  }

  /**
   * Detecta problemas de tipos regionales
   */
  private detectRegionalTypeIssues(query: string): GuatemalaEdgeCase[] {
    const cases: GuatemalaEdgeCase[] = [];
    const queryLower = query.toLowerCase();

    for (const [area, info] of Object.entries(this.REGIONAL_PROPERTY_TYPES)) {
      if (queryLower.includes(area)) {
        // Verificar si se menciona un tipo que se debe evitar
        for (const avoidType of info.avoid) {
          if (queryLower.includes(avoidType.toLowerCase())) {
            cases.push({
              type: 'regional_types',
              originalInput: `${avoidType} en ${area}`,
              detectedIssue: `Tipo "${avoidType}" poco común en ${area}`,
              suggestedCorrection: `Considerar: ${info.typical.join(', ')}`,
              confidence: 0.6,
              alternatives: info.typical,
            });
          }
        }
      }
    }

    return cases;
  }

  /**
   * Aplica correcciones de términos
   */
  private applyTermCorrections(query: string, corrections: GuatemalaEdgeCase[]): string {
    let corrected = query;

    for (const correction of corrections) {
      if (correction.type === 'local_terms') {
        const regex = new RegExp(`\\b${correction.originalInput}\\b`, 'gi');
        corrected = corrected.replace(regex, correction.suggestedCorrection);
      }
    }

    return corrected;
  }

  /**
   * Obtiene advertencias de zona
   */
  private getZoneWarnings(query: string): string[] {
    const warnings: string[] = [];
    const queryLower = query.toLowerCase();

    for (const [zone, info] of Object.entries(this.SIMILAR_ZONES)) {
      if (queryLower.includes(zone)) {
        warnings.push(...info.warnings);
      }
    }

    return warnings;
  }

  /**
   * Obtiene sugerencias de zona
   */
  private getZoneSuggestions(query: string): string[] {
    const suggestions: string[] = [];
    const queryLower = query.toLowerCase();

    for (const [zone, info] of Object.entries(this.SIMILAR_ZONES)) {
      if (queryLower.includes(zone)) {
        suggestions.push(...info.suggestions);
      }
    }

    return suggestions;
  }

  /**
   * Obtiene advertencias de precio
   */
  private getPriceWarnings(query: string): string[] {
    const warnings: string[] = [];
    const queryLower = query.toLowerCase();

    for (const [zone, range] of Object.entries(this.PRICE_RANGES_BY_ZONE)) {
      if (queryLower.includes(zone)) {
        warnings.push(`${zone}: ${range.note}`);
      }
    }

    return warnings;
  }

  /**
   * Obtiene sugerencias de tipo
   */
  private getTypeSuggestions(query: string): string[] {
    const suggestions: string[] = [];
    const queryLower = query.toLowerCase();

    for (const [area, info] of Object.entries(this.REGIONAL_PROPERTY_TYPES)) {
      if (queryLower.includes(area)) {
        suggestions.push(`En ${area}: ${info.note}`);
        suggestions.push(`Tipos típicos: ${info.typical.join(', ')}`);
      }
    }

    return suggestions;
  }

  /**
   * Obtiene información de zona específica
   */
  getZoneInfo(zone: string): {
    priceRange?: { min: number; max: number; type: string; note: string };
    propertyTypes?: { typical: string[]; avoid: string[]; note: string };
    warnings?: string[];
    suggestions?: string[];
  } {
    const zoneLower = zone.toLowerCase();

    return {
      priceRange: this.PRICE_RANGES_BY_ZONE[zoneLower as keyof typeof this.PRICE_RANGES_BY_ZONE],
      propertyTypes: this.REGIONAL_PROPERTY_TYPES[zoneLower as keyof typeof this.REGIONAL_PROPERTY_TYPES],
      warnings: this.SIMILAR_ZONES[zoneLower as keyof typeof this.SIMILAR_ZONES]?.warnings,
      suggestions: this.SIMILAR_ZONES[zoneLower as keyof typeof this.SIMILAR_ZONES]?.suggestions,
    };
  }

  /**
   * Normaliza términos guatemaltecos
   */
  normalizeGuatemalaTerms(text: string): string {
    let normalized = text;

    for (const [localTerm, standardTerm] of Object.entries(this.LOCAL_TERMS)) {
      const regex = new RegExp(`\\b${localTerm}\\b`, 'gi');
      normalized = normalized.replace(regex, standardTerm);
    }

    return normalized;
  }
}
